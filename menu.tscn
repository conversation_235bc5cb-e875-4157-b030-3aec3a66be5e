[gd_scene load_steps=6 format=3 uid="uid://bm674f7vonbwv"]

[ext_resource type="FontFile" uid="uid://d2w1ayggejrfj" path="res://fonts/Next Bravo.ttf" id="1_lti8b"]
[ext_resource type="Script" uid="uid://bywd6buwu1ww2" path="res://menu.gd" id="1_mae5d"]
[ext_resource type="Texture2D" uid="uid://i4hhenti5o6w" path="res://11.jpg" id="1_ouk20"]

[sub_resource type="LabelSettings" id="LabelSettings_ouk20"]
font = ExtResource("1_lti8b")
font_size = 275
font_color = Color(0, 0, 0, 1)
outline_size = 30
shadow_size = 13
shadow_color = Color(0.0295066, 0.0295066, 0.0295066, 1)
shadow_offset = Vector2(20, 20)

[sub_resource type="Theme" id="Theme_ouk20"]

[node name="Menu" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_mae5d")

[node name="ColorRect" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_right = 17.0
offset_bottom = 10.0
grow_horizontal = 2
grow_vertical = 2
color = Color(1, 1, 1, 0.498039)

[node name="TextureRect" type="TextureRect" parent="ColorRect"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -1433.5
offset_top = -546.0
offset_right = 1384.5
offset_bottom = 1413.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("1_ouk20")
expand_mode = 1

[node name="ColorRect" type="ColorRect" parent="ColorRect/TextureRect"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(1, 1, 1, 0.121569)

[node name="Label" type="Label" parent="."]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -370.5
offset_top = 140.0
offset_right = 370.5
offset_bottom = 412.0
grow_horizontal = 2
text = "C J S C"
label_settings = SubResource("LabelSettings_ouk20")

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 12
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 371.0
offset_top = -236.0
offset_right = -290.0
offset_bottom = -123.0
grow_horizontal = 2
grow_vertical = 0
theme_override_constants/separation = 10

[node name="truthOrTRUTH" type="Button" parent="VBoxContainer"]
layout_mode = 2
focus_neighbor_top = NodePath("../untitled")
focus_neighbor_bottom = NodePath("../floridaMan")
theme = SubResource("Theme_ouk20")
text = "Truth or TRUTH"

[node name="untitled" type="Button" parent="VBoxContainer"]
layout_mode = 2
focus_neighbor_top = NodePath("../floridaMan")
focus_neighbor_bottom = NodePath("../truthOrTRUTH")
text = "Untitled Card Game"

[node name="floridaMan" type="Button" parent="VBoxContainer"]
layout_mode = 2
focus_neighbor_top = NodePath("../truthOrTRUTH")
focus_neighbor_bottom = NodePath("../multiplayerButton")
text = "Florida Man"

[node name="multiplayerButton" type="Button" parent="VBoxContainer"]
visible = false
layout_mode = 2
focus_neighbor_top = NodePath("../floridaMan")
focus_neighbor_bottom = NodePath("../untitled")
text = "Multiplayer"
