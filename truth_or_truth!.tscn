[gd_scene load_steps=7 format=3 uid="uid://cap0b17i5snh7"]

[ext_resource type="FontFile" uid="uid://dstx8k6ypf8mc" path="res://fonts/Geizer.otf" id="1_elckh"]
[ext_resource type="Script" uid="uid://cv5wajrl73w02" path="res://truth_or_truth!.gd" id="1_fbapv"]

[sub_resource type="CanvasTexture" id="CanvasTexture_fbapv"]

[sub_resource type="LabelSettings" id="LabelSettings_ya2sf"]
font = ExtResource("1_elckh")
font_size = 75
font_color = Color(0, 0, 0, 1)

[sub_resource type="LabelSettings" id="LabelSettings_vc5eb"]
font = ExtResource("1_elckh")
font_size = 50
font_color = Color(0, 0, 0, 1)

[sub_resource type="Theme" id="Theme_elckh"]
default_font = ExtResource("1_elckh")
default_font_size = 30

[node name="Truth or TRUTH!" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_fbapv")

[node name="TextureRect" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
texture = SubResource("CanvasTexture_fbapv")

[node name="FlowContainer" type="FlowContainer" parent="TextureRect"]
layout_mode = 1
offset_top = 264.0
offset_right = 547.0
offset_bottom = 377.0
rotation = -0.523599

[node name="Label" type="Label" parent="TextureRect/FlowContainer"]
layout_mode = 2
size_flags_horizontal = 6
size_flags_vertical = 0
text = "Truth Or TRUTH!"
label_settings = SubResource("LabelSettings_ya2sf")

[node name="Label2" type="Label" parent="TextureRect/FlowContainer"]
layout_mode = 2
text = "Hundreds of Invasive Questions"
label_settings = SubResource("LabelSettings_vc5eb")

[node name="ColorRect" type="ColorRect" parent="TextureRect"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -135.0
offset_top = -131.0
offset_right = 135.0
offset_bottom = 131.0
grow_horizontal = 2
grow_vertical = 2

[node name="CardLabel" type="Label" parent="TextureRect/ColorRect"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -233.0
offset_top = -114.5
offset_right = 233.0
offset_bottom = 114.5
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0.111197, 0.111197, 0.111197, 1)
theme_override_font_sizes/font_size = 40
text = "\"Click Draw to Pick a Card\""
horizontal_alignment = 1
autowrap_mode = 2
justification_flags = 161

[node name="HBoxContainer" type="HBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -226.0
offset_top = -85.0
offset_right = 226.0
offset_bottom = -45.0
grow_horizontal = 2
grow_vertical = 0
theme = SubResource("Theme_elckh")
theme_override_constants/separation = 100

[node name="options" type="Button" parent="HBoxContainer"]
layout_mode = 2
text = " Options "

[node name="next" type="Button" parent="HBoxContainer"]
layout_mode = 2
text = " Next Card "

[node name="exit" type="Button" parent="HBoxContainer"]
layout_mode = 2
text = " Main Menu "
