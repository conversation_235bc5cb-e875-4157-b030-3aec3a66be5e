import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';

// Define interfaces for our data structures
interface PromptObject {
  prompt: string;
  nsfw?: string | boolean;
  [key: string]: any; // Allow any other properties
}

interface PromptResponse {
  prompts?: PromptObject[];
  [key: string]: any; // Allow any other properties
}

@Injectable({
  providedIn: 'root'
})
export class PromptService {
  private promptsSubject = new BehaviorSubject<string[]>([]);
  public prompts$ = this.promptsSubject.asObservable();

  private cachedPrompts: string[] = [];
  private localStorageKey = 'cardGamePrompts';

  constructor(private http: HttpClient) {
    // Clear any existing cached data to ensure we get fresh data
    localStorage.removeItem(this.localStorageKey);

    // Try to load prompts from local storage first
    this.loadFromLocalStorage();
  }

  /**
   * Load prompts from local storage if available
   */
  private loadFromLocalStorage(): void {
    const storedPrompts = localStorage.getItem(this.localStorageKey);
    if (storedPrompts) {
      try {
        this.cachedPrompts = JSON.parse(storedPrompts);
        this.promptsSubject.next(this.cachedPrompts);
        console.log('Loaded prompts from local storage:', this.cachedPrompts.length);
      } catch (error) {
        console.error('Error parsing stored prompts:', error);
        this.loadFromAssets();
      }
    } else {
      this.loadFromAssets();
    }
  }

  /**
   * Load prompts from assets - DISABLED
   * We no longer load prompts from assets as the Godot client is responsible for prompts
   */
  private loadFromAssets(): void {
    console.log('DISABLED: Web client no longer loads prompts from assets');
    console.log('The Godot client is responsible for selecting and managing prompts');

    // Use an empty array for prompts
    const emptyPrompts: string[] = [];
    this.cachePrompts(emptyPrompts);
    this.promptsSubject.next(emptyPrompts);
  }

  /**
   * Cache prompts in local storage
   */
  private cachePrompts(prompts: string[]): void {
    this.cachedPrompts = prompts;
    try {
      localStorage.setItem(this.localStorageKey, JSON.stringify(prompts));
      console.log('Cached prompts in local storage');
    } catch (error) {
      console.error('Error caching prompts:', error);
    }
  }

  /**
   * Get a random prompt - DISABLED
   * We no longer provide random prompts as the Godot client is responsible for prompts
   */
  getRandomPrompt(): string {
    console.warn('DISABLED: Web client no longer provides random prompts');
    console.warn('The Godot client is responsible for selecting and managing prompts');
    return 'Prompts are managed by the Godot client';
  }
}
