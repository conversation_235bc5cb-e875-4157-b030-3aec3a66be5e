extends Control

# References
var multiplayer_manager
var firebase_manager

# UI references
@onready var code_label = $OuterPanel/MarginContainer/VBoxContainer/CodeLabel
@onready var qr_code_rect = $OuterPanel/MarginContainer/VBoxContainer/QRCodeRect
# Start button removed - game will be started from web client
# Players display removed - not needed

# Base URL for the web client
const BASE_URL = "http://example.com/home?room_code="

# Game state
# Players tracking removed - not needed

func _ready():
	# Get references
	multiplayer_manager = get_node_or_null("/root/MultiplayerManager")
	firebase_manager = get_node_or_null("/root/FirebaseManager")

	if not multiplayer_manager or not firebase_manager:
		push_error("MultiplayerManager or FirebaseManager not found")
		return

	# Connect signals - player joined signal removed
	# Start button removed - game will be started from web client

	# Create a new game
	multiplayer_manager.start_new_game()

func _on_firebase_game_created(_game_id, room_code):
	# Update the room code display
	code_label.text = room_code

	# Update the QR code with the room code URL
	if qr_code_rect:
		var qr_url = BASE_URL + room_code

		# Print the URL prominently for debugging
		print("\n\n========== QR CODE URL ==========")
		print("URL: ", qr_url)
		print("Room Code: ", room_code)
		print("===================================\n\n")

		# Explicitly set the mode to BYTE for handling URLs
		qr_code_rect.mode = 4  # BYTE mode (4 is the correct value according to the enum)

		# Enable UTF-8 encoding for the URL
		qr_code_rect.use_eci = true
		qr_code_rect.eci_value = 26  # UTF-8

		# Set the data directly - the QRCodeRect will handle the conversion
		qr_code_rect.data = qr_url
	else:
		print("[QR CODE DEBUG] QR code rect is null!")

# Function to set the room code and update display
func set_room_code(code: String):
	print("[ROOM CODE DEBUG] Setting room code to: ", code)
	
	# Update the room code label
	if code_label:
		code_label.text = code
	
	# Update the QR code with the room code URL
	if qr_code_rect:
		var qr_url = BASE_URL + code
		
		# Print the URL prominently for debugging
		print("\n\n========== QR CODE URL ==========")
		print("URL: ", qr_url)
		print("Room Code: ", code)
		print("===================================\n\n")

		# Explicitly set the mode to BYTE for handling URLs
		qr_code_rect.mode = 4  # BYTE mode (4 is the correct value according to the enum)

		# Enable UTF-8 encoding for the URL
		qr_code_rect.use_eci = true
		qr_code_rect.eci_value = 26  # UTF-8

		# Set the data directly - the QRCodeRect will handle the conversion
		qr_code_rect.data = qr_url

# Function to show ready status when enough players have joined
func show_ready_status(ready: bool):
	print("[ROOM CODE DEBUG] Setting ready status to: ", ready)
	
	# You could add visual feedback here to indicate that the game is ready to start
	if ready:
		# Make the code label green to indicate ready status
		if code_label:
			code_label.add_theme_color_override("font_color", Color(0, 0.7, 0, 1))  # Green color
			
			# Add "Ready!" text to the label
			var current_text = code_label.text
			if not "Ready!" in current_text:
				code_label.text = current_text + "\nReady!"
	else:
		# Reset to default color if not ready
		if code_label:
			code_label.remove_theme_color_override("font_color")
			
			# Remove "Ready!" text if present
			var current_text = code_label.text
			if "Ready!" in current_text:
				code_label.text = current_text.replace("\nReady!", "")
