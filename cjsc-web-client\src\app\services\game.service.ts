import { Injectable } from '@angular/core';
import { FirebaseService } from './firebase.service';
import { ResponseService } from './response.service';
// PromptService is no longer used as the Godot client handles prompts
// import { PromptService } from './prompt.service';
import { BehaviorSubject } from 'rxjs';
import { Router } from '@angular/router';

export interface GameState {
  status: string;
  players: string[];
  player_scores: { [key: string]: number };
  submitted_responses: { text: string; player_name: string }[];
  current_prompt: { prompt: string };
  current_round?: number;
  current_judge_index?: number;
  room_code?: string;
  winner?: string;
  winning_response?: string;
  game_winner?: string; // Overall game winner (player who reached 5 points)
  response_indices?: { [key: string]: number[] };
  all_responses?: string[];
  responses?: { response: string }[];
  // @deprecated - No longer used for storing player responses, web client manages responses locally
  player_response_texts?: { [key: string]: string[] };
  // @deprecated - No longer used for storing player responses, web client manages responses locally
  player_responses?: { [key: string]: string[] };
}

@Injectable({
  providedIn: 'root'
})
export class GameService {
  private gameStateSubject = new BehaviorSubject<GameState | null>(null);
  public gameState$ = this.gameStateSubject.asObservable();

  private playerResponsesSubject = new BehaviorSubject<string[]>([]);
  public playerResponses$ = this.playerResponsesSubject.asObservable();

  private isJudgeSubject = new BehaviorSubject<boolean>(false);
  public isJudge$ = this.isJudgeSubject.asObservable();

  private periodicCheckInterval: any;
  
  // Property to store player responses when they become judge
  private savedPlayerResponses: { [key: string]: string[] } = {};

  constructor(
    private firebaseService: FirebaseService,
    private responseService: ResponseService,
    // promptService is no longer used as the Godot client handles prompts
    // private promptService: PromptService,
    private router: Router
  ) {
    console.log('GameService constructor called');

    // Subscribe to Firebase game state changes
    this.firebaseService.gameState$.subscribe(gameState => {
      console.log('GameService received game state update:', gameState ? `Status: ${gameState.status}` : 'null');

      if (gameState) {
        console.log('Updating game state in GameService');
        this.gameStateSubject.next(gameState);
        this.updatePlayerRole(gameState);
        this.handleGameStateChange(gameState);

        // Set up periodic check for all responses submitted
        this.setupPeriodicCheck(gameState);
      } else {
        console.warn('Received null game state in GameService');
      }
    });
  }

  // Set up periodic check for all responses submitted
  private setupPeriodicCheck(gameState: GameState): void {
    // Clear any existing interval
    if (this.periodicCheckInterval) {
      clearInterval(this.periodicCheckInterval);
      this.periodicCheckInterval = null;
    }

    // Only set up the interval if we're in the right state and we're the leader
    if (gameState.status === 'Waiting for Player Responses' && this.isLeader) {
      console.log('Setting up periodic check for all responses submitted');

      // Check every 5 seconds
      this.periodicCheckInterval = setInterval(() => {
        this.checkAllResponsesSubmitted();
      }, 5000);
    }
  }

  // Join a game with room code
  async joinGame(roomCode: string, playerName: string): Promise<boolean> {
    const success = await this.firebaseService.joinGame(roomCode, playerName);
    return success;
  }

  // Get player's responses from the game state
  private updatePlayerRole(gameState: GameState): void {
    const playerName = this.firebaseService.currentPlayerName;
    if (!playerName) return;

    console.log(`===== UPDATING PLAYER ROLE =====`);
    console.log(`Checking responses for player: ${playerName}`);

    // Check if player is the judge
    const judgeIndex = gameState.current_judge_index || 0;
    const isJudge = gameState.players && gameState.players[judgeIndex] === playerName;
    const wasJudge = this.isJudgeSubject.getValue();
    this.isJudgeSubject.next(isJudge);

    // Store current responses before handling judge status change
    const currentResponsesBeforeChange = this.playerResponsesSubject.getValue();
    
    // Create a static property to store responses when player becomes judge
    if (!this.savedPlayerResponses) {
      this.savedPlayerResponses = {};
    }

    // Skip response handling if player is the judge, but save their responses first
    if (isJudge) {
      console.log(`Player ${playerName} is the judge, saving their responses for later`);
      
      // Only save non-empty responses
      if (currentResponsesBeforeChange.length > 0) {
        console.log(`Saving ${currentResponsesBeforeChange.length} responses for player ${playerName}`);
        this.savedPlayerResponses[playerName] = [...currentResponsesBeforeChange];
      }
      
      // Clear the current responses since the judge doesn't need them
      this.playerResponsesSubject.next([]);
      console.log(`===== END UPDATING PLAYER ROLE (JUDGE) =====`);
      return;
    }
    
    // If player was previously a judge and is now a regular player, restore their saved responses
    if (wasJudge && !isJudge && this.savedPlayerResponses && this.savedPlayerResponses[playerName]) {
      const savedResponses = this.savedPlayerResponses[playerName];
      if (savedResponses.length > 0) {
        console.log(`Player ${playerName} was previously a judge, restoring their saved responses:`, savedResponses);
        this.playerResponsesSubject.next([...savedResponses]);
        console.log(`===== END UPDATING PLAYER ROLE (RESTORED) =====`);
        return;
      } else {
        console.log(`Player ${playerName} was previously a judge but has no saved responses. Proceeding with normal response assignment.`);
      }
    } else if (wasJudge && !isJudge) {
      console.log(`Player ${playerName} was previously a judge but has no saved responses in the main storage. Checking round changes.`);
    }

    // Get current responses
    const currentResponses = this.playerResponsesSubject.getValue();

    // Initial assignment of responses if we don't have any
    if (currentResponses.length === 0 && (gameState.status === 'Ready' || gameState.status === 'Waiting for Player Responses')) {
      // We need to assign initial responses to this player
      console.log('Assigning initial responses to player');
      const newResponses = this.responseService.getRandomResponses(5);
      this.playerResponsesSubject.next(newResponses);
      console.log('Assigned initial responses:', newResponses);

      // No longer updating Firebase with player responses
      // Responses are now managed locally by the web client
      console.log('Player responses are now managed locally, skipping Firebase update');

      console.log(`===== END UPDATING PLAYER ROLE =====`);
      return;
    }

    // Removed localStorage response restoration - Responses should only exist in volatile memory

    // For backward compatibility, check if we have responses in the game state
    // First, check if we have direct player responses (new format)
    if (gameState.player_responses && gameState.player_responses[playerName]) {
      const responseTexts = gameState.player_responses[playerName];
      console.log('Found direct player responses:', responseTexts);

      // Check for duplicates
      const uniqueResponses = [...new Set(responseTexts)];
      if (uniqueResponses.length !== responseTexts.length) {
        console.warn('Duplicate responses found, using unique responses only');
        this.playerResponsesSubject.next(uniqueResponses);
      } else {
        this.playerResponsesSubject.next(responseTexts);
      }
      console.log(`===== END UPDATING PLAYER ROLE =====`);
      return;
    }

    // Check for player_response_texts (older format)
    if (gameState.player_response_texts && gameState.player_response_texts[playerName]) {
      const responseTexts = gameState.player_response_texts[playerName];
      console.log('Found player response texts:', responseTexts);
      this.playerResponsesSubject.next(responseTexts);
      console.log(`===== END UPDATING PLAYER ROLE =====`);
      return;
    }

    // If we still don't have responses and we're in the right state, assign new ones
    if (currentResponses.length < 5 && (gameState.status === 'Ready' || gameState.status === 'Waiting for Player Responses')) {
      console.log(`Player has ${currentResponses.length} responses, need to add ${5 - currentResponses.length} more`);
      
      // Keep existing responses and only add new ones to fill up to 5
      if (currentResponses.length > 0) {
        // This is the key case where we need to preserve existing responses
        // Check if there was a round change
        const previousRound = gameState.current_round ? gameState.current_round - 1 : 0;
        const currentRound = gameState.current_round || 1;
        const roundChanged = previousRound > 0 && previousRound !== currentRound;
        
        if (roundChanged) {
          console.log(`Round changed from ${previousRound} to ${currentRound}`);
          console.log(`Preserving ${currentResponses.length} unused responses from last round`);
        }
        
        // Keep existing responses and add just enough new ones to get to 5
        const additionalNeeded = 5 - currentResponses.length;
        console.log(`Adding ${additionalNeeded} new responses to existing ones`);
        
        const additionalResponses = this.responseService.getRandomResponses(additionalNeeded);
        const updatedResponses = [...currentResponses, ...additionalResponses];
        
        console.log('Updated response array:', updatedResponses);
        this.playerResponsesSubject.next(updatedResponses);
      } else {
        // No existing responses, assign 5 new ones
        console.log('No existing responses, assigning 5 new ones');
        const newResponses = this.responseService.getRandomResponses(5);
        this.playerResponsesSubject.next(newResponses);
        console.log('Assigned initial responses:', newResponses);
      }

      // No longer updating Firebase with player responses
      // Responses are now managed locally by the web client
      console.log('Player responses are now managed locally, skipping Firebase update');
    }

    // No longer replenishing responses in any state other than ready/waiting for responses
    if (currentResponses.length < 5 && !isJudge && 
        gameState.status !== 'Ready' && gameState.status !== 'Waiting for Player Responses') {
      console.log(`Player has only ${currentResponses.length} responses. Not replenishing in current state: ${gameState.status}`);
    }

    console.log(`===== END UPDATING PLAYER ROLE =====`);
  }

  // Handle game state changes
  private handleGameStateChange(gameState: GameState): void {
    console.log('Game state changed:', gameState.status);

    // Removed localStorage response restoration - Responses should exist only in volatile memory
    const currentResponses = this.playerResponsesSubject.getValue();

    // IMPROVED PROMPT DEBUGGING: More detailed logging about the prompt
    if (gameState.current_prompt) {
      console.log('CURRENT PROMPT IN GAME STATE (DETAILED):', {
        promptObject: gameState.current_prompt,
        promptText: gameState.current_prompt.prompt,
        hasPromptProperty: 'prompt' in gameState.current_prompt,
        promptStringified: JSON.stringify(gameState.current_prompt)
      });
    } else {
      console.log('⚠️ NO PROMPT FOUND IN GAME STATE');
    }

    // Log important game state information for debugging
    if (gameState.status === 'Ready' || gameState.status === 'Waiting for Player Responses' || gameState.status === 'Waiting for Prompt') {
      console.log('Game state details:', {
        status: gameState.status,
        prompt: gameState.current_prompt,
        responseIndices: gameState.response_indices,
        allResponses: gameState.all_responses,
        responses: gameState.responses,
        player_response_texts: gameState.player_response_texts,
        player_responses: gameState.player_responses,
        current_round: gameState.current_round
      });

      // Check if the current player has responses
      const playerName = this.firebaseService.currentPlayerName;
      if (playerName) {
        console.log('Current player response data:', {
          player_response_texts: gameState.player_response_texts?.[playerName],
          player_responses: gameState.player_responses?.[playerName],
          response_indices: gameState.response_indices?.[playerName]
        });
      }
    }

    switch (gameState.status) {
      case 'Ready for Judging':
        // No longer replenishing responses in Ready for Judging state
        const playerName = this.firebaseService.currentPlayerName;
        if (playerName) {
          // Check if player is the judge
          const judgeIndex = gameState.current_judge_index || 0;
          const isJudge = gameState.players && gameState.players[judgeIndex] === playerName;

          // Only log for non-judges
          if (!isJudge) {
            // Get current responses
            const currentResponses = this.playerResponsesSubject.getValue();
            console.log(`Player has ${currentResponses.length} responses in Ready for Judging state`);
            console.log('Not replenishing responses as requested');
          } else {
            console.log('Player is the judge');
          }
        }
        break;

      case 'Waiting for Prompt':
        // Check if we already have a prompt in the game state
        console.log('Waiting for Prompt state detected, checking for prompt');

        // If we're the leader, update the current prompt to "Incoming Prompt!"
        if (this.isLeader) {
          console.log('Leader updating current prompt to "Incoming Prompt!"');
          this.updateCurrentPromptToIncoming().then(() => {
            console.log('Current prompt updated to "Incoming Prompt!" by leader');
          });
        }

        if (gameState.current_prompt && gameState.current_prompt.prompt) {
          // If we already have a prompt, we can display it
          console.log('✅ Prompt found during Waiting for Prompt state:', gameState.current_prompt.prompt);

          // The Godot client has already selected a prompt, we don't need to wait for Ready state
          // If we're the leader, transition directly to "Waiting for Player Responses"
          if (this.isLeader) {
            console.log('Leader detected prompt already available, transitioning to "Waiting for Player Responses"');
            setTimeout(() => {
              this.firebaseService.updateGameStatus('Waiting for Player Responses');
            }, 2000);
          }
        } else {
          console.log('Waiting for Godot client to select a prompt');

          // Check for prompt periodically if we're the leader
          if (this.isLeader) {
            console.log('Leader starting periodic prompt check');

            // Set up a prompt check interval
            const promptCheckInterval = setInterval(() => {
              this.firebaseService.verifyGameData();

              // Get the latest game state
              const currentState = this.gameStateSubject.getValue();

              // If we now have a prompt, clear the interval
              if (currentState?.current_prompt?.prompt) {
                console.log('Prompt now available:', currentState.current_prompt.prompt);
                clearInterval(promptCheckInterval);

                // Transition to Waiting for Player Responses after a short delay
                setTimeout(() => {
                  this.firebaseService.updateGameStatus('Waiting for Player Responses');
                }, 2000);
              }
            }, 2000); // Check every 2 seconds

            // Safety: clear the interval after 20 seconds to avoid infinite checking
            setTimeout(() => {
              clearInterval(promptCheckInterval);
            }, 20000);
          }
        }
        break;

      case 'Ready':
        // When the game is ready, ensure we have a prompt, then transition to "Waiting for Player Responses"
        console.log('Ready state detected, checking for prompt');

        if (!gameState.current_prompt || !gameState.current_prompt.prompt) {
          console.log('⚠️ WARNING: No prompt found in Ready state, this might cause display issues');

          // Wait a moment and attempt to fetch the updated game state directly
          if (this.isLeader) {
            console.log('Leader is requesting a prompt verification check');
            setTimeout(() => {
              this.firebaseService.verifyGameData();
            }, 1000);
          }
        } else {
          console.log('✅ Prompt found in Ready state:', gameState.current_prompt.prompt);
        }

        // When the game is ready, the leader should transition to "Waiting for Player Responses"
        if (this.isLeader) {
          console.log('Leader is transitioning game to "Waiting for Player Responses"');
          setTimeout(() => {
            this.firebaseService.updateGameStatus('Waiting for Player Responses');
          }, 2000); // Increased delay to ensure Firebase has processed the previous state
        }
        break;

      case 'Winner Chosen':
        // When a winner is chosen for the round, the leader should start a new round after a delay
        if (this.isLeader) {
          console.log('Leader will start a new round in 5 seconds...');

          // Store the timestamp when the winner was chosen
          const winnerChosenTime = Date.now();
          localStorage.setItem('winnerChosenTime', winnerChosenTime.toString());

          // We'll let the UI handle the countdown and trigger the next round
          // This is now handled in the game.page.ts file
          console.log('UI will handle countdown and trigger next round');
        }
        
        // IMPORTANT: For non-judges, ensure we preserve the current responses
        if (!this.isJudgeSubject.getValue()) {
          const currentResponses = this.playerResponsesSubject.getValue();
          if (currentResponses.length > 0) {
            console.log('Preserving player responses at Winner Chosen state:', currentResponses);
            
            // Save these responses in the savedPlayerResponses map for the current player
            // This provides another layer of backup for preserving responses
            const playerName = this.firebaseService.currentPlayerName;
            if (playerName) {
              this.savedPlayerResponses[playerName] = [...currentResponses];
              console.log(`Backed up ${currentResponses.length} responses for player ${playerName} at Winner Chosen state`);
            }
          }
        }
        break;

      case 'Game Over':
        // Game is over, someone reached 5 points
        console.log('Game over! Winner:', gameState.game_winner);
        // No automatic action needed - waiting for leader to choose what to do next
        break;

      case 'Closed':
        // Game is closed, return to home
        this.firebaseService.clearPlayerInfo();
        this.router.navigate(['/home']);
        break;
    }

    // Update player responses only if not the judge and not in the middle of selection
    if (gameState.status === 'Waiting for Player Responses') {
      console.log('Game status is "Waiting for Player Responses", checking if player needs responses');

      const playerName = this.firebaseService.currentPlayerName;
      if (playerName) {
        console.log(`Current player: ${playerName}`);

        // Check if player is the judge
        const judgeIndex = gameState.current_judge_index || 0;
        const isJudge = gameState.players && gameState.players[judgeIndex] === playerName;
        console.log(`Is player the judge? ${isJudge} (judge index: ${judgeIndex}, judge: ${gameState.players?.[judgeIndex]})`);

        // Log current response state
        const currentResponses = this.playerResponsesSubject.getValue();
        console.log(`Current local responses: ${currentResponses.length > 0 ? currentResponses.length : 'none'}`);
        if (currentResponses.length > 0) {
          console.log('Sample responses:', currentResponses.slice(0, 2));
        }

        // Only update responses for non-judges
        if (!isJudge) {
          // Get player responses from game state (check both formats for backward compatibility)
          const playerResponseTexts = gameState.player_responses?.[playerName] || gameState.player_response_texts?.[playerName] || [];
          const currentResponses = this.playerResponsesSubject.getValue();

          // IMPORTANT: We want to preserve the existing responses
          // that the player has accumulated through gameplay
          // Only update from Firebase if:
          // 1. There are actually responses in Firebase, AND
          // 2. The current local responses are empty (never had any responses)
          if (playerResponseTexts.length > 0 && currentResponses.length === 0) {
            console.log('Initializing responses from Firebase (empty hand):', playerResponseTexts);
            this.playerResponsesSubject.next(playerResponseTexts);
          } else if (currentResponses.length === 0) {
            // If we don't have any responses locally or in Firebase, assign new ones
            // This is only for initial setup, not for replenishment
            console.log('No responses found locally or in Firebase, assigning initial ones');
            const newResponses = this.responseService.getRandomResponses(5);
            this.playerResponsesSubject.next(newResponses);
            console.log('Assigned initial responses:', newResponses);

            // No longer saving to localStorage - keeping responses only in volatile memory
          } else if (currentResponses.length < 5) {
            // We have some responses but need more to reach 5
            console.log(`Only have ${currentResponses.length} responses, topping up to 5`);
            const additionalNeeded = 5 - currentResponses.length;
            const additionalResponses = this.responseService.getRandomResponses(additionalNeeded);
            const updatedResponses = [...currentResponses, ...additionalResponses];
            
            console.log('Updated response array:', updatedResponses);
            this.playerResponsesSubject.next(updatedResponses);
          } else {
            // We have enough responses, preserve them
            console.log('Preserving existing responses:', currentResponses);
          }
        }
      }
    }
  }

  // Submit a response
  async submitResponse(responseText: string, responseIndex: number): Promise<void> {
    console.log(`===== SUBMITTING RESPONSE =====`);
    console.log(`Original response array:`, this.playerResponsesSubject.getValue());
    console.log(`Submitting response: "${responseText}" at index ${responseIndex}`);

    // Get the current responses
    const currentResponses = this.playerResponsesSubject.getValue();
    let updatedResponses: string[];

    // Check if this is a custom response (index = -1) or a card response
    if (responseIndex === -1) {
      // For custom responses, don't remove any cards from the player's hand
      console.log('Custom response submitted, keeping all existing responses');
      updatedResponses = [...currentResponses];
    } else {
      // For card responses, remove the selected card and replace it with a new one
      // Create a new array without the selected response
      console.log(`Removing response at index ${responseIndex}: "${currentResponses[responseIndex]}"`);
      updatedResponses = currentResponses.filter((_, index) => index !== responseIndex);
      console.log(`Updated response array after removal:`, updatedResponses);
      
      // Get a new random response to replace the used one
      const newResponse = this.responseService.getRandomResponses(1)[0];
      if (newResponse) {
        // Make sure the new response doesn't already exist in our hand
        while (updatedResponses.includes(newResponse)) {
          console.log(`Response "${newResponse}" already in hand, getting a different one`);
          const anotherResponse = this.responseService.getRandomResponses(1)[0];
          if (anotherResponse && !updatedResponses.includes(anotherResponse)) {
            console.log(`Using alternative response: "${anotherResponse}"`);
            updatedResponses.push(anotherResponse);
            console.log(`Added new response: "${anotherResponse}"`);
            break;
          }
        }
        
        // If we didn't add a response yet, add the original one
        if (updatedResponses.length < currentResponses.length) {
          updatedResponses.push(newResponse);
          console.log(`Added new response: "${newResponse}"`);
        }
      } else {
        console.warn('Failed to get a new random response!');
      }
      
      // Log all submissions and updates to track what's happening
      console.log("DEBUG - Card submission flow:");
      console.log("1. Original responses:", [...currentResponses]);
      console.log("2. Card being submitted:", responseText);
      console.log("3. Index of submitted card:", responseIndex);
      console.log("4. Updated responses after removal:", [...updatedResponses]);
    }

    // Update the subject with the new array - THIS IS CRITICAL
    // The playerResponsesSubject needs to be updated immediately
    console.log(`Updating playerResponsesSubject with new array of length ${updatedResponses.length}`);
    this.playerResponsesSubject.next(updatedResponses);
    console.log(`Final updated response array:`, updatedResponses);

    try {
      // Submit to Firebase and wait for the operation to complete
      await this.firebaseService.submitResponse(responseText, responseIndex);

      console.log(`Response submitted to Firebase successfully`);

      // After submitting, manually check if all players have submitted responses
      // This is a backup in case the automatic check in the Firebase service fails
      setTimeout(() => {
        this.checkAllResponsesSubmitted();
      }, 1000); // Wait 1 second to ensure Firebase has updated

      console.log(`===== END SUBMITTING RESPONSE =====`);
    } catch (error) {
      // If there's an error, revert the local array
      this.playerResponsesSubject.next(currentResponses);
      console.error(`Error submitting response:`, error);
      console.log(`===== END SUBMITTING RESPONSE (WITH ERROR) =====`);
      throw error;
    }
  }

  // Manually check if all players have submitted responses
  private async checkAllResponsesSubmitted(): Promise<void> {
    console.log(`===== MANUAL CHECK FOR ALL RESPONSES SUBMITTED =====`);

    // Get the current game state
    const gameState = this.gameStateSubject.getValue();
    if (!gameState) {
      console.log(`No game state available, aborting check`);
      console.log(`===== END MANUAL CHECK FOR ALL RESPONSES SUBMITTED =====`);
      return;
    }

    // Only proceed if we're in the right state
    if (gameState.status !== 'Waiting for Player Responses') {
      console.log(`Game status is ${gameState.status}, not checking responses`);
      console.log(`===== END MANUAL CHECK FOR ALL RESPONSES SUBMITTED =====`);
      return;
    }

    const players = gameState.players || [];
    const responses = gameState.submitted_responses || [];

    // Find the judge
    const judgeIndex = gameState.current_judge_index || 0;
    const judge = players[judgeIndex];

    // Count non-judge players
    const nonJudgePlayers = players.filter(p => p !== judge);

    console.log('Manual response check:', {
      totalPlayers: players.length,
      nonJudgePlayers: nonJudgePlayers.length,
      submittedResponses: responses.length,
      judge: judge
    });

    // If there are no non-judge players, auto-advance
    if (nonJudgePlayers.length === 0) {
      console.log('No non-judge players, auto-advancing to Ready for Judging');
      if (this.isLeader) {
        await this.firebaseService.updateGameStatus('Ready for Judging');
      }
      console.log(`===== END MANUAL CHECK FOR ALL RESPONSES SUBMITTED =====`);
      return;
    }

    // Check if each player has submitted a response
    const playerResponseCount: { [key: string]: number } = {};

    // Initialize counts to zero
    nonJudgePlayers.forEach(player => {
      playerResponseCount[player] = 0;
    });

    // Count responses for each player
    responses.forEach(response => {
      if (playerResponseCount[response.player_name] !== undefined) {
        playerResponseCount[response.player_name]++;
      }
    });

    // Check if all players have at least one submission
    const allPlayersHaveAtLeastOneSubmission = Object.values(playerResponseCount).every(count => count > 0);

    console.log('Player response counts:', playerResponseCount);
    console.log('All players have at least one submission:', allPlayersHaveAtLeastOneSubmission);

    // If all players have submitted at least one response and we're the leader, update the game status
    if (allPlayersHaveAtLeastOneSubmission && this.isLeader) {
      console.log('All players have submitted at least one response, changing status to Ready for Judging');
      await this.firebaseService.updateGameStatus('Ready for Judging');
    } else if (allPlayersHaveAtLeastOneSubmission) {
      console.log('All players have submitted at least one response, but not the leader');
    } else {
      console.log('Waiting for more players to submit responses');
    }

    console.log(`===== END MANUAL CHECK FOR ALL RESPONSES SUBMITTED =====`);
  }

  // Judge selects a winner
  selectWinner(responseIndex: number): Promise<void> {
    return this.firebaseService.selectWinner(responseIndex);
  }

  // Skip judging (can be called by any player)
  skipJudging(): Promise<void> {
    return this.firebaseService.skipJudging();
  }

  // Leader starts a new game
  startNewGame(): Promise<void> {
    // Get the current responses but DO NOT preserve all of them
    // We only want to preserve the responses that weren't submitted
    const currentResponses = this.playerResponsesSubject.getValue();
    const hasResponses = currentResponses.length > 0;
    
    // Don't automatically restore all responses - instead, we'll let the 
    // normal response handling in updatePlayerRole handle this
    if (hasResponses) {
      console.log('Current responses before new round:', currentResponses);
      console.log('These will NOT be automatically restored - only non-submitted responses should persist');
    }
    
    return this.firebaseService.startNewGame();
  }

  // Leader resets the game
  resetGame(): Promise<void> {
    // We intentionally don't preserve responses when resetting the game
    // This gives players a fresh hand of cards when starting a new game
    console.log('Resetting game, player will receive new responses');
    
    return this.firebaseService.resetGame();
  }

  // Leader closes the game
  closeGame(): Promise<void> {
    // Clear any periodic check interval
    if (this.periodicCheckInterval) {
      clearInterval(this.periodicCheckInterval);
      this.periodicCheckInterval = null;
    }

    return this.firebaseService.closeGame();
  }

  // Player leaves the game
  leaveGame(): Promise<void> {
    // Clear any periodic check interval
    if (this.periodicCheckInterval) {
      clearInterval(this.periodicCheckInterval);
      this.periodicCheckInterval = null;
    }

    return this.firebaseService.removePlayerFromGame();
  }

  // Update game status
  updateGameStatus(status: string): Promise<void> {
    console.log(`Updating game status to: ${status}`);
    return this.firebaseService.updateGameStatus(status);
  }

  // Update current prompt to "Incoming Prompt!"
  updateCurrentPromptToIncoming(): Promise<void> {
    console.log('Updating current prompt to "Incoming Prompt!"');
    return this.firebaseService.updateCurrentPromptToIncoming();
  }

  // Check if current player is the leader
  get isLeader(): boolean {
    return this.firebaseService.isGameLeader;
  }

  // Get current player name
  get playerName(): string | null {
    return this.firebaseService.currentPlayerName;
  }

  // Get current player responses (volatile memory only)
  getCurrentResponses(): string[] {
    return this.playerResponsesSubject.getValue();
  }

  // Helper method to compare arrays
  private areArraysEqual(arr1: any[], arr2: any[]): boolean {
    if (arr1.length !== arr2.length) return false;
    for (let i = 0; i < arr1.length; i++) {
      if (arr1[i] !== arr2[i]) return false;
    }
    return true;
  }
}






