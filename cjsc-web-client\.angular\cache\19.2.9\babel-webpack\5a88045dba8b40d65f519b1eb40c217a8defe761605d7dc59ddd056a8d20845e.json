{"ast": null, "code": "import _asyncToGenerator from \"D:/Godot_Games/cjsc/cjsc-web-client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nvar _GamePage;\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/game.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@ionic/angular\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nfunction GamePage_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"p\");\n    i0.ɵɵtext(2, \"Loading game...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction GamePage_div_10_ion_item_11_ion_note_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ion-note\", 2);\n    i0.ɵɵtext(1, \"You\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GamePage_div_10_ion_item_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ion-item\", 15)(1, \"ion-label\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, GamePage_div_10_ion_item_11_ion_note_3_Template, 2, 0, \"ion-note\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const player_r1 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(player_r1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", player_r1 === ctx_r1.playerName);\n  }\n}\nfunction GamePage_div_10_ion_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ion-button\", 17);\n    i0.ɵɵlistener(\"click\", function GamePage_div_10_ion_button_12_Template_ion_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.startGame());\n    });\n    i0.ɵɵtext(1, \" Start Game \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GamePage_div_10_p_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 11);\n    i0.ɵɵtext(1, \" Need at least 3 players to start the game. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GamePage_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"h1\", 11);\n    i0.ɵɵtext(2, \"Waiting for Players\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 11);\n    i0.ɵɵtext(4, \"Room Code: \");\n    i0.ɵɵelementStart(5, \"strong\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"ion-list\")(8, \"ion-list-header\")(9, \"ion-label\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(11, GamePage_div_10_ion_item_11_Template, 4, 2, \"ion-item\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, GamePage_div_10_ion_button_12_Template, 2, 0, \"ion-button\", 13)(13, GamePage_div_10_p_13_Template, 2, 0, \"p\", 14);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.roomCode);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"Players (\", ctx_r1.getPlayers().length, \")\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getPlayers());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLeader && ctx_r1.getPlayers().length >= 3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLeader && ctx_r1.getPlayers().length < 3);\n  }\n}\nfunction GamePage_div_11_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"h3\");\n    i0.ɵɵtext(2, \"You are the judge for this round\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction GamePage_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"h1\", 11);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 19)(4, \"h2\", 11);\n    i0.ɵɵtext(5, \"Waiting for prompt...\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 11);\n    i0.ɵɵtext(7, \"The game is selecting a prompt for this round\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, GamePage_div_11_div_8_Template, 3, 0, \"div\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Round \", ctx_r1.getCurrentRound(), \"\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isJudge);\n  }\n}\nfunction GamePage_div_12_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"h3\");\n    i0.ɵɵtext(2, \"You are the judge for this round\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"Wait for players to submit their responses.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction GamePage_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"h1\", 11);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 19)(4, \"h2\", 11);\n    i0.ɵɵtext(5, \"Prompt:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 22);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, GamePage_div_12_div_8_Template, 5, 0, \"div\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Round \", ctx_r1.getCurrentRound(), \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.getPrompt());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isJudge);\n  }\n}\nfunction GamePage_div_13_div_8_p_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", (ctx_r1.gameState == null ? null : ctx_r1.gameState.submitted_responses == null ? null : ctx_r1.gameState.submitted_responses.length) || 0, \" / \", ((ctx_r1.gameState == null ? null : ctx_r1.gameState.players == null ? null : ctx_r1.gameState.players.length) || 0) - 1, \" players have submitted \");\n  }\n}\nfunction GamePage_div_13_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"h3\");\n    i0.ɵɵtext(2, \"You are the judge for this round\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"Wait for players to submit their responses.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, GamePage_div_13_div_8_p_5_Template, 2, 2, \"p\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.gameState == null ? null : ctx_r1.gameState.submitted_responses);\n  }\n}\nfunction GamePage_div_13_div_9_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"p\");\n    i0.ɵɵtext(2, \"Waiting for responses to be assigned...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction GamePage_div_13_div_9_div_4_p_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", (ctx_r1.gameState == null ? null : ctx_r1.gameState.submitted_responses == null ? null : ctx_r1.gameState.submitted_responses.length) || 0, \" / \", ((ctx_r1.gameState == null ? null : ctx_r1.gameState.players == null ? null : ctx_r1.gameState.players.length) || 0) - 1, \" players have submitted \");\n  }\n}\nfunction GamePage_div_13_div_9_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"p\");\n    i0.ɵɵtext(2, \"You have submitted your response.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"Waiting for other players to submit their responses...\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, GamePage_div_13_div_9_div_4_p_5_Template, 2, 2, \"p\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.gameState == null ? null : ctx_r1.gameState.submitted_responses);\n  }\n}\nfunction GamePage_div_13_div_9_div_5_ion_list_8_ion_item_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ion-item\", 36);\n    i0.ɵɵlistener(\"click\", function GamePage_div_13_div_9_div_5_ion_list_8_ion_item_1_Template_ion_item_click_0_listener() {\n      const i_r6 = i0.ɵɵrestoreView(_r5).index;\n      const ctx_r1 = i0.ɵɵnextContext(5);\n      return i0.ɵɵresetView(ctx_r1.selectResponse(i_r6));\n    });\n    i0.ɵɵelementStart(1, \"ion-label\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const response_r7 = ctx.$implicit;\n    const i_r6 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(5);\n    i0.ɵɵclassProp(\"selected-response\", ctx_r1.selectedResponseIndex === i_r6);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(response_r7);\n  }\n}\nfunction GamePage_div_13_div_9_div_5_ion_list_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ion-list\");\n    i0.ɵɵtemplate(1, GamePage_div_13_div_9_div_5_ion_list_8_ion_item_1_Template, 3, 3, \"ion-item\", 35);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.playerResponses);\n  }\n}\nfunction GamePage_div_13_div_9_div_5_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"ion-item\", 15)(2, \"ion-label\", 38);\n    i0.ɵɵtext(3, \"Enter your custom response:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"ion-textarea\", 39);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function GamePage_div_13_div_9_div_5_div_9_Template_ion_textarea_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.customResponse, $event) || (ctx_r1.customResponse = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.customResponse);\n    i0.ɵɵproperty(\"counter\", true);\n  }\n}\nfunction GamePage_div_13_div_9_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"ion-segment\", 31);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function GamePage_div_13_div_9_div_5_Template_ion_segment_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.responseMode, $event) || (ctx_r1.responseMode = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ionChange\", function GamePage_div_13_div_9_div_5_Template_ion_segment_ionChange_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.responseTypeChanged());\n    });\n    i0.ɵɵelementStart(2, \"ion-segment-button\", 32)(3, \"ion-label\");\n    i0.ɵɵtext(4, \"Cards\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"ion-segment-button\", 33)(6, \"ion-label\");\n    i0.ɵɵtext(7, \"Custom\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(8, GamePage_div_13_div_9_div_5_ion_list_8_Template, 2, 1, \"ion-list\", 24)(9, GamePage_div_13_div_9_div_5_div_9_Template, 5, 2, \"div\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.responseMode);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.responseMode === \"cards\" && ctx_r1.playerResponses.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.responseMode === \"custom\");\n  }\n}\nfunction GamePage_div_13_div_9_ion_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ion-button\", 40);\n    i0.ɵɵlistener(\"click\", function GamePage_div_13_div_9_ion_button_6_Template_ion_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.submitResponse());\n    });\n    i0.ɵɵtext(1, \" Submit Response \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.responseMode === \"cards\" && (ctx_r1.selectedResponseIndex < 0 || ctx_r1.playerResponses.length === 0) || ctx_r1.responseMode === \"custom\" && !ctx_r1.customResponse.trim());\n  }\n}\nfunction GamePage_div_13_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"h3\", 11);\n    i0.ɵɵtext(2, \"Choose your response:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, GamePage_div_13_div_9_div_3_Template, 3, 0, \"div\", 26)(4, GamePage_div_13_div_9_div_4_Template, 6, 1, \"div\", 26)(5, GamePage_div_13_div_9_div_5_Template, 10, 3, \"div\", 27)(6, GamePage_div_13_div_9_ion_button_6_Template, 2, 1, \"ion-button\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.playerResponses.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasSubmitted);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.hasSubmitted);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.hasSubmitted);\n  }\n}\nfunction GamePage_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"h1\", 11);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 19)(4, \"h2\", 11);\n    i0.ɵɵtext(5, \"Prompt:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 22);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, GamePage_div_13_div_8_Template, 6, 1, \"div\", 20)(9, GamePage_div_13_div_9_Template, 7, 4, \"div\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Round \", ctx_r1.getCurrentRound(), \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.getPrompt());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isJudge);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isJudge);\n  }\n}\nfunction GamePage_div_14_div_3_ion_item_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ion-item\", 36);\n    i0.ɵɵlistener(\"click\", function GamePage_div_14_div_3_ion_item_4_Template_ion_item_click_0_listener() {\n      const i_r12 = i0.ɵɵrestoreView(_r11).index;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.selectWinnerResponse(i_r12));\n    });\n    i0.ɵɵelementStart(1, \"ion-label\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const response_r13 = ctx.$implicit;\n    const i_r12 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"selected-response\", ctx_r1.selectedWinnerIndex === i_r12);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(response_r13.text);\n  }\n}\nfunction GamePage_div_14_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 43)(1, \"h3\", 11);\n    i0.ɵɵtext(2, \"You are the judge - select the best response:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ion-list\");\n    i0.ɵɵtemplate(4, GamePage_div_14_div_3_ion_item_4_Template, 3, 3, \"ion-item\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"ion-button\", 40);\n    i0.ɵɵlistener(\"click\", function GamePage_div_14_div_3_Template_ion_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.selectWinner());\n    });\n    i0.ɵɵtext(6, \" Select Winner \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"ion-button\", 44);\n    i0.ɵɵlistener(\"click\", function GamePage_div_14_div_3_Template_ion_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.skipJudging());\n    });\n    i0.ɵɵtext(8, \" Skip Judging (No Winner) \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getSubmittedResponses());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.selectedWinnerIndex === null);\n  }\n}\nfunction GamePage_div_14_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"h3\");\n    i0.ɵɵtext(2, \"Waiting for judge to select a winner...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction GamePage_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"h1\", 11);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, GamePage_div_14_div_3_Template, 9, 2, \"div\", 41)(4, GamePage_div_14_div_4_Template, 3, 0, \"div\", 42);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Round \", ctx_r1.getCurrentRound(), \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isJudge);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isJudge);\n  }\n}\nfunction GamePage_div_15_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"h2\", 11);\n    i0.ɵɵtext(2, \"Prompt:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 22);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.getPrompt());\n  }\n}\nfunction GamePage_div_15_ion_item_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ion-item\", 15)(1, \"ion-label\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ion-note\", 2);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const player_r14 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(player_r14);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getPlayerScore(player_r14));\n  }\n}\nfunction GamePage_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"h1\", 11);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, GamePage_div_15_div_3_Template, 5, 1, \"div\", 46);\n    i0.ɵɵelementStart(4, \"div\", 47)(5, \"h2\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 48);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"ion-list\")(10, \"ion-list-header\")(11, \"ion-label\");\n    i0.ɵɵtext(12, \"Scores\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(13, GamePage_div_15_ion_item_13_Template, 5, 2, \"ion-item\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 49)(15, \"p\");\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Round \", ctx_r1.getCurrentRound(), \" Results\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getPrompt());\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Winner: \", (ctx_r1.gameState == null ? null : ctx_r1.gameState.winner) || \"No winner\", \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate((ctx_r1.gameState == null ? null : ctx_r1.gameState.winning_response) || \"No winning response\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getPlayers());\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Next round starting automatically in \", ctx_r1.nextRoundCountdown, \" seconds...\");\n  }\n}\nfunction GamePage_div_16_ion_item_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ion-item\", 15)(1, \"ion-label\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ion-note\", 2);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const player_r15 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(player_r15);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getPlayerScore(player_r15));\n  }\n}\nfunction GamePage_div_16_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 51)(1, \"h3\", 11);\n    i0.ɵɵtext(2, \"What would you like to do?\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ion-button\", 17);\n    i0.ɵɵlistener(\"click\", function GamePage_div_16_div_13_Template_ion_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.playAgain());\n    });\n    i0.ɵɵtext(4, \" Play Again (Same Players) \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"ion-button\", 52);\n    i0.ɵɵlistener(\"click\", function GamePage_div_16_div_13_Template_ion_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.endGame());\n    });\n    i0.ɵɵtext(6, \" End Game \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction GamePage_div_16_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"h3\");\n    i0.ɵɵtext(2, \"Waiting for leader to decide...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction GamePage_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"h1\", 11);\n    i0.ɵɵtext(2, \"Game Over\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 47)(4, \"h2\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7, \"Reached 5 points first!\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"ion-list\")(9, \"ion-list-header\")(10, \"ion-label\");\n    i0.ɵɵtext(11, \"Final Scores\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(12, GamePage_div_16_ion_item_12_Template, 5, 2, \"ion-item\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, GamePage_div_16_div_13_Template, 7, 0, \"div\", 50)(14, GamePage_div_16_div_14_Template, 3, 0, \"div\", 42);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"Game Winner: \", (ctx_r1.gameState == null ? null : ctx_r1.gameState.game_winner) || ctx_r1.getGameWinner(), \"\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getPlayers());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLeader);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLeader);\n  }\n}\nexport class GamePage {\n  constructor(gameService, router, alertController) {\n    this.gameService = gameService;\n    this.router = router;\n    this.alertController = alertController;\n    this.gameState = null;\n    this.playerResponses = [];\n    this.isJudge = false;\n    this.selectedResponse = null;\n    this.selectedResponseIndex = -1; // Track the index of the selected response\n    this.selectedWinnerIndex = null;\n    this.hasSubmitted = false;\n    this.nextRoundCountdown = 8; // Countdown timer for next round (increased to allow Godot prompt selection)\n    // New variables for custom response feature\n    this.responseMode = 'cards'; // 'cards' or 'custom'\n    this.customResponse = '';\n    // Add a new property to track if prompt should be hidden\n    this.hidePrompt = false;\n    // Add a new property to track the last selected response index\n    this.lastSelectedResponseIndex = -1;\n    // Add these properties to track selection state more reliably\n    this.selectedResponseText = null;\n    this.isProcessingGameStateUpdate = false;\n    // Add properties to track custom response state\n    this.lastCustomResponse = '';\n    this.lastResponseMode = 'cards';\n    this.gameStateSubscription = null;\n    this.playerResponsesSubscription = null;\n    this.isJudgeSubscription = null;\n    this.previousGameState = null;\n  }\n  ngOnInit() {\n    console.log('GamePage ngOnInit called');\n    // Subscribe to game state changes\n    this.gameStateSubscription = this.gameService.gameState$.subscribe(state => {\n      console.log('GamePage received game state update:', state ? `Status: ${state.status}` : 'null');\n      this.isProcessingGameStateUpdate = true;\n      // Update the gameState property\n      this.gameState = state;\n      this.previousGameState = (state === null || state === void 0 ? void 0 : state.status) || null;\n      // Hide prompt when transitioning to Winner Chosen\n      if (state && state.status === 'Winner Chosen') {\n        this.hidePrompt = true;\n        console.log('Hiding prompt because status is Winner Chosen');\n      }\n      // Show prompt when we have a new prompt in any state that should display it\n      if (state && state.current_prompt && state.current_prompt.prompt) {\n        // Show prompt for these states\n        if (state.status === 'Waiting for Prompt' || state.status === 'Ready' || state.status === 'Waiting for Player Responses' || state.status === 'Ready for Judging') {\n          this.hidePrompt = false;\n          console.log('Showing prompt in', state.status, 'state with prompt:', state.current_prompt.prompt);\n        }\n      }\n      // Hide prompt when waiting for a new prompt (but no prompt available yet)\n      if (state && state.status === 'Waiting for Prompt' && (!state.current_prompt || !state.current_prompt.prompt)) {\n        this.hidePrompt = true;\n        console.log('Hiding prompt because status is Waiting for Prompt and no prompt available');\n      }\n      // Reset submission state when game status changes\n      if (state && state.status === 'Waiting for Player Responses') {\n        var _this$gameState;\n        // Check if the player has already submitted a response\n        const playerName = this.playerName;\n        const submittedResponses = state.submitted_responses || [];\n        // Check if this player has already submitted a response\n        const hasAlreadySubmitted = submittedResponses.some(response => response.player_name === playerName);\n        // IMPORTANT: Update hasSubmitted based on the game state\n        // This ensures it correctly reflects whether the player has submitted\n        // regardless of local state\n        this.hasSubmitted = hasAlreadySubmitted;\n        console.log(`Setting hasSubmitted=${hasAlreadySubmitted} based on game state`);\n        // Track the previous round to detect round changes\n        const previousRound = ((_this$gameState = this.gameState) === null || _this$gameState === void 0 ? void 0 : _this$gameState.current_round) || 0;\n        const currentRound = state.current_round || 0;\n        // If we're in a new round, make sure hasSubmitted is reset and log current responses\n        if (previousRound !== currentRound && previousRound > 0) {\n          console.log(`Round changed from ${previousRound} to ${currentRound}, resetting hasSubmitted`);\n          console.log(`Current responses at round change:`, [...this.playerResponses]);\n          this.hasSubmitted = false;\n        }\n        if (!hasAlreadySubmitted) {\n          // Only reset selection if the player hasn't made a selection yet\n          // or if the responses have changed\n          if (this.lastSelectedResponseIndex === -1 || this.lastSelectedResponseIndex >= 0 && this.lastSelectedResponseIndex < this.playerResponses.length && this.playerResponses[this.lastSelectedResponseIndex] !== this.selectedResponse) {\n            this.selectedResponse = null;\n            this.selectedResponseIndex = -1;\n          } else {\n            // Restore the last selected index if it's still valid\n            this.selectedResponseIndex = this.lastSelectedResponseIndex;\n            if (this.selectedResponseIndex >= 0 && this.selectedResponseIndex < this.playerResponses.length) {\n              this.selectedResponse = this.playerResponses[this.selectedResponseIndex];\n            }\n          }\n          // Don't reset custom response or response mode here\n          // This was causing the issue where custom responses were lost when other players submitted\n        }\n      }\n      // Reset winner selection when game status changes\n      if (state && state.status === 'Ready for Judging') {\n        this.selectedWinnerIndex = null;\n      }\n      // Handle game closed state\n      if (state && state.status === 'Closed') {\n        this.router.navigate(['/home']);\n      }\n      // Remove web client countdown - let Godot client handle the countdown and new round logic\n      // The Godot client will now handle the countdown and automatically start new rounds\n      // If in response phase, handle selection state based on submission status\n      if (state && state.status === 'Waiting for Player Responses') {\n        // Check if the player has already submitted a response\n        const playerName = this.playerName;\n        const submittedResponses = state.submitted_responses || []; // Check if this player has already submitted a response\n        const hasAlreadySubmitted = submittedResponses.some(response => response.player_name === playerName);\n        // Log the current selection state before updating\n        console.log('Current selection state before updating hasSubmitted:', {\n          currentHasSubmitted: this.hasSubmitted,\n          newHasSubmitted: hasAlreadySubmitted,\n          selectedResponseIndex: this.selectedResponseIndex,\n          selectedResponse: this.selectedResponse,\n          selectedResponseText: this.selectedResponseText,\n          responseCount: this.playerResponses.length\n        });\n        // Update submission status\n        this.hasSubmitted = hasAlreadySubmitted;\n        // Only reset if player has submitted\n        if (hasAlreadySubmitted) {\n          console.log('Player has submitted, resetting selection');\n          this.selectedResponseIndex = -1;\n          this.selectedResponse = null;\n          this.selectedResponseText = null;\n          this.lastSelectedResponseIndex = -1;\n        } else {\n          // If player hasn't submitted, preserve the current selection\n          console.log('Player has not submitted, preserving selection');\n          // No need to reset anything here, keep the current selection\n        }\n      }\n      this.isProcessingGameStateUpdate = false;\n    });\n    // Subscribe to player responses\n    this.playerResponsesSubscription = this.gameService.playerResponses$.subscribe(responses => {\n      console.log('Received player responses update:', responses);\n      // Store current response mode and custom response\n      const currentResponseMode = this.responseMode;\n      const currentCustomResponse = this.customResponse;\n      // FIXED: Always process response updates to ensure cards are properly removed\n      // Only skip in the middle of a game state update, but NOT when the player has submitted\n      // This ensures the array gets updated after submission\n      if (!this.isProcessingGameStateUpdate) {\n        console.log('Processing player responses update (not in game state update)');\n        // Save the current submission state\n        const wasSubmitted = this.hasSubmitted;\n        // Process the responses\n        this.handlePlayerResponsesUpdate(responses);\n        // Restore the submission state (in case handlePlayerResponsesUpdate changed it)\n        this.hasSubmitted = wasSubmitted;\n        // Restore response mode and custom response after update\n        if (currentResponseMode === 'custom') {\n          console.log('Restoring custom response mode and text:', currentCustomResponse);\n          this.responseMode = 'custom';\n          this.customResponse = currentCustomResponse;\n        }\n      } else {\n        console.log('Skipping player responses update due to game state update in progress');\n      }\n    });\n    // Subscribe to judge status\n    this.isJudgeSubscription = this.gameService.isJudge$.subscribe(isJudge => {\n      this.isJudge = isJudge;\n    });\n  }\n  ngOnDestroy() {\n    // Unsubscribe to prevent memory leaks\n    if (this.gameStateSubscription) {\n      this.gameStateSubscription.unsubscribe();\n    }\n    if (this.playerResponsesSubscription) {\n      this.playerResponsesSubscription.unsubscribe();\n    }\n    if (this.isJudgeSubscription) {\n      this.isJudgeSubscription.unsubscribe();\n    }\n    // Clear any active countdown\n    this.clearNextRoundCountdown();\n  }\n  // Handle response type change (cards/custom)\n  responseTypeChanged() {\n    console.log(`Response mode changed to: ${this.responseMode}`);\n    // Store the current response mode\n    this.lastResponseMode = this.responseMode;\n    // Reset selections when switching modes\n    if (this.responseMode === 'cards') {\n      // Store the custom response before clearing it\n      this.lastCustomResponse = this.customResponse;\n      this.customResponse = '';\n      // Don't reset selectedResponseIndex here\n    } else {\n      // Store the current selection before switching to custom mode\n      this.lastSelectedResponseIndex = this.selectedResponseIndex;\n      this.selectedResponseIndex = -1;\n    }\n    console.log(`Response mode change complete. Mode: ${this.responseMode}, Custom: ${this.customResponse}`);\n  }\n  // Start countdown timer for next round\n  startNextRoundCountdown() {\n    // Reset countdown to 8 seconds (increased to allow Godot prompt selection)\n    this.nextRoundCountdown = 8;\n    // Make sure prompt is hidden during countdown\n    this.hidePrompt = true;\n    console.log('Hiding prompt during next round countdown');\n    // Clear any existing interval\n    this.clearNextRoundCountdown();\n    // Start a new interval\n    this.countdownInterval = setInterval(() => {\n      this.nextRoundCountdown--;\n      // When countdown reaches 0, clear the interval and start new round\n      if (this.nextRoundCountdown <= 0) {\n        this.clearNextRoundCountdown();\n        // If this player is the leader, start a new round\n        if (this.isLeader) {\n          console.log('Countdown reached zero, leader starting new round...');\n          this.gameService.startNewGame();\n        }\n      }\n    }, 1000);\n  }\n  // Clear countdown timer\n  clearNextRoundCountdown() {\n    if (this.countdownInterval) {\n      clearInterval(this.countdownInterval);\n      this.countdownInterval = null;\n    }\n  }\n  // Start the game (leader only)\n  startGame() {\n    // Reset hidePrompt flag when starting a new game\n    this.hidePrompt = false;\n    console.log('Resetting hidePrompt flag when starting a new game');\n    this.gameService.startNewGame();\n  }\n  // Submit a response\n  submitResponse() {\n    try {\n      if (this.responseMode === 'cards') {\n        // Submit pre-defined card response\n        if (this.selectedResponseIndex < 0) {\n          console.warn('Cannot submit: No response selected');\n          return;\n        }\n        // Make sure we have the selected response text\n        const responseText = this.playerResponses[this.selectedResponseIndex];\n        if (!responseText) {\n          console.warn('Cannot submit: Selected response text is empty');\n          return;\n        }\n        console.log(`Submitting card response: \"${responseText}\" with index ${this.selectedResponseIndex}`);\n        // Mark as submitted BEFORE sending to avoid race conditions\n        this.hasSubmitted = true;\n        // Send both the response text and the index\n        this.gameService.submitResponse(responseText, this.selectedResponseIndex).then(() => {\n          // Track that this was the last submitted response\n          console.log('Card response submitted successfully');\n          // Force update player responses from the service\n          const updatedResponses = this.gameService.getCurrentResponses();\n          if (updatedResponses.length > 0) {\n            console.log('Updated player responses after submission:', updatedResponses);\n            this.playerResponses = [...updatedResponses];\n          }\n        }).catch(error => {\n          console.error('Error submitting response:', error);\n          this.hasSubmitted = false; // Reset submission state on error\n        });\n      } else {\n        var _this$customResponse;\n        // Submit custom text response\n        if (!((_this$customResponse = this.customResponse) !== null && _this$customResponse !== void 0 && _this$customResponse.trim())) {\n          console.warn('Cannot submit: Custom response is empty');\n          return;\n        }\n        console.log(`Submitting custom response: \"${this.customResponse}\"`);\n        // Mark as submitted BEFORE sending to avoid race conditions\n        this.hasSubmitted = true;\n        // Send custom response\n        this.gameService.submitResponse(this.customResponse.trim(), -1).then(() => {\n          console.log('Custom response submitted successfully');\n        }).catch(error => {\n          console.error('Error submitting custom response:', error);\n          this.hasSubmitted = false; // Reset submission state on error\n        });\n      }\n      // Reset selection after submission is initiated\n      console.log('Response submission initiated, resetting selection state');\n      this.selectedResponseIndex = -1;\n      this.selectedResponse = null;\n      this.selectedResponseText = null;\n      this.lastSelectedResponseIndex = -1;\n    } catch (error) {\n      // If there was an error, revert the submitted state\n      console.error('Error submitting response:', error);\n      this.hasSubmitted = false;\n    }\n  }\n  // Select a response (for player)\n  selectResponse(index) {\n    if (index < 0 || index >= this.playerResponses.length) {\n      console.warn(`Invalid response index: ${index}, max index: ${this.playerResponses.length - 1}`);\n      return;\n    }\n    this.selectedResponseIndex = index;\n    this.selectedResponse = this.playerResponses[index];\n    this.selectedResponseText = this.playerResponses[index];\n    // Store the last selected index for restoration after updates\n    this.lastSelectedResponseIndex = index;\n    console.log(`Selected response at index ${index}: \"${this.selectedResponseText}\"`);\n  }\n  // Select a winner response (for judge)\n  selectWinnerResponse(index) {\n    this.selectedWinnerIndex = index;\n    console.log(`Selected winner response at index ${index}: ${this.getSubmittedResponses()[index].text}`);\n  }\n  // Select a winner (judge only)\n  selectWinner() {\n    if (this.selectedWinnerIndex === null) return;\n    this.gameService.selectWinner(this.selectedWinnerIndex);\n  }\n  // Skip judging (no winner)\n  skipJudging() {\n    this.gameService.skipJudging();\n  }\n  // Start next round (leader only)\n  nextRound() {\n    // Reset hidePrompt flag when starting a new round\n    this.hidePrompt = false;\n    console.log('Resetting hidePrompt flag when starting a new round');\n    this.gameService.startNewGame();\n  }\n  // Play again with same players (leader only)\n  playAgain() {\n    // Reset hidePrompt flag when playing again\n    this.hidePrompt = false;\n    console.log('Resetting hidePrompt flag when playing again');\n    this.gameService.resetGame();\n  }\n  // End the game (leader only)\n  endGame() {\n    this.gameService.closeGame();\n  }\n  // Leave the game (any player)\n  leaveGame() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const alert = yield _this.alertController.create({\n        header: 'Leave Game',\n        message: 'Are you sure you want to leave the game?',\n        buttons: [{\n          text: 'No',\n          role: 'cancel',\n          cssClass: 'secondary',\n          handler: () => {\n            console.log('Leave game cancelled');\n          }\n        }, {\n          text: 'Yes',\n          handler: () => {\n            console.log('Confirming leave game');\n            _this.gameService.leaveGame().then(() => {\n              _this.router.navigate(['/home']);\n            });\n          }\n        }]\n      });\n      yield alert.present();\n    })();\n  }\n  // Get player score\n  getPlayerScore(playerName) {\n    if (!this.gameState || !this.gameState.player_scores) return 0;\n    return this.gameState.player_scores[playerName] || 0;\n  }\n  // Get the game winner\n  getGameWinner() {\n    if (!this.gameState || !this.gameState.player_scores || !this.gameState.players) {\n      return 'Unknown';\n    }\n    let highestScore = 0;\n    let winner = 'Unknown';\n    for (const player of this.gameState.players) {\n      const score = this.getPlayerScore(player);\n      if (score > highestScore) {\n        highestScore = score;\n        winner = player;\n      }\n    }\n    return winner;\n  }\n  // Safe access methods to prevent null errors\n  getPlayers() {\n    var _this$gameState2;\n    return ((_this$gameState2 = this.gameState) === null || _this$gameState2 === void 0 ? void 0 : _this$gameState2.players) || [];\n  }\n  getCurrentRound() {\n    var _this$gameState3;\n    return ((_this$gameState3 = this.gameState) === null || _this$gameState3 === void 0 ? void 0 : _this$gameState3.current_round) || 1;\n  }\n  getPrompt() {\n    var _this$gameState4, _this$gameState5, _this$gameState6, _this$gameState7;\n    // If hidePrompt is true, return empty string to hide the prompt\n    if (this.hidePrompt) {\n      console.log('getPrompt() returning empty string because hidePrompt is true');\n      return '';\n    }\n    // If we're in Winner Chosen state, hide the prompt\n    if (((_this$gameState4 = this.gameState) === null || _this$gameState4 === void 0 ? void 0 : _this$gameState4.status) === 'Winner Chosen') {\n      console.log('getPrompt() returning empty string because we are in Winner Chosen state');\n      return '';\n    }\n    console.log('GameState in getPrompt():', (_this$gameState5 = this.gameState) === null || _this$gameState5 === void 0 ? void 0 : _this$gameState5.status);\n    console.log('Current prompt object:', (_this$gameState6 = this.gameState) === null || _this$gameState6 === void 0 ? void 0 : _this$gameState6.current_prompt);\n    const prompt = ((_this$gameState7 = this.gameState) === null || _this$gameState7 === void 0 || (_this$gameState7 = _this$gameState7.current_prompt) === null || _this$gameState7 === void 0 ? void 0 : _this$gameState7.prompt) || '';\n    console.log('PROMPT TEXT FROM FIREBASE:', prompt);\n    // If we have a prompt, return it (regardless of state)\n    if (prompt) {\n      return prompt;\n    }\n    // Otherwise return an empty string\n    return '';\n  }\n  getSubmittedResponses() {\n    var _this$gameState8;\n    return ((_this$gameState8 = this.gameState) === null || _this$gameState8 === void 0 ? void 0 : _this$gameState8.submitted_responses) || [];\n  }\n  // Getters\n  get isLeader() {\n    return this.gameService.isLeader;\n  }\n  get playerName() {\n    return this.gameService.playerName || 'Unknown';\n  }\n  get roomCode() {\n    var _this$gameState9;\n    return ((_this$gameState9 = this.gameState) === null || _this$gameState9 === void 0 ? void 0 : _this$gameState9.room_code) || '';\n  }\n  // Create a new method to handle player responses update\n  handlePlayerResponsesUpdate(responses) {\n    console.log('Handling player responses update:', responses);\n    console.log('IMPORTANT - Current state of hasSubmitted:', this.hasSubmitted);\n    // Store current selection and state before updating\n    const previousSelectedText = this.selectedResponseText;\n    const previousSelectedIndex = this.selectedResponseIndex;\n    const currentResponseMode = this.responseMode;\n    const currentCustomResponse = this.customResponse;\n    // FIXED: Always process response updates to ensure cards are properly removed\n    // We no longer skip updates when the player has submitted\n    // Log the current state before updating\n    console.log('Current state before update:', {\n      previousSelectedText,\n      previousSelectedIndex,\n      responseMode: currentResponseMode,\n      customResponse: currentCustomResponse,\n      hasSubmitted: this.hasSubmitted,\n      currentResponses: [...this.playerResponses]\n    });\n    // Update the responses array\n    this.playerResponses = [...responses]; // Create a new array to ensure change detection\n    // If we had a previous selection, try to restore it\n    if (previousSelectedText && previousSelectedIndex >= 0 && currentResponseMode === 'cards') {\n      console.log('Attempting to restore previous card selection:', previousSelectedText);\n      // First try to find the exact same text\n      const newIndex = this.playerResponses.findIndex(text => text === previousSelectedText);\n      if (newIndex >= 0) {\n        console.log('Found matching text at new index:', newIndex);\n        this.selectedResponseIndex = newIndex;\n        this.selectedResponse = previousSelectedText;\n        this.selectedResponseText = previousSelectedText; // Ensure this is also updated\n      }\n      // If not found but the previous index is still valid, use it\n      else if (previousSelectedIndex < this.playerResponses.length) {\n        console.log('Using previous index with new text:', previousSelectedIndex);\n        this.selectedResponseIndex = previousSelectedIndex;\n        this.selectedResponse = this.playerResponses[previousSelectedIndex];\n        this.selectedResponseText = this.selectedResponse;\n      }\n      // Otherwise reset selection\n      else {\n        console.log('Could not restore selection, resetting');\n        this.selectedResponseIndex = -1;\n        this.selectedResponse = null;\n        this.selectedResponseText = null;\n      }\n    }\n    // Log the state after updating\n    console.log('State after update:', {\n      selectedResponseIndex: this.selectedResponseIndex,\n      selectedResponse: this.selectedResponse,\n      selectedResponseText: this.selectedResponseText,\n      responseMode: this.responseMode,\n      customResponse: this.customResponse,\n      playerResponses: [...this.playerResponses]\n    });\n  }\n}\n_GamePage = GamePage;\n_GamePage.ɵfac = function GamePage_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _GamePage)(i0.ɵɵdirectiveInject(i1.GameService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.AlertController));\n};\n_GamePage.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: _GamePage,\n  selectors: [[\"app-game\"]],\n  standalone: false,\n  decls: 17,\n  vars: 10,\n  consts: [[3, \"translucent\"], [\"color\", \"primary\"], [\"slot\", \"end\"], [3, \"click\"], [\"slot\", \"icon-only\", \"name\", \"exit-outline\"], [1, \"ion-padding\", 3, \"fullscreen\"], [\"class\", \"loading-container ion-text-center\", 4, \"ngIf\"], [\"class\", \"lobby-container\", 4, \"ngIf\"], [\"class\", \"game-container\", 4, \"ngIf\"], [1, \"loading-container\", \"ion-text-center\"], [1, \"lobby-container\"], [1, \"ion-text-center\"], [\"lines\", \"none\", 4, \"ngFor\", \"ngForOf\"], [\"expand\", \"block\", \"class\", \"ion-margin-top\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"ion-text-center\", 4, \"ngIf\"], [\"lines\", \"none\"], [\"slot\", \"end\", 4, \"ngIf\"], [\"expand\", \"block\", 1, \"ion-margin-top\", 3, \"click\"], [1, \"game-container\"], [1, \"prompt-container\", \"ion-margin-vertical\"], [\"class\", \"judge-container ion-text-center\", 4, \"ngIf\"], [1, \"judge-container\", \"ion-text-center\"], [1, \"prompt\", \"ion-text-center\"], [\"class\", \"responses-container\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"responses-container\"], [\"class\", \"ion-text-center ion-padding\", 4, \"ngIf\"], [\"class\", \"response-options\", 4, \"ngIf\"], [\"expand\", \"block\", \"class\", \"ion-margin-top\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [1, \"ion-text-center\", \"ion-padding\"], [1, \"response-options\"], [3, \"ngModelChange\", \"ionChange\", \"ngModel\"], [\"value\", \"cards\"], [\"value\", \"custom\"], [\"class\", \"custom-input-container ion-margin-vertical\", 4, \"ngIf\"], [\"lines\", \"none\", \"button\", \"\", 3, \"selected-response\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"lines\", \"none\", \"button\", \"\", 3, \"click\"], [1, \"custom-input-container\", \"ion-margin-vertical\"], [\"position\", \"stacked\"], [\"placeholder\", \"Type your response here...\", \"maxlength\", \"150\", \"rows\", \"3\", 1, \"custom-response-input\", 3, \"ngModelChange\", \"ngModel\", \"counter\"], [\"expand\", \"block\", 1, \"ion-margin-top\", 3, \"click\", \"disabled\"], [\"class\", \"judge-container\", 4, \"ngIf\"], [\"class\", \"waiting-container ion-text-center\", 4, \"ngIf\"], [1, \"judge-container\"], [\"expand\", \"block\", \"color\", \"medium\", 1, \"ion-margin-top\", 3, \"click\"], [1, \"waiting-container\", \"ion-text-center\"], [\"class\", \"prompt-container ion-margin-vertical\", 4, \"ngIf\"], [1, \"winner-container\", \"ion-text-center\", \"ion-margin-vertical\"], [1, \"winning-response\"], [1, \"ion-text-center\", \"ion-margin-vertical\"], [\"class\", \"leader-options\", 4, \"ngIf\"], [1, \"leader-options\"], [\"expand\", \"block\", \"color\", \"danger\", 1, \"ion-margin-top\", 3, \"click\"]],\n  template: function GamePage_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"ion-header\", 0)(1, \"ion-toolbar\", 1)(2, \"ion-title\");\n      i0.ɵɵtext(3, \" Untitled Card Game \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(4, \"ion-buttons\", 2)(5, \"ion-button\", 3);\n      i0.ɵɵlistener(\"click\", function GamePage_Template_ion_button_click_5_listener() {\n        return ctx.leaveGame();\n      });\n      i0.ɵɵelement(6, \"ion-icon\", 4);\n      i0.ɵɵtext(7, \" Leave \");\n      i0.ɵɵelementEnd()()()();\n      i0.ɵɵelementStart(8, \"ion-content\", 5);\n      i0.ɵɵtemplate(9, GamePage_div_9_Template, 3, 0, \"div\", 6)(10, GamePage_div_10_Template, 14, 5, \"div\", 7)(11, GamePage_div_11_Template, 9, 2, \"div\", 8)(12, GamePage_div_12_Template, 9, 3, \"div\", 8)(13, GamePage_div_13_Template, 10, 4, \"div\", 8)(14, GamePage_div_14_Template, 5, 3, \"div\", 8)(15, GamePage_div_15_Template, 17, 6, \"div\", 8)(16, GamePage_div_16_Template, 15, 4, \"div\", 8);\n      i0.ɵɵelementEnd();\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"translucent\", true);\n      i0.ɵɵadvance(8);\n      i0.ɵɵproperty(\"fullscreen\", true);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", !ctx.gameState);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", (ctx.gameState == null ? null : ctx.gameState.status) === \"Waiting for Players\");\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", (ctx.gameState == null ? null : ctx.gameState.status) === \"Waiting for Prompt\");\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", (ctx.gameState == null ? null : ctx.gameState.status) === \"Ready\");\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", (ctx.gameState == null ? null : ctx.gameState.status) === \"Waiting for Player Responses\");\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", (ctx.gameState == null ? null : ctx.gameState.status) === \"Ready for Judging\");\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", (ctx.gameState == null ? null : ctx.gameState.status) === \"Winner Chosen\");\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", (ctx.gameState == null ? null : ctx.gameState.status) === \"Game Over\");\n    }\n  },\n  dependencies: [i4.NgForOf, i4.NgIf, i5.NgControlStatus, i5.MaxLengthValidator, i5.NgModel, i3.IonButton, i3.IonButtons, i3.IonContent, i3.IonHeader, i3.IonIcon, i3.IonItem, i3.IonLabel, i3.IonList, i3.IonListHeader, i3.IonNote, i3.IonSegment, i3.IonSegmentButton, i3.IonTextarea, i3.IonTitle, i3.IonToolbar, i3.SelectValueAccessor, i3.TextValueAccessor],\n  styles: [\".loading-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  height: 100%;\\n}\\n\\n.prompt[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  font-weight: bold;\\n  padding: 15px;\\n  background-color: var(--ion-color-light);\\n  border-radius: 8px;\\n  margin: 10px 0;\\n}\\n\\n.winning-response[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  font-style: italic;\\n  padding: 15px;\\n  background-color: var(--ion-color-success-tint);\\n  border-radius: 8px;\\n  margin: 10px 0;\\n  color: var(--ion-color-success-contrast);\\n}\\n\\n.error-message[_ngcontent-%COMP%] {\\n  color: var(--ion-color-danger);\\n  font-size: 0.8rem;\\n  margin: 5px 0 0 16px;\\n}\\n\\n.lobby-container[_ngcontent-%COMP%], .game-container[_ngcontent-%COMP%] {\\n  max-width: 600px;\\n  margin: 0 auto;\\n}\\n\\n.selected-response[_ngcontent-%COMP%] {\\n  --background: var(--ion-color-primary-tint);\\n  --color: var(--ion-color-primary-contrast);\\n}\\n.selected-response[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%] {\\n  font-weight: bold;\\n}\\n.selected-response[_ngcontent-%COMP%]::part(native) {\\n  border-left: 4px solid var(--ion-color-primary);\\n}\\n\\nion-item[button][_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  transition: background-color 0.2s ease;\\n}\\nion-item[button][_ngcontent-%COMP%]:hover:not(.selected-response) {\\n  --background: var(--ion-color-light-shade);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvZ2FtZS9nYW1lLnBhZ2Uuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGFBQUE7RUFDQSxzQkFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7RUFDQSxZQUFBO0FBQ0Y7O0FBRUE7RUFDRSxpQkFBQTtFQUNBLGlCQUFBO0VBQ0EsYUFBQTtFQUNBLHdDQUFBO0VBQ0Esa0JBQUE7RUFDQSxjQUFBO0FBQ0Y7O0FBRUE7RUFDRSxpQkFBQTtFQUNBLGtCQUFBO0VBQ0EsYUFBQTtFQUNBLCtDQUFBO0VBQ0Esa0JBQUE7RUFDQSxjQUFBO0VBQ0Esd0NBQUE7QUFDRjs7QUFFQTtFQUNFLDhCQUFBO0VBQ0EsaUJBQUE7RUFDQSxvQkFBQTtBQUNGOztBQUVBO0VBQ0UsZ0JBQUE7RUFDQSxjQUFBO0FBQ0Y7O0FBR0E7RUFDRSwyQ0FBQTtFQUNBLDBDQUFBO0FBQUY7QUFFRTtFQUNFLGlCQUFBO0FBQUo7QUFHRTtFQUNFLCtDQUFBO0FBREo7O0FBTUE7RUFDRSxlQUFBO0VBQ0Esc0NBQUE7QUFIRjtBQUtFO0VBQ0UsMENBQUE7QUFISiIsInNvdXJjZXNDb250ZW50IjpbIi5sb2FkaW5nLWNvbnRhaW5lciB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICBoZWlnaHQ6IDEwMCU7XG59XG5cbi5wcm9tcHQge1xuICBmb250LXNpemU6IDEuMnJlbTtcbiAgZm9udC13ZWlnaHQ6IGJvbGQ7XG4gIHBhZGRpbmc6IDE1cHg7XG4gIGJhY2tncm91bmQtY29sb3I6IHZhcigtLWlvbi1jb2xvci1saWdodCk7XG4gIGJvcmRlci1yYWRpdXM6IDhweDtcbiAgbWFyZ2luOiAxMHB4IDA7XG59XG5cbi53aW5uaW5nLXJlc3BvbnNlIHtcbiAgZm9udC1zaXplOiAxLjFyZW07XG4gIGZvbnQtc3R5bGU6IGl0YWxpYztcbiAgcGFkZGluZzogMTVweDtcbiAgYmFja2dyb3VuZC1jb2xvcjogdmFyKC0taW9uLWNvbG9yLXN1Y2Nlc3MtdGludCk7XG4gIGJvcmRlci1yYWRpdXM6IDhweDtcbiAgbWFyZ2luOiAxMHB4IDA7XG4gIGNvbG9yOiB2YXIoLS1pb24tY29sb3Itc3VjY2Vzcy1jb250cmFzdCk7XG59XG5cbi5lcnJvci1tZXNzYWdlIHtcbiAgY29sb3I6IHZhcigtLWlvbi1jb2xvci1kYW5nZXIpO1xuICBmb250LXNpemU6IDAuOHJlbTtcbiAgbWFyZ2luOiA1cHggMCAwIDE2cHg7XG59XG5cbi5sb2JieS1jb250YWluZXIsIC5nYW1lLWNvbnRhaW5lciB7XG4gIG1heC13aWR0aDogNjAwcHg7XG4gIG1hcmdpbjogMCBhdXRvO1xufVxuXG4vLyBTdHlsZSBmb3Igc2VsZWN0ZWQgcmVzcG9uc2VzXG4uc2VsZWN0ZWQtcmVzcG9uc2Uge1xuICAtLWJhY2tncm91bmQ6IHZhcigtLWlvbi1jb2xvci1wcmltYXJ5LXRpbnQpO1xuICAtLWNvbG9yOiB2YXIoLS1pb24tY29sb3ItcHJpbWFyeS1jb250cmFzdCk7XG5cbiAgaW9uLWxhYmVsIHtcbiAgICBmb250LXdlaWdodDogYm9sZDtcbiAgfVxuXG4gICY6OnBhcnQobmF0aXZlKSB7XG4gICAgYm9yZGVyLWxlZnQ6IDRweCBzb2xpZCB2YXIoLS1pb24tY29sb3ItcHJpbWFyeSk7XG4gIH1cbn1cblxuLy8gQWRkIGhvdmVyIGVmZmVjdCBmb3IgY2xpY2thYmxlIHJlc3BvbnNlc1xuaW9uLWl0ZW1bYnV0dG9uXSB7XG4gIGN1cnNvcjogcG9pbnRlcjtcbiAgdHJhbnNpdGlvbjogYmFja2dyb3VuZC1jb2xvciAwLjJzIGVhc2U7XG5cbiAgJjpob3Zlcjpub3QoLnNlbGVjdGVkLXJlc3BvbnNlKSB7XG4gICAgLS1iYWNrZ3JvdW5kOiB2YXIoLS1pb24tY29sb3ItbGlnaHQtc2hhZGUpO1xuICB9XG59XG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n});", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "GamePage_div_10_ion_item_11_ion_note_3_Template", "ɵɵadvance", "ɵɵtextInterpolate", "player_r1", "ɵɵproperty", "ctx_r1", "<PERSON><PERSON><PERSON>", "ɵɵlistener", "GamePage_div_10_ion_button_12_Template_ion_button_click_0_listener", "ɵɵrestoreView", "_r3", "ɵɵnextContext", "ɵɵresetView", "startGame", "GamePage_div_10_ion_item_11_Template", "GamePage_div_10_ion_button_12_Template", "GamePage_div_10_p_13_Template", "roomCode", "ɵɵtextInterpolate1", "getPlayers", "length", "<PERSON><PERSON><PERSON><PERSON>", "GamePage_div_11_div_8_Template", "getCurrentRound", "isJudge", "GamePage_div_12_div_8_Template", "getPrompt", "ɵɵtextInterpolate2", "gameState", "submitted_responses", "players", "GamePage_div_13_div_8_p_5_Template", "GamePage_div_13_div_9_div_4_p_5_Template", "GamePage_div_13_div_9_div_5_ion_list_8_ion_item_1_Template_ion_item_click_0_listener", "i_r6", "_r5", "index", "selectResponse", "ɵɵclassProp", "selectedResponseIndex", "response_r7", "GamePage_div_13_div_9_div_5_ion_list_8_ion_item_1_Template", "playerResponses", "ɵɵtwoWayListener", "GamePage_div_13_div_9_div_5_div_9_Template_ion_textarea_ngModelChange_4_listener", "$event", "_r8", "ɵɵtwoWayBindingSet", "customResponse", "ɵɵtwoWayProperty", "GamePage_div_13_div_9_div_5_Template_ion_segment_ngModelChange_1_listener", "_r4", "responseMode", "GamePage_div_13_div_9_div_5_Template_ion_segment_ionChange_1_listener", "responseTypeChanged", "GamePage_div_13_div_9_div_5_ion_list_8_Template", "GamePage_div_13_div_9_div_5_div_9_Template", "GamePage_div_13_div_9_ion_button_6_Template_ion_button_click_0_listener", "_r9", "submitResponse", "trim", "GamePage_div_13_div_9_div_3_Template", "GamePage_div_13_div_9_div_4_Template", "GamePage_div_13_div_9_div_5_Template", "GamePage_div_13_div_9_ion_button_6_Template", "hasSubmitted", "GamePage_div_13_div_8_Template", "GamePage_div_13_div_9_Template", "GamePage_div_14_div_3_ion_item_4_Template_ion_item_click_0_listener", "i_r12", "_r11", "selectWinnerResponse", "selectedWinnerIndex", "response_r13", "text", "GamePage_div_14_div_3_ion_item_4_Template", "GamePage_div_14_div_3_Template_ion_button_click_5_listener", "_r10", "<PERSON><PERSON><PERSON><PERSON>", "GamePage_div_14_div_3_Template_ion_button_click_7_listener", "skipJudging", "getSubmittedResponses", "GamePage_div_14_div_3_Template", "GamePage_div_14_div_4_Template", "player_r14", "getPlayerScore", "GamePage_div_15_div_3_Template", "GamePage_div_15_ion_item_13_Template", "winner", "winning_response", "nextRoundCountdown", "player_r15", "GamePage_div_16_div_13_Template_ion_button_click_3_listener", "_r16", "playAgain", "GamePage_div_16_div_13_Template_ion_button_click_5_listener", "endGame", "GamePage_div_16_ion_item_12_Template", "GamePage_div_16_div_13_Template", "GamePage_div_16_div_14_Template", "game_winner", "getGame<PERSON>inner", "GamePage", "constructor", "gameService", "router", "alertController", "selectedResponse", "hidePrompt", "lastSelectedResponseIndex", "selectedResponseText", "isProcessingGameStateUpdate", "lastCustomResponse", "lastResponseMode", "gameStateSubscription", "playerResponsesSubscription", "isJudgeSubscription", "previousGameState", "ngOnInit", "console", "log", "gameState$", "subscribe", "state", "status", "current_prompt", "prompt", "_this$gameState", "submittedResponses", "hasAlreadySubmitted", "some", "response", "player_name", "previousRound", "current_round", "currentRound", "navigate", "currentHasSubmitted", "newHasSubmitted", "responseCount", "playerResponses$", "responses", "currentResponseMode", "currentCustomResponse", "wasSubmitted", "handlePlayerResponsesUpdate", "isJudge$", "ngOnDestroy", "unsubscribe", "clearNextRoundCountdown", "startNextRoundCountdown", "countdownInterval", "setInterval", "startNewGame", "clearInterval", "warn", "responseText", "then", "updatedResponses", "getCurrentResponses", "catch", "error", "_this$customResponse", "nextRound", "resetGame", "closeGame", "leaveGame", "_this", "_asyncToGenerator", "alert", "create", "header", "message", "buttons", "role", "cssClass", "handler", "present", "player_scores", "highestScore", "player", "score", "_this$gameState2", "_this$gameState3", "_this$gameState4", "_this$gameState5", "_this$gameState6", "_this$gameState7", "_this$gameState8", "_this$gameState9", "room_code", "previousSelectedText", "previousSelectedIndex", "currentResponses", "newIndex", "findIndex", "ɵɵdirectiveInject", "i1", "GameService", "i2", "Router", "i3", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectors", "standalone", "decls", "vars", "consts", "template", "GamePage_Template", "rf", "ctx", "GamePage_Template_ion_button_click_5_listener", "ɵɵelement", "GamePage_div_9_Template", "GamePage_div_10_Template", "GamePage_div_11_Template", "GamePage_div_12_Template", "GamePage_div_13_Template", "GamePage_div_14_Template", "GamePage_div_15_Template", "GamePage_div_16_Template"], "sources": ["D:\\Godot_Games\\cjsc\\cjsc-web-client\\src\\app\\game\\game.page.ts", "D:\\Godot_Games\\cjsc\\cjsc-web-client\\src\\app\\game\\game.page.html"], "sourcesContent": ["import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AlertController } from '@ionic/angular';\nimport { GameService } from '../services/game.service';\nimport { Subscription } from 'rxjs';\nimport { GameState } from '../services/game.service';\n\n@Component({\n  selector: 'app-game',\n  templateUrl: './game.page.html',\n  styleUrls: ['./game.page.scss'],\n  standalone: false,\n})\nexport class GamePage implements OnInit, OnDestroy {\n  gameState: GameState | null = null;\n  playerResponses: string[] = [];\n  isJudge: boolean = false;\n  selectedResponse: string | null = null;\n  selectedResponseIndex: number = -1; // Track the index of the selected response\n  selectedWinnerIndex: number | null = null;\n  hasSubmitted: boolean = false;\n  nextRoundCountdown: number = 8; // Countdown timer for next round (increased to allow Godot prompt selection)\n\n  // New variables for custom response feature\n  responseMode: string = 'cards'; // 'cards' or 'custom'\n  customResponse: string = '';\n\n  // Add a new property to track if prompt should be hidden\n  hidePrompt: boolean = false;\n\n  // Add a new property to track the last selected response index\n  private lastSelectedResponseIndex: number = -1;\n\n  // Add these properties to track selection state more reliably\n  private selectedResponseText: string | null = null;\n  private isProcessingGameStateUpdate: boolean = false;\n\n  // Add properties to track custom response state\n  private lastCustomResponse: string = '';\n  private lastResponseMode: string = 'cards';\n\n  private gameStateSubscription: Subscription | null = null;\n  private playerResponsesSubscription: Subscription | null = null;\n  private isJudgeSubscription: Subscription | null = null;\n  private previousGameState: string | null = null;\n\n  constructor(\n    private gameService: GameService,\n    private router: Router,\n    private alertController: AlertController\n  ) {}\n\n  private countdownInterval: any;\n\n  ngOnInit() {\n    console.log('GamePage ngOnInit called');\n\n    // Subscribe to game state changes\n    this.gameStateSubscription = this.gameService.gameState$.subscribe(state => {\n      console.log('GamePage received game state update:', state ? `Status: ${state.status}` : 'null');\n\n      this.isProcessingGameStateUpdate = true;\n\n      // Update the gameState property\n      this.gameState = state;\n\n      this.previousGameState = state?.status || null;\n\n      // Hide prompt when transitioning to Winner Chosen\n      if (state && state.status === 'Winner Chosen') {\n        this.hidePrompt = true;\n        console.log('Hiding prompt because status is Winner Chosen');\n      }\n\n      // Show prompt when we have a new prompt in any state that should display it\n      if (state && state.current_prompt && state.current_prompt.prompt) {\n        // Show prompt for these states\n        if (state.status === 'Waiting for Prompt' ||\n            state.status === 'Ready' ||\n            state.status === 'Waiting for Player Responses' ||\n            state.status === 'Ready for Judging') {\n          this.hidePrompt = false;\n          console.log('Showing prompt in', state.status, 'state with prompt:', state.current_prompt.prompt);\n        }\n      }\n\n      // Hide prompt when waiting for a new prompt (but no prompt available yet)\n      if (state && state.status === 'Waiting for Prompt' && (!state.current_prompt || !state.current_prompt.prompt)) {\n        this.hidePrompt = true;\n        console.log('Hiding prompt because status is Waiting for Prompt and no prompt available');\n      }\n\n      // Reset submission state when game status changes\n      if (state && state.status === 'Waiting for Player Responses') {\n        // Check if the player has already submitted a response\n        const playerName = this.playerName;\n        const submittedResponses = state.submitted_responses || [];\n\n        // Check if this player has already submitted a response\n        const hasAlreadySubmitted = submittedResponses.some(\n          response => response.player_name === playerName\n        );\n\n        // IMPORTANT: Update hasSubmitted based on the game state\n        // This ensures it correctly reflects whether the player has submitted\n        // regardless of local state\n        this.hasSubmitted = hasAlreadySubmitted;\n        console.log(`Setting hasSubmitted=${hasAlreadySubmitted} based on game state`);\n\n        // Track the previous round to detect round changes\n        const previousRound = this.gameState?.current_round || 0;\n        const currentRound = state.current_round || 0;\n        \n        // If we're in a new round, make sure hasSubmitted is reset and log current responses\n        if (previousRound !== currentRound && previousRound > 0) {\n          console.log(`Round changed from ${previousRound} to ${currentRound}, resetting hasSubmitted`);\n          console.log(`Current responses at round change:`, [...this.playerResponses]);\n          this.hasSubmitted = false;\n        }\n\n        if (!hasAlreadySubmitted) {\n          // Only reset selection if the player hasn't made a selection yet\n          // or if the responses have changed\n          if (this.lastSelectedResponseIndex === -1 ||\n              (this.lastSelectedResponseIndex >= 0 &&\n               this.lastSelectedResponseIndex < this.playerResponses.length &&\n               this.playerResponses[this.lastSelectedResponseIndex] !== this.selectedResponse)) {\n            this.selectedResponse = null;\n            this.selectedResponseIndex = -1;\n          } else {\n            // Restore the last selected index if it's still valid\n            this.selectedResponseIndex = this.lastSelectedResponseIndex;\n            if (this.selectedResponseIndex >= 0 && this.selectedResponseIndex < this.playerResponses.length) {\n              this.selectedResponse = this.playerResponses[this.selectedResponseIndex];\n            }\n          }\n          // Don't reset custom response or response mode here\n          // This was causing the issue where custom responses were lost when other players submitted\n        }\n      }\n\n      // Reset winner selection when game status changes\n      if (state && state.status === 'Ready for Judging') {\n        this.selectedWinnerIndex = null;\n      }\n\n      // Handle game closed state\n      if (state && state.status === 'Closed') {\n        this.router.navigate(['/home']);\n      }\n\n      // Remove web client countdown - let Godot client handle the countdown and new round logic\n      // The Godot client will now handle the countdown and automatically start new rounds\n\n      // If in response phase, handle selection state based on submission status\n      if (state && state.status === 'Waiting for Player Responses') {\n        // Check if the player has already submitted a response\n        const playerName = this.playerName;\n        const submittedResponses = state.submitted_responses || [];          // Check if this player has already submitted a response\n          const hasAlreadySubmitted = submittedResponses.some(\n            response => response.player_name === playerName\n          );\n\n          // Log the current selection state before updating\n          console.log('Current selection state before updating hasSubmitted:', {\n            currentHasSubmitted: this.hasSubmitted,\n            newHasSubmitted: hasAlreadySubmitted,\n            selectedResponseIndex: this.selectedResponseIndex,\n            selectedResponse: this.selectedResponse,\n            selectedResponseText: this.selectedResponseText,\n            responseCount: this.playerResponses.length\n          });\n\n        // Update submission status\n        this.hasSubmitted = hasAlreadySubmitted;\n\n        // Only reset if player has submitted\n        if (hasAlreadySubmitted) {\n          console.log('Player has submitted, resetting selection');\n          this.selectedResponseIndex = -1;\n          this.selectedResponse = null;\n          this.selectedResponseText = null;\n          this.lastSelectedResponseIndex = -1;\n        } else {\n          // If player hasn't submitted, preserve the current selection\n          console.log('Player has not submitted, preserving selection');\n          // No need to reset anything here, keep the current selection\n        }\n      }\n\n      this.isProcessingGameStateUpdate = false;\n    });\n\n    // Subscribe to player responses\n    this.playerResponsesSubscription = this.gameService.playerResponses$.subscribe(responses => {\n      console.log('Received player responses update:', responses);\n\n      // Store current response mode and custom response\n      const currentResponseMode = this.responseMode;\n      const currentCustomResponse = this.customResponse;\n\n      // FIXED: Always process response updates to ensure cards are properly removed\n      // Only skip in the middle of a game state update, but NOT when the player has submitted\n      // This ensures the array gets updated after submission\n      if (!this.isProcessingGameStateUpdate) {\n        console.log('Processing player responses update (not in game state update)');\n        \n        // Save the current submission state\n        const wasSubmitted = this.hasSubmitted;\n        \n        // Process the responses\n        this.handlePlayerResponsesUpdate(responses);\n\n        // Restore the submission state (in case handlePlayerResponsesUpdate changed it)\n        this.hasSubmitted = wasSubmitted;\n\n        // Restore response mode and custom response after update\n        if (currentResponseMode === 'custom') {\n          console.log('Restoring custom response mode and text:', currentCustomResponse);\n          this.responseMode = 'custom';\n          this.customResponse = currentCustomResponse;\n        }\n      } else {\n        console.log('Skipping player responses update due to game state update in progress');\n      }\n    });\n\n    // Subscribe to judge status\n    this.isJudgeSubscription = this.gameService.isJudge$.subscribe(isJudge => {\n      this.isJudge = isJudge;\n    });\n  }\n\n  ngOnDestroy() {\n    // Unsubscribe to prevent memory leaks\n    if (this.gameStateSubscription) {\n      this.gameStateSubscription.unsubscribe();\n    }\n    if (this.playerResponsesSubscription) {\n      this.playerResponsesSubscription.unsubscribe();\n    }\n    if (this.isJudgeSubscription) {\n      this.isJudgeSubscription.unsubscribe();\n    }\n\n    // Clear any active countdown\n    this.clearNextRoundCountdown();\n  }\n\n  // Handle response type change (cards/custom)\n  responseTypeChanged() {\n    console.log(`Response mode changed to: ${this.responseMode}`);\n\n    // Store the current response mode\n    this.lastResponseMode = this.responseMode;\n\n    // Reset selections when switching modes\n    if (this.responseMode === 'cards') {\n      // Store the custom response before clearing it\n      this.lastCustomResponse = this.customResponse;\n      this.customResponse = '';\n      // Don't reset selectedResponseIndex here\n    } else {\n      // Store the current selection before switching to custom mode\n      this.lastSelectedResponseIndex = this.selectedResponseIndex;\n      this.selectedResponseIndex = -1;\n    }\n\n    console.log(`Response mode change complete. Mode: ${this.responseMode}, Custom: ${this.customResponse}`);\n  }\n\n  // Start countdown timer for next round\n  startNextRoundCountdown() {\n    // Reset countdown to 8 seconds (increased to allow Godot prompt selection)\n    this.nextRoundCountdown = 8;\n\n    // Make sure prompt is hidden during countdown\n    this.hidePrompt = true;\n    console.log('Hiding prompt during next round countdown');\n\n    // Clear any existing interval\n    this.clearNextRoundCountdown();\n\n    // Start a new interval\n    this.countdownInterval = setInterval(() => {\n      this.nextRoundCountdown--;\n\n      // When countdown reaches 0, clear the interval and start new round\n      if (this.nextRoundCountdown <= 0) {\n        this.clearNextRoundCountdown();\n\n        // If this player is the leader, start a new round\n        if (this.isLeader) {\n          console.log('Countdown reached zero, leader starting new round...');\n          this.gameService.startNewGame();\n        }\n      }\n    }, 1000);\n  }\n\n  // Clear countdown timer\n  clearNextRoundCountdown() {\n    if (this.countdownInterval) {\n      clearInterval(this.countdownInterval);\n      this.countdownInterval = null;\n    }\n  }\n\n  // Start the game (leader only)\n  startGame() {\n    // Reset hidePrompt flag when starting a new game\n    this.hidePrompt = false;\n    console.log('Resetting hidePrompt flag when starting a new game');\n\n    this.gameService.startNewGame();\n  }\n\n  // Submit a response\n  submitResponse() {\n    try {\n      if (this.responseMode === 'cards') {\n        // Submit pre-defined card response\n        if (this.selectedResponseIndex < 0) {\n          console.warn('Cannot submit: No response selected');\n          return;\n        }\n\n        // Make sure we have the selected response text\n        const responseText = this.playerResponses[this.selectedResponseIndex];\n        if (!responseText) {\n          console.warn('Cannot submit: Selected response text is empty');\n          return;\n        }\n\n        console.log(`Submitting card response: \"${responseText}\" with index ${this.selectedResponseIndex}`);\n\n        // Mark as submitted BEFORE sending to avoid race conditions\n        this.hasSubmitted = true;\n\n        // Send both the response text and the index\n        this.gameService.submitResponse(responseText, this.selectedResponseIndex)\n          .then(() => {\n            // Track that this was the last submitted response\n            console.log('Card response submitted successfully');\n            \n            // Force update player responses from the service\n            const updatedResponses = this.gameService.getCurrentResponses();\n            if (updatedResponses.length > 0) {\n              console.log('Updated player responses after submission:', updatedResponses);\n              this.playerResponses = [...updatedResponses];\n            }\n          })\n          .catch(error => {\n            console.error('Error submitting response:', error);\n            this.hasSubmitted = false; // Reset submission state on error\n          });\n      } else {\n        // Submit custom text response\n        if (!this.customResponse?.trim()) {\n          console.warn('Cannot submit: Custom response is empty');\n          return;\n        }\n\n        console.log(`Submitting custom response: \"${this.customResponse}\"`);\n\n        // Mark as submitted BEFORE sending to avoid race conditions\n        this.hasSubmitted = true;\n\n        // Send custom response\n        this.gameService.submitResponse(this.customResponse.trim(), -1)\n          .then(() => {\n            console.log('Custom response submitted successfully');\n          })\n          .catch(error => {\n            console.error('Error submitting custom response:', error);\n            this.hasSubmitted = false; // Reset submission state on error\n          });\n      }\n\n      // Reset selection after submission is initiated\n      console.log('Response submission initiated, resetting selection state');\n      this.selectedResponseIndex = -1;\n      this.selectedResponse = null;\n      this.selectedResponseText = null;\n      this.lastSelectedResponseIndex = -1;\n    } catch (error) {\n      // If there was an error, revert the submitted state\n      console.error('Error submitting response:', error);\n      this.hasSubmitted = false;\n    }\n  }\n\n  // Select a response (for player)\n  selectResponse(index: number) {\n    if (index < 0 || index >= this.playerResponses.length) {\n      console.warn(`Invalid response index: ${index}, max index: ${this.playerResponses.length - 1}`);\n      return;\n    }\n\n    this.selectedResponseIndex = index;\n    this.selectedResponse = this.playerResponses[index];\n    this.selectedResponseText = this.playerResponses[index];\n\n    // Store the last selected index for restoration after updates\n    this.lastSelectedResponseIndex = index;\n\n    console.log(`Selected response at index ${index}: \"${this.selectedResponseText}\"`);\n  }\n\n  // Select a winner response (for judge)\n  selectWinnerResponse(index: number) {\n    this.selectedWinnerIndex = index;\n    console.log(`Selected winner response at index ${index}: ${this.getSubmittedResponses()[index].text}`);\n  }\n\n  // Select a winner (judge only)\n  selectWinner() {\n    if (this.selectedWinnerIndex === null) return;\n\n    this.gameService.selectWinner(this.selectedWinnerIndex);\n  }\n\n  // Skip judging (no winner)\n  skipJudging() {\n    this.gameService.skipJudging();\n  }\n\n  // Start next round (leader only)\n  nextRound() {\n    // Reset hidePrompt flag when starting a new round\n    this.hidePrompt = false;\n    console.log('Resetting hidePrompt flag when starting a new round');\n\n    this.gameService.startNewGame();\n  }\n\n  // Play again with same players (leader only)\n  playAgain() {\n    // Reset hidePrompt flag when playing again\n    this.hidePrompt = false;\n    console.log('Resetting hidePrompt flag when playing again');\n\n    this.gameService.resetGame();\n  }\n\n  // End the game (leader only)\n  endGame() {\n    this.gameService.closeGame();\n  }\n\n  // Leave the game (any player)\n  async leaveGame() {\n    const alert = await this.alertController.create({\n      header: 'Leave Game',\n      message: 'Are you sure you want to leave the game?',\n      buttons: [\n        {\n          text: 'No',\n          role: 'cancel',\n          cssClass: 'secondary',\n          handler: () => {\n            console.log('Leave game cancelled');\n          }\n        }, {\n          text: 'Yes',\n          handler: () => {\n            console.log('Confirming leave game');\n            this.gameService.leaveGame().then(() => {\n              this.router.navigate(['/home']);\n            });\n          }\n        }\n      ]\n    });\n\n    await alert.present();\n  }\n\n  // Get player score\n  getPlayerScore(playerName: string): number {\n    if (!this.gameState || !this.gameState.player_scores) return 0;\n    return this.gameState.player_scores[playerName] || 0;\n  }\n\n  // Get the game winner\n  getGameWinner(): string {\n    if (!this.gameState || !this.gameState.player_scores || !this.gameState.players) {\n      return 'Unknown';\n    }\n\n    let highestScore = 0;\n    let winner = 'Unknown';\n\n    for (const player of this.gameState.players) {\n      const score = this.getPlayerScore(player);\n      if (score > highestScore) {\n        highestScore = score;\n        winner = player;\n      }\n    }\n\n    return winner;\n  }\n\n  // Safe access methods to prevent null errors\n  getPlayers(): string[] {\n    return this.gameState?.players || [];\n  }\n\n  getCurrentRound(): number {\n    return this.gameState?.current_round || 1;\n  }\n\n  getPrompt(): string {\n    // If hidePrompt is true, return empty string to hide the prompt\n    if (this.hidePrompt) {\n      console.log('getPrompt() returning empty string because hidePrompt is true');\n      return '';\n    }\n\n    // If we're in Winner Chosen state, hide the prompt\n    if (this.gameState?.status === 'Winner Chosen') {\n      console.log('getPrompt() returning empty string because we are in Winner Chosen state');\n      return '';\n    }\n\n    console.log('GameState in getPrompt():', this.gameState?.status);\n    console.log('Current prompt object:', this.gameState?.current_prompt);\n\n    const prompt = this.gameState?.current_prompt?.prompt || '';\n    console.log('PROMPT TEXT FROM FIREBASE:', prompt);\n\n    // If we have a prompt, return it (regardless of state)\n    if (prompt) {\n      return prompt;\n    }\n\n    // Otherwise return an empty string\n    return '';\n  }\n\n  getSubmittedResponses(): any[] {\n    return this.gameState?.submitted_responses || [];\n  }\n\n  // Getters\n  get isLeader(): boolean {\n    return this.gameService.isLeader;\n  }\n\n  get playerName(): string {\n    return this.gameService.playerName || 'Unknown';\n  }\n\n  get roomCode(): string {\n    return this.gameState?.room_code || '';\n  }\n\n  // Create a new method to handle player responses update\n  private handlePlayerResponsesUpdate(responses: string[]) {\n    console.log('Handling player responses update:', responses);\n    console.log('IMPORTANT - Current state of hasSubmitted:', this.hasSubmitted);\n\n    // Store current selection and state before updating\n    const previousSelectedText = this.selectedResponseText;\n    const previousSelectedIndex = this.selectedResponseIndex;\n    const currentResponseMode = this.responseMode;\n    const currentCustomResponse = this.customResponse;\n\n    // FIXED: Always process response updates to ensure cards are properly removed\n    // We no longer skip updates when the player has submitted\n\n    // Log the current state before updating\n    console.log('Current state before update:', {\n      previousSelectedText,\n      previousSelectedIndex,\n      responseMode: currentResponseMode,\n      customResponse: currentCustomResponse,\n      hasSubmitted: this.hasSubmitted,\n      currentResponses: [...this.playerResponses]\n    });\n\n    // Update the responses array\n    this.playerResponses = [...responses]; // Create a new array to ensure change detection\n\n    // If we had a previous selection, try to restore it\n    if (previousSelectedText && previousSelectedIndex >= 0 && currentResponseMode === 'cards') {\n      console.log('Attempting to restore previous card selection:', previousSelectedText);\n\n      // First try to find the exact same text\n      const newIndex = this.playerResponses.findIndex(text => text === previousSelectedText);\n\n      if (newIndex >= 0) {\n        console.log('Found matching text at new index:', newIndex);\n        this.selectedResponseIndex = newIndex;\n        this.selectedResponse = previousSelectedText;\n        this.selectedResponseText = previousSelectedText; // Ensure this is also updated\n      }\n      // If not found but the previous index is still valid, use it\n      else if (previousSelectedIndex < this.playerResponses.length) {\n        console.log('Using previous index with new text:', previousSelectedIndex);\n        this.selectedResponseIndex = previousSelectedIndex;\n        this.selectedResponse = this.playerResponses[previousSelectedIndex];\n        this.selectedResponseText = this.selectedResponse;\n      }\n      // Otherwise reset selection\n      else {\n        console.log('Could not restore selection, resetting');\n        this.selectedResponseIndex = -1;\n        this.selectedResponse = null;\n        this.selectedResponseText = null;\n      }\n    }\n\n    // Log the state after updating\n    console.log('State after update:', {\n      selectedResponseIndex: this.selectedResponseIndex,\n      selectedResponse: this.selectedResponse,\n      selectedResponseText: this.selectedResponseText,\n      responseMode: this.responseMode,\n      customResponse: this.customResponse,\n      playerResponses: [...this.playerResponses]\n    });\n  }\n}\n\n", "<ion-header [translucent]=\"true\">\n  <ion-toolbar color=\"primary\">\n    <ion-title>\n      Untitled Card Game\n    </ion-title>\n    <ion-buttons slot=\"end\">\n      <ion-button (click)=\"leaveGame()\">\n        <ion-icon slot=\"icon-only\" name=\"exit-outline\"></ion-icon>\n        Leave\n      </ion-button>\n    </ion-buttons>\n  </ion-toolbar>\n</ion-header>\n\n<ion-content [fullscreen]=\"true\" class=\"ion-padding\">\n  <!-- Loading state -->\n  <div *ngIf=\"!gameState\" class=\"loading-container ion-text-center\">\n    <p>Loading game...</p>\n  </div>\n\n  <!-- Waiting for players state -->\n  <div *ngIf=\"gameState?.status === 'Waiting for Players'\" class=\"lobby-container\">\n    <h1 class=\"ion-text-center\">Waiting for Players</h1>\n    <p class=\"ion-text-center\">Room Code: <strong>{{ roomCode }}</strong></p>\n\n    <ion-list>\n      <ion-list-header>\n        <ion-label>Players ({{ getPlayers().length }})</ion-label>\n      </ion-list-header>\n      <ion-item *ngFor=\"let player of getPlayers()\" lines=\"none\">\n        <ion-label>{{ player }}</ion-label>\n        <ion-note slot=\"end\" *ngIf=\"player === playerName\">You</ion-note>\n      </ion-item>\n    </ion-list>\n\n    <ion-button\n      *ngIf=\"isLeader && getPlayers().length >= 3\"\n      expand=\"block\"\n      (click)=\"startGame()\"\n      class=\"ion-margin-top\">\n      Start Game\n    </ion-button>\n\n    <p *ngIf=\"isLeader && getPlayers().length < 3\" class=\"ion-text-center\">\n      Need at least 3 players to start the game.\n    </p>\n  </div>\n\n  <!-- Waiting for Prompt state -->\n  <div *ngIf=\"gameState?.status === 'Waiting for Prompt'\" class=\"game-container\">\n    <h1 class=\"ion-text-center\">Round {{ getCurrentRound() }}</h1>\n\n    <div class=\"prompt-container ion-margin-vertical\">\n      <h2 class=\"ion-text-center\">Waiting for prompt...</h2>\n      <p class=\"ion-text-center\">The game is selecting a prompt for this round</p>\n    </div>\n\n    <div class=\"judge-container ion-text-center\" *ngIf=\"isJudge\">\n      <h3>You are the judge for this round</h3>\n    </div>\n  </div>\n\n  <!-- Ready state -->\n  <div *ngIf=\"gameState?.status === 'Ready'\" class=\"game-container\">\n    <h1 class=\"ion-text-center\">Round {{ getCurrentRound() }}</h1>\n\n    <div class=\"prompt-container ion-margin-vertical\">\n      <h2 class=\"ion-text-center\">Prompt:</h2>\n      <p class=\"prompt ion-text-center\">{{ getPrompt() }}</p>\n    </div>\n\n    <div *ngIf=\"isJudge\" class=\"judge-container ion-text-center\">\n      <h3>You are the judge for this round</h3>\n      <p>Wait for players to submit their responses.</p>\n    </div>\n  </div>\n\n  <!-- Waiting for Player Responses state -->\n  <div *ngIf=\"gameState?.status === 'Waiting for Player Responses'\" class=\"game-container\">\n    <h1 class=\"ion-text-center\">Round {{ getCurrentRound() }}</h1>\n\n    <div class=\"prompt-container ion-margin-vertical\">\n      <h2 class=\"ion-text-center\">Prompt:</h2>\n      <p class=\"prompt ion-text-center\">{{ getPrompt() }}</p>\n    </div>\n\n    <div *ngIf=\"isJudge\" class=\"judge-container ion-text-center\">\n      <h3>You are the judge for this round</h3>\n      <p>Wait for players to submit their responses.</p>\n\n      <!-- Show how many players have submitted responses -->\n      <p *ngIf=\"gameState?.submitted_responses\">\n        {{ gameState?.submitted_responses?.length || 0 }} /\n        {{ (gameState?.players?.length || 0) - 1 }} players have submitted\n      </p>\n    </div>\n\n    <div *ngIf=\"!isJudge\" class=\"responses-container\">\n      <h3 class=\"ion-text-center\">Choose your response:</h3>\n\n      <div *ngIf=\"playerResponses.length === 0\" class=\"ion-text-center ion-padding\">\n        <p>Waiting for responses to be assigned...</p>\n      </div>\n\n      <div *ngIf=\"hasSubmitted\" class=\"ion-text-center ion-padding\">\n        <p>You have submitted your response.</p>\n        <p>Waiting for other players to submit their responses...</p>\n\n        <!-- Show how many players have submitted responses -->\n        <p *ngIf=\"gameState?.submitted_responses\">\n          {{ gameState?.submitted_responses?.length || 0 }} /\n          {{ (gameState?.players?.length || 0) - 1 }} players have submitted\n        </p>\n      </div>\n\n      <div *ngIf=\"!hasSubmitted\" class=\"response-options\">\n        <!-- Toggle between pre-defined cards and custom input -->\n        <ion-segment [(ngModel)]=\"responseMode\" (ionChange)=\"responseTypeChanged()\">\n          <ion-segment-button value=\"cards\">\n            <ion-label>Cards</ion-label>\n          </ion-segment-button>\n          <ion-segment-button value=\"custom\">\n            <ion-label>Custom</ion-label>\n          </ion-segment-button>\n        </ion-segment>\n\n        <!-- Pre-defined cards list -->\n        <ion-list *ngIf=\"responseMode === 'cards' && playerResponses.length > 0\">\n          <ion-item *ngFor=\"let response of playerResponses; let i = index\"\n                  [class.selected-response]=\"selectedResponseIndex === i\"\n                  (click)=\"selectResponse(i)\"\n                  lines=\"none\"\n                  button>\n            <ion-label>{{ response }}</ion-label>\n          </ion-item>\n        </ion-list>\n\n        <!-- Custom text input -->\n        <div *ngIf=\"responseMode === 'custom'\" class=\"custom-input-container ion-margin-vertical\">\n          <ion-item lines=\"none\">\n            <ion-label position=\"stacked\">Enter your custom response:</ion-label>\n            <ion-textarea\n              [(ngModel)]=\"customResponse\"\n              placeholder=\"Type your response here...\"\n              [counter]=\"true\"\n              maxlength=\"150\"\n              rows=\"3\"\n              class=\"custom-response-input\"></ion-textarea>\n          </ion-item>\n        </div>\n      </div>\n\n      <ion-button\n        expand=\"block\"\n        [disabled]=\"(responseMode === 'cards' && (selectedResponseIndex < 0 || playerResponses.length === 0)) ||\n                    (responseMode === 'custom' && !customResponse.trim())\"\n        (click)=\"submitResponse()\"\n        class=\"ion-margin-top\"\n        *ngIf=\"!hasSubmitted\">\n        Submit Response\n      </ion-button>\n    </div>\n  </div>\n\n  <!-- Ready for Judging state -->\n  <div *ngIf=\"gameState?.status === 'Ready for Judging'\" class=\"game-container\">\n    <h1 class=\"ion-text-center\">Round {{ getCurrentRound() }}</h1>\n\n    <div *ngIf=\"isJudge\" class=\"judge-container\">\n      <h3 class=\"ion-text-center\">You are the judge - select the best response:</h3>\n\n      <ion-list>\n        <ion-item *ngFor=\"let response of getSubmittedResponses(); let i = index\"\n                  [class.selected-response]=\"selectedWinnerIndex === i\"\n                  (click)=\"selectWinnerResponse(i)\"\n                  lines=\"none\"\n                  button>\n          <ion-label>{{ response.text }}</ion-label>\n        </ion-item>\n      </ion-list>\n\n      <ion-button\n        expand=\"block\"\n        [disabled]=\"selectedWinnerIndex === null\"\n        (click)=\"selectWinner()\"\n        class=\"ion-margin-top\">\n        Select Winner\n      </ion-button>\n\n      <ion-button\n        expand=\"block\"\n        (click)=\"skipJudging()\"\n        color=\"medium\"\n        class=\"ion-margin-top\">\n        Skip Judging (No Winner)\n      </ion-button>\n    </div>\n\n    <div *ngIf=\"!isJudge\" class=\"waiting-container ion-text-center\">\n      <h3>Waiting for judge to select a winner...</h3>\n    </div>\n  </div>\n\n  <!-- Winner Chosen state -->\n  <div *ngIf=\"gameState?.status === 'Winner Chosen'\" class=\"game-container\">\n    <h1 class=\"ion-text-center\">Round {{ getCurrentRound() }} Results</h1>\n\n    <!-- Only show prompt container if getPrompt() returns a non-empty string -->\n    <div *ngIf=\"getPrompt()\" class=\"prompt-container ion-margin-vertical\">\n      <h2 class=\"ion-text-center\">Prompt:</h2>\n      <p class=\"prompt ion-text-center\">{{ getPrompt() }}</p>\n    </div>\n\n    <div class=\"winner-container ion-text-center ion-margin-vertical\">\n      <h2>Winner: {{ gameState?.winner || 'No winner' }}</h2>\n      <p class=\"winning-response\">{{ gameState?.winning_response || 'No winning response' }}</p>\n    </div>\n\n    <ion-list>\n      <ion-list-header>\n        <ion-label>Scores</ion-label>\n      </ion-list-header>\n      <ion-item *ngFor=\"let player of getPlayers()\" lines=\"none\">\n        <ion-label>{{ player }}</ion-label>\n        <ion-note slot=\"end\">{{ getPlayerScore(player) }}</ion-note>\n      </ion-item>\n    </ion-list>\n\n    <div class=\"ion-text-center ion-margin-vertical\">\n      <p>Next round starting automatically in {{ nextRoundCountdown }} seconds...</p>\n    </div>\n  </div>\n\n  <!-- Game Over state -->\n  <div *ngIf=\"gameState?.status === 'Game Over'\" class=\"game-container\">\n    <h1 class=\"ion-text-center\">Game Over</h1>\n\n    <div class=\"winner-container ion-text-center ion-margin-vertical\">\n      <h2>Game Winner: {{ gameState?.game_winner || getGameWinner() }}</h2>\n      <p>Reached 5 points first!</p>\n    </div>\n\n    <ion-list>\n      <ion-list-header>\n        <ion-label>Final Scores</ion-label>\n      </ion-list-header>\n      <ion-item *ngFor=\"let player of getPlayers()\" lines=\"none\">\n        <ion-label>{{ player }}</ion-label>\n        <ion-note slot=\"end\">{{ getPlayerScore(player) }}</ion-note>\n      </ion-item>\n    </ion-list>\n\n    <div *ngIf=\"isLeader\" class=\"leader-options\">\n      <h3 class=\"ion-text-center\">What would you like to do?</h3>\n\n      <ion-button\n        expand=\"block\"\n        (click)=\"playAgain()\"\n        class=\"ion-margin-top\">\n        Play Again (Same Players)\n      </ion-button>\n\n      <ion-button\n        expand=\"block\"\n        (click)=\"endGame()\"\n        color=\"danger\"\n        class=\"ion-margin-top\">\n        End Game\n      </ion-button>\n    </div>\n\n    <div *ngIf=\"!isLeader\" class=\"waiting-container ion-text-center\">\n      <h3>Waiting for leader to decide...</h3>\n    </div>\n  </div>\n</ion-content>\n"], "mappings": ";;;;;;;;;;ICiBIA,EADF,CAAAC,cAAA,aAAkE,QAC7D;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IACpBF,EADoB,CAAAG,YAAA,EAAI,EAClB;;;;;IAaAH,EAAA,CAAAC,cAAA,kBAAmD;IAAAD,EAAA,CAAAE,MAAA,UAAG;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IADjEH,EADF,CAAAC,cAAA,mBAA2D,gBAC9C;IAAAD,EAAA,CAAAE,MAAA,GAAY;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACnCH,EAAA,CAAAI,UAAA,IAAAC,+CAAA,uBAAmD;IACrDL,EAAA,CAAAG,YAAA,EAAW;;;;;IAFEH,EAAA,CAAAM,SAAA,GAAY;IAAZN,EAAA,CAAAO,iBAAA,CAAAC,SAAA,CAAY;IACDR,EAAA,CAAAM,SAAA,EAA2B;IAA3BN,EAAA,CAAAS,UAAA,SAAAD,SAAA,KAAAE,MAAA,CAAAC,UAAA,CAA2B;;;;;;IAIrDX,EAAA,CAAAC,cAAA,qBAIyB;IADvBD,EAAA,CAAAY,UAAA,mBAAAC,mEAAA;MAAAb,EAAA,CAAAc,aAAA,CAAAC,GAAA;MAAA,MAAAL,MAAA,GAAAV,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASP,MAAA,CAAAQ,SAAA,EAAW;IAAA,EAAC;IAErBlB,EAAA,CAAAE,MAAA,mBACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;;IAEbH,EAAA,CAAAC,cAAA,YAAuE;IACrED,EAAA,CAAAE,MAAA,mDACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAvBJH,EADF,CAAAC,cAAA,cAAiF,aACnD;IAAAD,EAAA,CAAAE,MAAA,0BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpDH,EAAA,CAAAC,cAAA,YAA2B;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAE,MAAA,GAAc;IAASF,EAAT,CAAAG,YAAA,EAAS,EAAI;IAIrEH,EAFJ,CAAAC,cAAA,eAAU,sBACS,gBACJ;IAAAD,EAAA,CAAAE,MAAA,IAAmC;IAChDF,EADgD,CAAAG,YAAA,EAAY,EAC1C;IAClBH,EAAA,CAAAI,UAAA,KAAAe,oCAAA,uBAA2D;IAI7DnB,EAAA,CAAAG,YAAA,EAAW;IAUXH,EARA,CAAAI,UAAA,KAAAgB,sCAAA,yBAIyB,KAAAC,6BAAA,gBAI8C;IAGzErB,EAAA,CAAAG,YAAA,EAAM;;;;IAvB0CH,EAAA,CAAAM,SAAA,GAAc;IAAdN,EAAA,CAAAO,iBAAA,CAAAG,MAAA,CAAAY,QAAA,CAAc;IAI7CtB,EAAA,CAAAM,SAAA,GAAmC;IAAnCN,EAAA,CAAAuB,kBAAA,cAAAb,MAAA,CAAAc,UAAA,GAAAC,MAAA,MAAmC;IAEnBzB,EAAA,CAAAM,SAAA,EAAe;IAAfN,EAAA,CAAAS,UAAA,YAAAC,MAAA,CAAAc,UAAA,GAAe;IAO3CxB,EAAA,CAAAM,SAAA,EAA0C;IAA1CN,EAAA,CAAAS,UAAA,SAAAC,MAAA,CAAAgB,QAAA,IAAAhB,MAAA,CAAAc,UAAA,GAAAC,MAAA,MAA0C;IAOzCzB,EAAA,CAAAM,SAAA,EAAyC;IAAzCN,EAAA,CAAAS,UAAA,SAAAC,MAAA,CAAAgB,QAAA,IAAAhB,MAAA,CAAAc,UAAA,GAAAC,MAAA,KAAyC;;;;;IAe3CzB,EADF,CAAAC,cAAA,cAA6D,SACvD;IAAAD,EAAA,CAAAE,MAAA,uCAAgC;IACtCF,EADsC,CAAAG,YAAA,EAAK,EACrC;;;;;IATNH,EADF,CAAAC,cAAA,cAA+E,aACjD;IAAAD,EAAA,CAAAE,MAAA,GAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAG5DH,EADF,CAAAC,cAAA,cAAkD,aACpB;IAAAD,EAAA,CAAAE,MAAA,4BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtDH,EAAA,CAAAC,cAAA,YAA2B;IAAAD,EAAA,CAAAE,MAAA,oDAA6C;IAC1EF,EAD0E,CAAAG,YAAA,EAAI,EACxE;IAENH,EAAA,CAAAI,UAAA,IAAAuB,8BAAA,kBAA6D;IAG/D3B,EAAA,CAAAG,YAAA,EAAM;;;;IAVwBH,EAAA,CAAAM,SAAA,GAA6B;IAA7BN,EAAA,CAAAuB,kBAAA,WAAAb,MAAA,CAAAkB,eAAA,OAA6B;IAOX5B,EAAA,CAAAM,SAAA,GAAa;IAAbN,EAAA,CAAAS,UAAA,SAAAC,MAAA,CAAAmB,OAAA,CAAa;;;;;IAezD7B,EADF,CAAAC,cAAA,cAA6D,SACvD;IAAAD,EAAA,CAAAE,MAAA,uCAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,kDAA2C;IAChDF,EADgD,CAAAG,YAAA,EAAI,EAC9C;;;;;IAVNH,EADF,CAAAC,cAAA,cAAkE,aACpC;IAAAD,EAAA,CAAAE,MAAA,GAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAG5DH,EADF,CAAAC,cAAA,cAAkD,aACpB;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxCH,EAAA,CAAAC,cAAA,YAAkC;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IACrDF,EADqD,CAAAG,YAAA,EAAI,EACnD;IAENH,EAAA,CAAAI,UAAA,IAAA0B,8BAAA,kBAA6D;IAI/D9B,EAAA,CAAAG,YAAA,EAAM;;;;IAXwBH,EAAA,CAAAM,SAAA,GAA6B;IAA7BN,EAAA,CAAAuB,kBAAA,WAAAb,MAAA,CAAAkB,eAAA,OAA6B;IAIrB5B,EAAA,CAAAM,SAAA,GAAiB;IAAjBN,EAAA,CAAAO,iBAAA,CAAAG,MAAA,CAAAqB,SAAA,GAAiB;IAG/C/B,EAAA,CAAAM,SAAA,EAAa;IAAbN,EAAA,CAAAS,UAAA,SAAAC,MAAA,CAAAmB,OAAA,CAAa;;;;;IAoBjB7B,EAAA,CAAAC,cAAA,QAA0C;IACxCD,EAAA,CAAAE,MAAA,GAEF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAFFH,EAAA,CAAAM,SAAA,EAEF;IAFEN,EAAA,CAAAgC,kBAAA,OAAAtB,MAAA,CAAAuB,SAAA,kBAAAvB,MAAA,CAAAuB,SAAA,CAAAC,mBAAA,kBAAAxB,MAAA,CAAAuB,SAAA,CAAAC,mBAAA,CAAAT,MAAA,iBAAAf,MAAA,CAAAuB,SAAA,kBAAAvB,MAAA,CAAAuB,SAAA,CAAAE,OAAA,kBAAAzB,MAAA,CAAAuB,SAAA,CAAAE,OAAA,CAAAV,MAAA,wCAEF;;;;;IAPAzB,EADF,CAAAC,cAAA,cAA6D,SACvD;IAAAD,EAAA,CAAAE,MAAA,uCAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,kDAA2C;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAGlDH,EAAA,CAAAI,UAAA,IAAAgC,kCAAA,gBAA0C;IAI5CpC,EAAA,CAAAG,YAAA,EAAM;;;;IAJAH,EAAA,CAAAM,SAAA,GAAoC;IAApCN,EAAA,CAAAS,UAAA,SAAAC,MAAA,CAAAuB,SAAA,kBAAAvB,MAAA,CAAAuB,SAAA,CAAAC,mBAAA,CAAoC;;;;;IAUtClC,EADF,CAAAC,cAAA,cAA8E,QACzE;IAAAD,EAAA,CAAAE,MAAA,8CAAuC;IAC5CF,EAD4C,CAAAG,YAAA,EAAI,EAC1C;;;;;IAOJH,EAAA,CAAAC,cAAA,QAA0C;IACxCD,EAAA,CAAAE,MAAA,GAEF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAFFH,EAAA,CAAAM,SAAA,EAEF;IAFEN,EAAA,CAAAgC,kBAAA,OAAAtB,MAAA,CAAAuB,SAAA,kBAAAvB,MAAA,CAAAuB,SAAA,CAAAC,mBAAA,kBAAAxB,MAAA,CAAAuB,SAAA,CAAAC,mBAAA,CAAAT,MAAA,iBAAAf,MAAA,CAAAuB,SAAA,kBAAAvB,MAAA,CAAAuB,SAAA,CAAAE,OAAA,kBAAAzB,MAAA,CAAAuB,SAAA,CAAAE,OAAA,CAAAV,MAAA,wCAEF;;;;;IAPAzB,EADF,CAAAC,cAAA,cAA8D,QACzD;IAAAD,EAAA,CAAAE,MAAA,wCAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACxCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,6DAAsD;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAG7DH,EAAA,CAAAI,UAAA,IAAAiC,wCAAA,gBAA0C;IAI5CrC,EAAA,CAAAG,YAAA,EAAM;;;;IAJAH,EAAA,CAAAM,SAAA,GAAoC;IAApCN,EAAA,CAAAS,UAAA,SAAAC,MAAA,CAAAuB,SAAA,kBAAAvB,MAAA,CAAAuB,SAAA,CAAAC,mBAAA,CAAoC;;;;;;IAmBtClC,EAAA,CAAAC,cAAA,mBAIe;IAFPD,EAAA,CAAAY,UAAA,mBAAA0B,qFAAA;MAAA,MAAAC,IAAA,GAAAvC,EAAA,CAAAc,aAAA,CAAA0B,GAAA,EAAAC,KAAA;MAAA,MAAA/B,MAAA,GAAAV,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASP,MAAA,CAAAgC,cAAA,CAAAH,IAAA,CAAiB;IAAA,EAAC;IAGjCvC,EAAA,CAAAC,cAAA,gBAAW;IAAAD,EAAA,CAAAE,MAAA,GAAc;IAC3BF,EAD2B,CAAAG,YAAA,EAAY,EAC5B;;;;;;IALHH,EAAA,CAAA2C,WAAA,sBAAAjC,MAAA,CAAAkC,qBAAA,KAAAL,IAAA,CAAuD;IAIlDvC,EAAA,CAAAM,SAAA,GAAc;IAAdN,EAAA,CAAAO,iBAAA,CAAAsC,WAAA,CAAc;;;;;IAN7B7C,EAAA,CAAAC,cAAA,eAAyE;IACvED,EAAA,CAAAI,UAAA,IAAA0C,0DAAA,uBAIe;IAGjB9C,EAAA,CAAAG,YAAA,EAAW;;;;IAPsBH,EAAA,CAAAM,SAAA,EAAoB;IAApBN,EAAA,CAAAS,UAAA,YAAAC,MAAA,CAAAqC,eAAA,CAAoB;;;;;;IAYjD/C,EAFJ,CAAAC,cAAA,cAA0F,mBACjE,oBACS;IAAAD,EAAA,CAAAE,MAAA,kCAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACrEH,EAAA,CAAAC,cAAA,uBAMgC;IAL9BD,EAAA,CAAAgD,gBAAA,2BAAAC,iFAAAC,MAAA;MAAAlD,EAAA,CAAAc,aAAA,CAAAqC,GAAA;MAAA,MAAAzC,MAAA,GAAAV,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAoD,kBAAA,CAAA1C,MAAA,CAAA2C,cAAA,EAAAH,MAAA,MAAAxC,MAAA,CAAA2C,cAAA,GAAAH,MAAA;MAAA,OAAAlD,EAAA,CAAAiB,WAAA,CAAAiC,MAAA;IAAA,EAA4B;IAOlClD,EAFoC,CAAAG,YAAA,EAAe,EACtC,EACP;;;;IAPAH,EAAA,CAAAM,SAAA,GAA4B;IAA5BN,EAAA,CAAAsD,gBAAA,YAAA5C,MAAA,CAAA2C,cAAA,CAA4B;IAE5BrD,EAAA,CAAAS,UAAA,iBAAgB;;;;;;IA3BtBT,EAFF,CAAAC,cAAA,cAAoD,sBAE0B;IAA/DD,EAAA,CAAAgD,gBAAA,2BAAAO,0EAAAL,MAAA;MAAAlD,EAAA,CAAAc,aAAA,CAAA0C,GAAA;MAAA,MAAA9C,MAAA,GAAAV,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAoD,kBAAA,CAAA1C,MAAA,CAAA+C,YAAA,EAAAP,MAAA,MAAAxC,MAAA,CAAA+C,YAAA,GAAAP,MAAA;MAAA,OAAAlD,EAAA,CAAAiB,WAAA,CAAAiC,MAAA;IAAA,EAA0B;IAAClD,EAAA,CAAAY,UAAA,uBAAA8C,sEAAA;MAAA1D,EAAA,CAAAc,aAAA,CAAA0C,GAAA;MAAA,MAAA9C,MAAA,GAAAV,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAaP,MAAA,CAAAiD,mBAAA,EAAqB;IAAA,EAAC;IAEvE3D,EADF,CAAAC,cAAA,6BAAkC,gBACrB;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAClBF,EADkB,CAAAG,YAAA,EAAY,EACT;IAEnBH,EADF,CAAAC,cAAA,6BAAmC,gBACtB;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAErBF,EAFqB,CAAAG,YAAA,EAAY,EACV,EACT;IAcdH,EAXA,CAAAI,UAAA,IAAAwD,+CAAA,uBAAyE,IAAAC,0CAAA,kBAWiB;IAY5F7D,EAAA,CAAAG,YAAA,EAAM;;;;IAjCSH,EAAA,CAAAM,SAAA,EAA0B;IAA1BN,EAAA,CAAAsD,gBAAA,YAAA5C,MAAA,CAAA+C,YAAA,CAA0B;IAU5BzD,EAAA,CAAAM,SAAA,GAA4D;IAA5DN,EAAA,CAAAS,UAAA,SAAAC,MAAA,CAAA+C,YAAA,gBAAA/C,MAAA,CAAAqC,eAAA,CAAAtB,MAAA,KAA4D;IAWjEzB,EAAA,CAAAM,SAAA,EAA+B;IAA/BN,EAAA,CAAAS,UAAA,SAAAC,MAAA,CAAA+C,YAAA,cAA+B;;;;;;IAcvCzD,EAAA,CAAAC,cAAA,qBAMwB;IAFtBD,EAAA,CAAAY,UAAA,mBAAAkD,wEAAA;MAAA9D,EAAA,CAAAc,aAAA,CAAAiD,GAAA;MAAA,MAAArD,MAAA,GAAAV,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASP,MAAA,CAAAsD,cAAA,EAAgB;IAAA,EAAC;IAG1BhE,EAAA,CAAAE,MAAA,wBACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IANXH,EAAA,CAAAS,UAAA,aAAAC,MAAA,CAAA+C,YAAA,iBAAA/C,MAAA,CAAAkC,qBAAA,QAAAlC,MAAA,CAAAqC,eAAA,CAAAtB,MAAA,WAAAf,MAAA,CAAA+C,YAAA,kBAAA/C,MAAA,CAAA2C,cAAA,CAAAY,IAAA,GACkE;;;;;IAzDpEjE,EADF,CAAAC,cAAA,cAAkD,aACpB;IAAAD,EAAA,CAAAE,MAAA,4BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAsDtDH,EApDA,CAAAI,UAAA,IAAA8D,oCAAA,kBAA8E,IAAAC,oCAAA,kBAIhB,IAAAC,oCAAA,mBAWV,IAAAC,2CAAA,yBA2C5B;IAG1BrE,EAAA,CAAAG,YAAA,EAAM;;;;IA7DEH,EAAA,CAAAM,SAAA,GAAkC;IAAlCN,EAAA,CAAAS,UAAA,SAAAC,MAAA,CAAAqC,eAAA,CAAAtB,MAAA,OAAkC;IAIlCzB,EAAA,CAAAM,SAAA,EAAkB;IAAlBN,EAAA,CAAAS,UAAA,SAAAC,MAAA,CAAA4D,YAAA,CAAkB;IAWlBtE,EAAA,CAAAM,SAAA,EAAmB;IAAnBN,EAAA,CAAAS,UAAA,UAAAC,MAAA,CAAA4D,YAAA,CAAmB;IA2CtBtE,EAAA,CAAAM,SAAA,EAAmB;IAAnBN,EAAA,CAAAS,UAAA,UAAAC,MAAA,CAAA4D,YAAA,CAAmB;;;;;IA/ExBtE,EADF,CAAAC,cAAA,cAAyF,aAC3D;IAAAD,EAAA,CAAAE,MAAA,GAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAG5DH,EADF,CAAAC,cAAA,cAAkD,aACpB;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxCH,EAAA,CAAAC,cAAA,YAAkC;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IACrDF,EADqD,CAAAG,YAAA,EAAI,EACnD;IAaNH,EAXA,CAAAI,UAAA,IAAAmE,8BAAA,kBAA6D,IAAAC,8BAAA,kBAWX;IAiEpDxE,EAAA,CAAAG,YAAA,EAAM;;;;IAnFwBH,EAAA,CAAAM,SAAA,GAA6B;IAA7BN,EAAA,CAAAuB,kBAAA,WAAAb,MAAA,CAAAkB,eAAA,OAA6B;IAIrB5B,EAAA,CAAAM,SAAA,GAAiB;IAAjBN,EAAA,CAAAO,iBAAA,CAAAG,MAAA,CAAAqB,SAAA,GAAiB;IAG/C/B,EAAA,CAAAM,SAAA,EAAa;IAAbN,EAAA,CAAAS,UAAA,SAAAC,MAAA,CAAAmB,OAAA,CAAa;IAWb7B,EAAA,CAAAM,SAAA,EAAc;IAAdN,EAAA,CAAAS,UAAA,UAAAC,MAAA,CAAAmB,OAAA,CAAc;;;;;;IA2EhB7B,EAAA,CAAAC,cAAA,mBAIiB;IAFPD,EAAA,CAAAY,UAAA,mBAAA6D,oEAAA;MAAA,MAAAC,KAAA,GAAA1E,EAAA,CAAAc,aAAA,CAAA6D,IAAA,EAAAlC,KAAA;MAAA,MAAA/B,MAAA,GAAAV,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASP,MAAA,CAAAkE,oBAAA,CAAAF,KAAA,CAAuB;IAAA,EAAC;IAGzC1E,EAAA,CAAAC,cAAA,gBAAW;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAChCF,EADgC,CAAAG,YAAA,EAAY,EACjC;;;;;;IALDH,EAAA,CAAA2C,WAAA,sBAAAjC,MAAA,CAAAmE,mBAAA,KAAAH,KAAA,CAAqD;IAIlD1E,EAAA,CAAAM,SAAA,GAAmB;IAAnBN,EAAA,CAAAO,iBAAA,CAAAuE,YAAA,CAAAC,IAAA,CAAmB;;;;;;IARlC/E,EADF,CAAAC,cAAA,cAA6C,aACf;IAAAD,EAAA,CAAAE,MAAA,oDAA6C;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAE9EH,EAAA,CAAAC,cAAA,eAAU;IACRD,EAAA,CAAAI,UAAA,IAAA4E,yCAAA,uBAIiB;IAGnBhF,EAAA,CAAAG,YAAA,EAAW;IAEXH,EAAA,CAAAC,cAAA,qBAIyB;IADvBD,EAAA,CAAAY,UAAA,mBAAAqE,2DAAA;MAAAjF,EAAA,CAAAc,aAAA,CAAAoE,IAAA;MAAA,MAAAxE,MAAA,GAAAV,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASP,MAAA,CAAAyE,YAAA,EAAc;IAAA,EAAC;IAExBnF,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;IAEbH,EAAA,CAAAC,cAAA,qBAIyB;IAFvBD,EAAA,CAAAY,UAAA,mBAAAwE,2DAAA;MAAApF,EAAA,CAAAc,aAAA,CAAAoE,IAAA;MAAA,MAAAxE,MAAA,GAAAV,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASP,MAAA,CAAA2E,WAAA,EAAa;IAAA,EAAC;IAGvBrF,EAAA,CAAAE,MAAA,iCACF;IACFF,EADE,CAAAG,YAAA,EAAa,EACT;;;;IAxB6BH,EAAA,CAAAM,SAAA,GAA4B;IAA5BN,EAAA,CAAAS,UAAA,YAAAC,MAAA,CAAA4E,qBAAA,GAA4B;IAW3DtF,EAAA,CAAAM,SAAA,EAAyC;IAAzCN,EAAA,CAAAS,UAAA,aAAAC,MAAA,CAAAmE,mBAAA,UAAyC;;;;;IAgB3C7E,EADF,CAAAC,cAAA,cAAgE,SAC1D;IAAAD,EAAA,CAAAE,MAAA,8CAAuC;IAC7CF,EAD6C,CAAAG,YAAA,EAAK,EAC5C;;;;;IAlCNH,EADF,CAAAC,cAAA,cAA8E,aAChD;IAAAD,EAAA,CAAAE,MAAA,GAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAgC9DH,EA9BA,CAAAI,UAAA,IAAAmF,8BAAA,kBAA6C,IAAAC,8BAAA,kBA8BmB;IAGlExF,EAAA,CAAAG,YAAA,EAAM;;;;IAnCwBH,EAAA,CAAAM,SAAA,GAA6B;IAA7BN,EAAA,CAAAuB,kBAAA,WAAAb,MAAA,CAAAkB,eAAA,OAA6B;IAEnD5B,EAAA,CAAAM,SAAA,EAAa;IAAbN,EAAA,CAAAS,UAAA,SAAAC,MAAA,CAAAmB,OAAA,CAAa;IA8Bb7B,EAAA,CAAAM,SAAA,EAAc;IAAdN,EAAA,CAAAS,UAAA,UAAAC,MAAA,CAAAmB,OAAA,CAAc;;;;;IAWlB7B,EADF,CAAAC,cAAA,cAAsE,aACxC;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxCH,EAAA,CAAAC,cAAA,YAAkC;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IACrDF,EADqD,CAAAG,YAAA,EAAI,EACnD;;;;IAD8BH,EAAA,CAAAM,SAAA,GAAiB;IAAjBN,EAAA,CAAAO,iBAAA,CAAAG,MAAA,CAAAqB,SAAA,GAAiB;;;;;IAajD/B,EADF,CAAAC,cAAA,mBAA2D,gBAC9C;IAAAD,EAAA,CAAAE,MAAA,GAAY;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACnCH,EAAA,CAAAC,cAAA,kBAAqB;IAAAD,EAAA,CAAAE,MAAA,GAA4B;IACnDF,EADmD,CAAAG,YAAA,EAAW,EACnD;;;;;IAFEH,EAAA,CAAAM,SAAA,GAAY;IAAZN,EAAA,CAAAO,iBAAA,CAAAkF,UAAA,CAAY;IACFzF,EAAA,CAAAM,SAAA,GAA4B;IAA5BN,EAAA,CAAAO,iBAAA,CAAAG,MAAA,CAAAgF,cAAA,CAAAD,UAAA,EAA4B;;;;;IAnBrDzF,EADF,CAAAC,cAAA,cAA0E,aAC5C;IAAAD,EAAA,CAAAE,MAAA,GAAqC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAGtEH,EAAA,CAAAI,UAAA,IAAAuF,8BAAA,kBAAsE;IAMpE3F,EADF,CAAAC,cAAA,cAAkE,SAC5D;IAAAD,EAAA,CAAAE,MAAA,GAA8C;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvDH,EAAA,CAAAC,cAAA,YAA4B;IAAAD,EAAA,CAAAE,MAAA,GAA0D;IACxFF,EADwF,CAAAG,YAAA,EAAI,EACtF;IAIFH,EAFJ,CAAAC,cAAA,eAAU,uBACS,iBACJ;IAAAD,EAAA,CAAAE,MAAA,cAAM;IACnBF,EADmB,CAAAG,YAAA,EAAY,EACb;IAClBH,EAAA,CAAAI,UAAA,KAAAwF,oCAAA,uBAA2D;IAI7D5F,EAAA,CAAAG,YAAA,EAAW;IAGTH,EADF,CAAAC,cAAA,eAAiD,SAC5C;IAAAD,EAAA,CAAAE,MAAA,IAAwE;IAE/EF,EAF+E,CAAAG,YAAA,EAAI,EAC3E,EACF;;;;IA1BwBH,EAAA,CAAAM,SAAA,GAAqC;IAArCN,EAAA,CAAAuB,kBAAA,WAAAb,MAAA,CAAAkB,eAAA,eAAqC;IAG3D5B,EAAA,CAAAM,SAAA,EAAiB;IAAjBN,EAAA,CAAAS,UAAA,SAAAC,MAAA,CAAAqB,SAAA,GAAiB;IAMjB/B,EAAA,CAAAM,SAAA,GAA8C;IAA9CN,EAAA,CAAAuB,kBAAA,cAAAb,MAAA,CAAAuB,SAAA,kBAAAvB,MAAA,CAAAuB,SAAA,CAAA4D,MAAA,qBAA8C;IACtB7F,EAAA,CAAAM,SAAA,GAA0D;IAA1DN,EAAA,CAAAO,iBAAA,EAAAG,MAAA,CAAAuB,SAAA,kBAAAvB,MAAA,CAAAuB,SAAA,CAAA6D,gBAAA,2BAA0D;IAOzD9F,EAAA,CAAAM,SAAA,GAAe;IAAfN,EAAA,CAAAS,UAAA,YAAAC,MAAA,CAAAc,UAAA,GAAe;IAOzCxB,EAAA,CAAAM,SAAA,GAAwE;IAAxEN,EAAA,CAAAuB,kBAAA,0CAAAb,MAAA,CAAAqF,kBAAA,gBAAwE;;;;;IAkBzE/F,EADF,CAAAC,cAAA,mBAA2D,gBAC9C;IAAAD,EAAA,CAAAE,MAAA,GAAY;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACnCH,EAAA,CAAAC,cAAA,kBAAqB;IAAAD,EAAA,CAAAE,MAAA,GAA4B;IACnDF,EADmD,CAAAG,YAAA,EAAW,EACnD;;;;;IAFEH,EAAA,CAAAM,SAAA,GAAY;IAAZN,EAAA,CAAAO,iBAAA,CAAAyF,UAAA,CAAY;IACFhG,EAAA,CAAAM,SAAA,GAA4B;IAA5BN,EAAA,CAAAO,iBAAA,CAAAG,MAAA,CAAAgF,cAAA,CAAAM,UAAA,EAA4B;;;;;;IAKnDhG,EADF,CAAAC,cAAA,cAA6C,aACf;IAAAD,EAAA,CAAAE,MAAA,iCAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAE3DH,EAAA,CAAAC,cAAA,qBAGyB;IADvBD,EAAA,CAAAY,UAAA,mBAAAqF,4DAAA;MAAAjG,EAAA,CAAAc,aAAA,CAAAoF,IAAA;MAAA,MAAAxF,MAAA,GAAAV,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASP,MAAA,CAAAyF,SAAA,EAAW;IAAA,EAAC;IAErBnG,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;IAEbH,EAAA,CAAAC,cAAA,qBAIyB;IAFvBD,EAAA,CAAAY,UAAA,mBAAAwF,4DAAA;MAAApG,EAAA,CAAAc,aAAA,CAAAoF,IAAA;MAAA,MAAAxF,MAAA,GAAAV,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASP,MAAA,CAAA2F,OAAA,EAAS;IAAA,EAAC;IAGnBrG,EAAA,CAAAE,MAAA,iBACF;IACFF,EADE,CAAAG,YAAA,EAAa,EACT;;;;;IAGJH,EADF,CAAAC,cAAA,cAAiE,SAC3D;IAAAD,EAAA,CAAAE,MAAA,sCAA+B;IACrCF,EADqC,CAAAG,YAAA,EAAK,EACpC;;;;;IAtCNH,EADF,CAAAC,cAAA,cAAsE,aACxC;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAGxCH,EADF,CAAAC,cAAA,cAAkE,SAC5D;IAAAD,EAAA,CAAAE,MAAA,GAA4D;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrEH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,8BAAuB;IAC5BF,EAD4B,CAAAG,YAAA,EAAI,EAC1B;IAIFH,EAFJ,CAAAC,cAAA,eAAU,sBACS,iBACJ;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IACzBF,EADyB,CAAAG,YAAA,EAAY,EACnB;IAClBH,EAAA,CAAAI,UAAA,KAAAkG,oCAAA,uBAA2D;IAI7DtG,EAAA,CAAAG,YAAA,EAAW;IAqBXH,EAnBA,CAAAI,UAAA,KAAAmG,+BAAA,kBAA6C,KAAAC,+BAAA,kBAmBoB;IAGnExG,EAAA,CAAAG,YAAA,EAAM;;;;IApCEH,EAAA,CAAAM,SAAA,GAA4D;IAA5DN,EAAA,CAAAuB,kBAAA,mBAAAb,MAAA,CAAAuB,SAAA,kBAAAvB,MAAA,CAAAuB,SAAA,CAAAwE,WAAA,KAAA/F,MAAA,CAAAgG,aAAA,OAA4D;IAQnC1G,EAAA,CAAAM,SAAA,GAAe;IAAfN,EAAA,CAAAS,UAAA,YAAAC,MAAA,CAAAc,UAAA,GAAe;IAMxCxB,EAAA,CAAAM,SAAA,EAAc;IAAdN,EAAA,CAAAS,UAAA,SAAAC,MAAA,CAAAgB,QAAA,CAAc;IAmBd1B,EAAA,CAAAM,SAAA,EAAe;IAAfN,EAAA,CAAAS,UAAA,UAAAC,MAAA,CAAAgB,QAAA,CAAe;;;ADlQzB,OAAM,MAAOiF,QAAQ;EAiCnBC,YACUC,WAAwB,EACxBC,MAAc,EACdC,eAAgC;IAFhC,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,eAAe,GAAfA,eAAe;IAnCzB,KAAA9E,SAAS,GAAqB,IAAI;IAClC,KAAAc,eAAe,GAAa,EAAE;IAC9B,KAAAlB,OAAO,GAAY,KAAK;IACxB,KAAAmF,gBAAgB,GAAkB,IAAI;IACtC,KAAApE,qBAAqB,GAAW,CAAC,CAAC,CAAC,CAAC;IACpC,KAAAiC,mBAAmB,GAAkB,IAAI;IACzC,KAAAP,YAAY,GAAY,KAAK;IAC7B,KAAAyB,kBAAkB,GAAW,CAAC,CAAC,CAAC;IAEhC;IACA,KAAAtC,YAAY,GAAW,OAAO,CAAC,CAAC;IAChC,KAAAJ,cAAc,GAAW,EAAE;IAE3B;IACA,KAAA4D,UAAU,GAAY,KAAK;IAE3B;IACQ,KAAAC,yBAAyB,GAAW,CAAC,CAAC;IAE9C;IACQ,KAAAC,oBAAoB,GAAkB,IAAI;IAC1C,KAAAC,2BAA2B,GAAY,KAAK;IAEpD;IACQ,KAAAC,kBAAkB,GAAW,EAAE;IAC/B,KAAAC,gBAAgB,GAAW,OAAO;IAElC,KAAAC,qBAAqB,GAAwB,IAAI;IACjD,KAAAC,2BAA2B,GAAwB,IAAI;IACvD,KAAAC,mBAAmB,GAAwB,IAAI;IAC/C,KAAAC,iBAAiB,GAAkB,IAAI;EAM5C;EAIHC,QAAQA,CAAA;IACNC,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;IAEvC;IACA,IAAI,CAACN,qBAAqB,GAAG,IAAI,CAACV,WAAW,CAACiB,UAAU,CAACC,SAAS,CAACC,KAAK,IAAG;MACzEJ,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEG,KAAK,GAAG,WAAWA,KAAK,CAACC,MAAM,EAAE,GAAG,MAAM,CAAC;MAE/F,IAAI,CAACb,2BAA2B,GAAG,IAAI;MAEvC;MACA,IAAI,CAACnF,SAAS,GAAG+F,KAAK;MAEtB,IAAI,CAACN,iBAAiB,GAAG,CAAAM,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEC,MAAM,KAAI,IAAI;MAE9C;MACA,IAAID,KAAK,IAAIA,KAAK,CAACC,MAAM,KAAK,eAAe,EAAE;QAC7C,IAAI,CAAChB,UAAU,GAAG,IAAI;QACtBW,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;MAC9D;MAEA;MACA,IAAIG,KAAK,IAAIA,KAAK,CAACE,cAAc,IAAIF,KAAK,CAACE,cAAc,CAACC,MAAM,EAAE;QAChE;QACA,IAAIH,KAAK,CAACC,MAAM,KAAK,oBAAoB,IACrCD,KAAK,CAACC,MAAM,KAAK,OAAO,IACxBD,KAAK,CAACC,MAAM,KAAK,8BAA8B,IAC/CD,KAAK,CAACC,MAAM,KAAK,mBAAmB,EAAE;UACxC,IAAI,CAAChB,UAAU,GAAG,KAAK;UACvBW,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEG,KAAK,CAACC,MAAM,EAAE,oBAAoB,EAAED,KAAK,CAACE,cAAc,CAACC,MAAM,CAAC;QACnG;MACF;MAEA;MACA,IAAIH,KAAK,IAAIA,KAAK,CAACC,MAAM,KAAK,oBAAoB,KAAK,CAACD,KAAK,CAACE,cAAc,IAAI,CAACF,KAAK,CAACE,cAAc,CAACC,MAAM,CAAC,EAAE;QAC7G,IAAI,CAAClB,UAAU,GAAG,IAAI;QACtBW,OAAO,CAACC,GAAG,CAAC,4EAA4E,CAAC;MAC3F;MAEA;MACA,IAAIG,KAAK,IAAIA,KAAK,CAACC,MAAM,KAAK,8BAA8B,EAAE;QAAA,IAAAG,eAAA;QAC5D;QACA,MAAMzH,UAAU,GAAG,IAAI,CAACA,UAAU;QAClC,MAAM0H,kBAAkB,GAAGL,KAAK,CAAC9F,mBAAmB,IAAI,EAAE;QAE1D;QACA,MAAMoG,mBAAmB,GAAGD,kBAAkB,CAACE,IAAI,CACjDC,QAAQ,IAAIA,QAAQ,CAACC,WAAW,KAAK9H,UAAU,CAChD;QAED;QACA;QACA;QACA,IAAI,CAAC2D,YAAY,GAAGgE,mBAAmB;QACvCV,OAAO,CAACC,GAAG,CAAC,wBAAwBS,mBAAmB,sBAAsB,CAAC;QAE9E;QACA,MAAMI,aAAa,GAAG,EAAAN,eAAA,OAAI,CAACnG,SAAS,cAAAmG,eAAA,uBAAdA,eAAA,CAAgBO,aAAa,KAAI,CAAC;QACxD,MAAMC,YAAY,GAAGZ,KAAK,CAACW,aAAa,IAAI,CAAC;QAE7C;QACA,IAAID,aAAa,KAAKE,YAAY,IAAIF,aAAa,GAAG,CAAC,EAAE;UACvDd,OAAO,CAACC,GAAG,CAAC,sBAAsBa,aAAa,OAAOE,YAAY,0BAA0B,CAAC;UAC7FhB,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE,CAAC,GAAG,IAAI,CAAC9E,eAAe,CAAC,CAAC;UAC5E,IAAI,CAACuB,YAAY,GAAG,KAAK;QAC3B;QAEA,IAAI,CAACgE,mBAAmB,EAAE;UACxB;UACA;UACA,IAAI,IAAI,CAACpB,yBAAyB,KAAK,CAAC,CAAC,IACpC,IAAI,CAACA,yBAAyB,IAAI,CAAC,IACnC,IAAI,CAACA,yBAAyB,GAAG,IAAI,CAACnE,eAAe,CAACtB,MAAM,IAC5D,IAAI,CAACsB,eAAe,CAAC,IAAI,CAACmE,yBAAyB,CAAC,KAAK,IAAI,CAACF,gBAAiB,EAAE;YACpF,IAAI,CAACA,gBAAgB,GAAG,IAAI;YAC5B,IAAI,CAACpE,qBAAqB,GAAG,CAAC,CAAC;UACjC,CAAC,MAAM;YACL;YACA,IAAI,CAACA,qBAAqB,GAAG,IAAI,CAACsE,yBAAyB;YAC3D,IAAI,IAAI,CAACtE,qBAAqB,IAAI,CAAC,IAAI,IAAI,CAACA,qBAAqB,GAAG,IAAI,CAACG,eAAe,CAACtB,MAAM,EAAE;cAC/F,IAAI,CAACuF,gBAAgB,GAAG,IAAI,CAACjE,eAAe,CAAC,IAAI,CAACH,qBAAqB,CAAC;YAC1E;UACF;UACA;UACA;QACF;MACF;MAEA;MACA,IAAIoF,KAAK,IAAIA,KAAK,CAACC,MAAM,KAAK,mBAAmB,EAAE;QACjD,IAAI,CAACpD,mBAAmB,GAAG,IAAI;MACjC;MAEA;MACA,IAAImD,KAAK,IAAIA,KAAK,CAACC,MAAM,KAAK,QAAQ,EAAE;QACtC,IAAI,CAACnB,MAAM,CAAC+B,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;MACjC;MAEA;MACA;MAEA;MACA,IAAIb,KAAK,IAAIA,KAAK,CAACC,MAAM,KAAK,8BAA8B,EAAE;QAC5D;QACA,MAAMtH,UAAU,GAAG,IAAI,CAACA,UAAU;QAClC,MAAM0H,kBAAkB,GAAGL,KAAK,CAAC9F,mBAAmB,IAAI,EAAE,CAAC,CAAU;QACnE,MAAMoG,mBAAmB,GAAGD,kBAAkB,CAACE,IAAI,CACjDC,QAAQ,IAAIA,QAAQ,CAACC,WAAW,KAAK9H,UAAU,CAChD;QAED;QACAiH,OAAO,CAACC,GAAG,CAAC,uDAAuD,EAAE;UACnEiB,mBAAmB,EAAE,IAAI,CAACxE,YAAY;UACtCyE,eAAe,EAAET,mBAAmB;UACpC1F,qBAAqB,EAAE,IAAI,CAACA,qBAAqB;UACjDoE,gBAAgB,EAAE,IAAI,CAACA,gBAAgB;UACvCG,oBAAoB,EAAE,IAAI,CAACA,oBAAoB;UAC/C6B,aAAa,EAAE,IAAI,CAACjG,eAAe,CAACtB;SACrC,CAAC;QAEJ;QACA,IAAI,CAAC6C,YAAY,GAAGgE,mBAAmB;QAEvC;QACA,IAAIA,mBAAmB,EAAE;UACvBV,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;UACxD,IAAI,CAACjF,qBAAqB,GAAG,CAAC,CAAC;UAC/B,IAAI,CAACoE,gBAAgB,GAAG,IAAI;UAC5B,IAAI,CAACG,oBAAoB,GAAG,IAAI;UAChC,IAAI,CAACD,yBAAyB,GAAG,CAAC,CAAC;QACrC,CAAC,MAAM;UACL;UACAU,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;UAC7D;QACF;MACF;MAEA,IAAI,CAACT,2BAA2B,GAAG,KAAK;IAC1C,CAAC,CAAC;IAEF;IACA,IAAI,CAACI,2BAA2B,GAAG,IAAI,CAACX,WAAW,CAACoC,gBAAgB,CAAClB,SAAS,CAACmB,SAAS,IAAG;MACzFtB,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEqB,SAAS,CAAC;MAE3D;MACA,MAAMC,mBAAmB,GAAG,IAAI,CAAC1F,YAAY;MAC7C,MAAM2F,qBAAqB,GAAG,IAAI,CAAC/F,cAAc;MAEjD;MACA;MACA;MACA,IAAI,CAAC,IAAI,CAAC+D,2BAA2B,EAAE;QACrCQ,OAAO,CAACC,GAAG,CAAC,+DAA+D,CAAC;QAE5E;QACA,MAAMwB,YAAY,GAAG,IAAI,CAAC/E,YAAY;QAEtC;QACA,IAAI,CAACgF,2BAA2B,CAACJ,SAAS,CAAC;QAE3C;QACA,IAAI,CAAC5E,YAAY,GAAG+E,YAAY;QAEhC;QACA,IAAIF,mBAAmB,KAAK,QAAQ,EAAE;UACpCvB,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEuB,qBAAqB,CAAC;UAC9E,IAAI,CAAC3F,YAAY,GAAG,QAAQ;UAC5B,IAAI,CAACJ,cAAc,GAAG+F,qBAAqB;QAC7C;MACF,CAAC,MAAM;QACLxB,OAAO,CAACC,GAAG,CAAC,uEAAuE,CAAC;MACtF;IACF,CAAC,CAAC;IAEF;IACA,IAAI,CAACJ,mBAAmB,GAAG,IAAI,CAACZ,WAAW,CAAC0C,QAAQ,CAACxB,SAAS,CAAClG,OAAO,IAAG;MACvE,IAAI,CAACA,OAAO,GAAGA,OAAO;IACxB,CAAC,CAAC;EACJ;EAEA2H,WAAWA,CAAA;IACT;IACA,IAAI,IAAI,CAACjC,qBAAqB,EAAE;MAC9B,IAAI,CAACA,qBAAqB,CAACkC,WAAW,EAAE;IAC1C;IACA,IAAI,IAAI,CAACjC,2BAA2B,EAAE;MACpC,IAAI,CAACA,2BAA2B,CAACiC,WAAW,EAAE;IAChD;IACA,IAAI,IAAI,CAAChC,mBAAmB,EAAE;MAC5B,IAAI,CAACA,mBAAmB,CAACgC,WAAW,EAAE;IACxC;IAEA;IACA,IAAI,CAACC,uBAAuB,EAAE;EAChC;EAEA;EACA/F,mBAAmBA,CAAA;IACjBiE,OAAO,CAACC,GAAG,CAAC,6BAA6B,IAAI,CAACpE,YAAY,EAAE,CAAC;IAE7D;IACA,IAAI,CAAC6D,gBAAgB,GAAG,IAAI,CAAC7D,YAAY;IAEzC;IACA,IAAI,IAAI,CAACA,YAAY,KAAK,OAAO,EAAE;MACjC;MACA,IAAI,CAAC4D,kBAAkB,GAAG,IAAI,CAAChE,cAAc;MAC7C,IAAI,CAACA,cAAc,GAAG,EAAE;MACxB;IACF,CAAC,MAAM;MACL;MACA,IAAI,CAAC6D,yBAAyB,GAAG,IAAI,CAACtE,qBAAqB;MAC3D,IAAI,CAACA,qBAAqB,GAAG,CAAC,CAAC;IACjC;IAEAgF,OAAO,CAACC,GAAG,CAAC,wCAAwC,IAAI,CAACpE,YAAY,aAAa,IAAI,CAACJ,cAAc,EAAE,CAAC;EAC1G;EAEA;EACAsG,uBAAuBA,CAAA;IACrB;IACA,IAAI,CAAC5D,kBAAkB,GAAG,CAAC;IAE3B;IACA,IAAI,CAACkB,UAAU,GAAG,IAAI;IACtBW,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;IAExD;IACA,IAAI,CAAC6B,uBAAuB,EAAE;IAE9B;IACA,IAAI,CAACE,iBAAiB,GAAGC,WAAW,CAAC,MAAK;MACxC,IAAI,CAAC9D,kBAAkB,EAAE;MAEzB;MACA,IAAI,IAAI,CAACA,kBAAkB,IAAI,CAAC,EAAE;QAChC,IAAI,CAAC2D,uBAAuB,EAAE;QAE9B;QACA,IAAI,IAAI,CAAChI,QAAQ,EAAE;UACjBkG,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;UACnE,IAAI,CAAChB,WAAW,CAACiD,YAAY,EAAE;QACjC;MACF;IACF,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;EACAJ,uBAAuBA,CAAA;IACrB,IAAI,IAAI,CAACE,iBAAiB,EAAE;MAC1BG,aAAa,CAAC,IAAI,CAACH,iBAAiB,CAAC;MACrC,IAAI,CAACA,iBAAiB,GAAG,IAAI;IAC/B;EACF;EAEA;EACA1I,SAASA,CAAA;IACP;IACA,IAAI,CAAC+F,UAAU,GAAG,KAAK;IACvBW,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;IAEjE,IAAI,CAAChB,WAAW,CAACiD,YAAY,EAAE;EACjC;EAEA;EACA9F,cAAcA,CAAA;IACZ,IAAI;MACF,IAAI,IAAI,CAACP,YAAY,KAAK,OAAO,EAAE;QACjC;QACA,IAAI,IAAI,CAACb,qBAAqB,GAAG,CAAC,EAAE;UAClCgF,OAAO,CAACoC,IAAI,CAAC,qCAAqC,CAAC;UACnD;QACF;QAEA;QACA,MAAMC,YAAY,GAAG,IAAI,CAAClH,eAAe,CAAC,IAAI,CAACH,qBAAqB,CAAC;QACrE,IAAI,CAACqH,YAAY,EAAE;UACjBrC,OAAO,CAACoC,IAAI,CAAC,gDAAgD,CAAC;UAC9D;QACF;QAEApC,OAAO,CAACC,GAAG,CAAC,8BAA8BoC,YAAY,gBAAgB,IAAI,CAACrH,qBAAqB,EAAE,CAAC;QAEnG;QACA,IAAI,CAAC0B,YAAY,GAAG,IAAI;QAExB;QACA,IAAI,CAACuC,WAAW,CAAC7C,cAAc,CAACiG,YAAY,EAAE,IAAI,CAACrH,qBAAqB,CAAC,CACtEsH,IAAI,CAAC,MAAK;UACT;UACAtC,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;UAEnD;UACA,MAAMsC,gBAAgB,GAAG,IAAI,CAACtD,WAAW,CAACuD,mBAAmB,EAAE;UAC/D,IAAID,gBAAgB,CAAC1I,MAAM,GAAG,CAAC,EAAE;YAC/BmG,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEsC,gBAAgB,CAAC;YAC3E,IAAI,CAACpH,eAAe,GAAG,CAAC,GAAGoH,gBAAgB,CAAC;UAC9C;QACF,CAAC,CAAC,CACDE,KAAK,CAACC,KAAK,IAAG;UACb1C,OAAO,CAAC0C,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;UAClD,IAAI,CAAChG,YAAY,GAAG,KAAK,CAAC,CAAC;QAC7B,CAAC,CAAC;MACN,CAAC,MAAM;QAAA,IAAAiG,oBAAA;QACL;QACA,IAAI,GAAAA,oBAAA,GAAC,IAAI,CAAClH,cAAc,cAAAkH,oBAAA,eAAnBA,oBAAA,CAAqBtG,IAAI,EAAE,GAAE;UAChC2D,OAAO,CAACoC,IAAI,CAAC,yCAAyC,CAAC;UACvD;QACF;QAEApC,OAAO,CAACC,GAAG,CAAC,gCAAgC,IAAI,CAACxE,cAAc,GAAG,CAAC;QAEnE;QACA,IAAI,CAACiB,YAAY,GAAG,IAAI;QAExB;QACA,IAAI,CAACuC,WAAW,CAAC7C,cAAc,CAAC,IAAI,CAACX,cAAc,CAACY,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC,CAC5DiG,IAAI,CAAC,MAAK;UACTtC,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;QACvD,CAAC,CAAC,CACDwC,KAAK,CAACC,KAAK,IAAG;UACb1C,OAAO,CAAC0C,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;UACzD,IAAI,CAAChG,YAAY,GAAG,KAAK,CAAC,CAAC;QAC7B,CAAC,CAAC;MACN;MAEA;MACAsD,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;MACvE,IAAI,CAACjF,qBAAqB,GAAG,CAAC,CAAC;MAC/B,IAAI,CAACoE,gBAAgB,GAAG,IAAI;MAC5B,IAAI,CAACG,oBAAoB,GAAG,IAAI;MAChC,IAAI,CAACD,yBAAyB,GAAG,CAAC,CAAC;IACrC,CAAC,CAAC,OAAOoD,KAAK,EAAE;MACd;MACA1C,OAAO,CAAC0C,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,IAAI,CAAChG,YAAY,GAAG,KAAK;IAC3B;EACF;EAEA;EACA5B,cAAcA,CAACD,KAAa;IAC1B,IAAIA,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAI,IAAI,CAACM,eAAe,CAACtB,MAAM,EAAE;MACrDmG,OAAO,CAACoC,IAAI,CAAC,2BAA2BvH,KAAK,gBAAgB,IAAI,CAACM,eAAe,CAACtB,MAAM,GAAG,CAAC,EAAE,CAAC;MAC/F;IACF;IAEA,IAAI,CAACmB,qBAAqB,GAAGH,KAAK;IAClC,IAAI,CAACuE,gBAAgB,GAAG,IAAI,CAACjE,eAAe,CAACN,KAAK,CAAC;IACnD,IAAI,CAAC0E,oBAAoB,GAAG,IAAI,CAACpE,eAAe,CAACN,KAAK,CAAC;IAEvD;IACA,IAAI,CAACyE,yBAAyB,GAAGzE,KAAK;IAEtCmF,OAAO,CAACC,GAAG,CAAC,8BAA8BpF,KAAK,MAAM,IAAI,CAAC0E,oBAAoB,GAAG,CAAC;EACpF;EAEA;EACAvC,oBAAoBA,CAACnC,KAAa;IAChC,IAAI,CAACoC,mBAAmB,GAAGpC,KAAK;IAChCmF,OAAO,CAACC,GAAG,CAAC,qCAAqCpF,KAAK,KAAK,IAAI,CAAC6C,qBAAqB,EAAE,CAAC7C,KAAK,CAAC,CAACsC,IAAI,EAAE,CAAC;EACxG;EAEA;EACAI,YAAYA,CAAA;IACV,IAAI,IAAI,CAACN,mBAAmB,KAAK,IAAI,EAAE;IAEvC,IAAI,CAACgC,WAAW,CAAC1B,YAAY,CAAC,IAAI,CAACN,mBAAmB,CAAC;EACzD;EAEA;EACAQ,WAAWA,CAAA;IACT,IAAI,CAACwB,WAAW,CAACxB,WAAW,EAAE;EAChC;EAEA;EACAmF,SAASA,CAAA;IACP;IACA,IAAI,CAACvD,UAAU,GAAG,KAAK;IACvBW,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;IAElE,IAAI,CAAChB,WAAW,CAACiD,YAAY,EAAE;EACjC;EAEA;EACA3D,SAASA,CAAA;IACP;IACA,IAAI,CAACc,UAAU,GAAG,KAAK;IACvBW,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;IAE3D,IAAI,CAAChB,WAAW,CAAC4D,SAAS,EAAE;EAC9B;EAEA;EACApE,OAAOA,CAAA;IACL,IAAI,CAACQ,WAAW,CAAC6D,SAAS,EAAE;EAC9B;EAEA;EACMC,SAASA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACb,MAAMC,KAAK,SAASF,KAAI,CAAC7D,eAAe,CAACgE,MAAM,CAAC;QAC9CC,MAAM,EAAE,YAAY;QACpBC,OAAO,EAAE,0CAA0C;QACnDC,OAAO,EAAE,CACP;UACEnG,IAAI,EAAE,IAAI;UACVoG,IAAI,EAAE,QAAQ;UACdC,QAAQ,EAAE,WAAW;UACrBC,OAAO,EAAEA,CAAA,KAAK;YACZzD,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;UACrC;SACD,EAAE;UACD9C,IAAI,EAAE,KAAK;UACXsG,OAAO,EAAEA,CAAA,KAAK;YACZzD,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;YACpC+C,KAAI,CAAC/D,WAAW,CAAC8D,SAAS,EAAE,CAACT,IAAI,CAAC,MAAK;cACrCU,KAAI,CAAC9D,MAAM,CAAC+B,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;YACjC,CAAC,CAAC;UACJ;SACD;OAEJ,CAAC;MAEF,MAAMiC,KAAK,CAACQ,OAAO,EAAE;IAAC;EACxB;EAEA;EACA5F,cAAcA,CAAC/E,UAAkB;IAC/B,IAAI,CAAC,IAAI,CAACsB,SAAS,IAAI,CAAC,IAAI,CAACA,SAAS,CAACsJ,aAAa,EAAE,OAAO,CAAC;IAC9D,OAAO,IAAI,CAACtJ,SAAS,CAACsJ,aAAa,CAAC5K,UAAU,CAAC,IAAI,CAAC;EACtD;EAEA;EACA+F,aAAaA,CAAA;IACX,IAAI,CAAC,IAAI,CAACzE,SAAS,IAAI,CAAC,IAAI,CAACA,SAAS,CAACsJ,aAAa,IAAI,CAAC,IAAI,CAACtJ,SAAS,CAACE,OAAO,EAAE;MAC/E,OAAO,SAAS;IAClB;IAEA,IAAIqJ,YAAY,GAAG,CAAC;IACpB,IAAI3F,MAAM,GAAG,SAAS;IAEtB,KAAK,MAAM4F,MAAM,IAAI,IAAI,CAACxJ,SAAS,CAACE,OAAO,EAAE;MAC3C,MAAMuJ,KAAK,GAAG,IAAI,CAAChG,cAAc,CAAC+F,MAAM,CAAC;MACzC,IAAIC,KAAK,GAAGF,YAAY,EAAE;QACxBA,YAAY,GAAGE,KAAK;QACpB7F,MAAM,GAAG4F,MAAM;MACjB;IACF;IAEA,OAAO5F,MAAM;EACf;EAEA;EACArE,UAAUA,CAAA;IAAA,IAAAmK,gBAAA;IACR,OAAO,EAAAA,gBAAA,OAAI,CAAC1J,SAAS,cAAA0J,gBAAA,uBAAdA,gBAAA,CAAgBxJ,OAAO,KAAI,EAAE;EACtC;EAEAP,eAAeA,CAAA;IAAA,IAAAgK,gBAAA;IACb,OAAO,EAAAA,gBAAA,OAAI,CAAC3J,SAAS,cAAA2J,gBAAA,uBAAdA,gBAAA,CAAgBjD,aAAa,KAAI,CAAC;EAC3C;EAEA5G,SAASA,CAAA;IAAA,IAAA8J,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA;IACP;IACA,IAAI,IAAI,CAAC/E,UAAU,EAAE;MACnBW,OAAO,CAACC,GAAG,CAAC,+DAA+D,CAAC;MAC5E,OAAO,EAAE;IACX;IAEA;IACA,IAAI,EAAAgE,gBAAA,OAAI,CAAC5J,SAAS,cAAA4J,gBAAA,uBAAdA,gBAAA,CAAgB5D,MAAM,MAAK,eAAe,EAAE;MAC9CL,OAAO,CAACC,GAAG,CAAC,0EAA0E,CAAC;MACvF,OAAO,EAAE;IACX;IAEAD,OAAO,CAACC,GAAG,CAAC,2BAA2B,GAAAiE,gBAAA,GAAE,IAAI,CAAC7J,SAAS,cAAA6J,gBAAA,uBAAdA,gBAAA,CAAgB7D,MAAM,CAAC;IAChEL,OAAO,CAACC,GAAG,CAAC,wBAAwB,GAAAkE,gBAAA,GAAE,IAAI,CAAC9J,SAAS,cAAA8J,gBAAA,uBAAdA,gBAAA,CAAgB7D,cAAc,CAAC;IAErE,MAAMC,MAAM,GAAG,EAAA6D,gBAAA,OAAI,CAAC/J,SAAS,cAAA+J,gBAAA,gBAAAA,gBAAA,GAAdA,gBAAA,CAAgB9D,cAAc,cAAA8D,gBAAA,uBAA9BA,gBAAA,CAAgC7D,MAAM,KAAI,EAAE;IAC3DP,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEM,MAAM,CAAC;IAEjD;IACA,IAAIA,MAAM,EAAE;MACV,OAAOA,MAAM;IACf;IAEA;IACA,OAAO,EAAE;EACX;EAEA7C,qBAAqBA,CAAA;IAAA,IAAA2G,gBAAA;IACnB,OAAO,EAAAA,gBAAA,OAAI,CAAChK,SAAS,cAAAgK,gBAAA,uBAAdA,gBAAA,CAAgB/J,mBAAmB,KAAI,EAAE;EAClD;EAEA;EACA,IAAIR,QAAQA,CAAA;IACV,OAAO,IAAI,CAACmF,WAAW,CAACnF,QAAQ;EAClC;EAEA,IAAIf,UAAUA,CAAA;IACZ,OAAO,IAAI,CAACkG,WAAW,CAAClG,UAAU,IAAI,SAAS;EACjD;EAEA,IAAIW,QAAQA,CAAA;IAAA,IAAA4K,gBAAA;IACV,OAAO,EAAAA,gBAAA,OAAI,CAACjK,SAAS,cAAAiK,gBAAA,uBAAdA,gBAAA,CAAgBC,SAAS,KAAI,EAAE;EACxC;EAEA;EACQ7C,2BAA2BA,CAACJ,SAAmB;IACrDtB,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEqB,SAAS,CAAC;IAC3DtB,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAE,IAAI,CAACvD,YAAY,CAAC;IAE5E;IACA,MAAM8H,oBAAoB,GAAG,IAAI,CAACjF,oBAAoB;IACtD,MAAMkF,qBAAqB,GAAG,IAAI,CAACzJ,qBAAqB;IACxD,MAAMuG,mBAAmB,GAAG,IAAI,CAAC1F,YAAY;IAC7C,MAAM2F,qBAAqB,GAAG,IAAI,CAAC/F,cAAc;IAEjD;IACA;IAEA;IACAuE,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE;MAC1CuE,oBAAoB;MACpBC,qBAAqB;MACrB5I,YAAY,EAAE0F,mBAAmB;MACjC9F,cAAc,EAAE+F,qBAAqB;MACrC9E,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/BgI,gBAAgB,EAAE,CAAC,GAAG,IAAI,CAACvJ,eAAe;KAC3C,CAAC;IAEF;IACA,IAAI,CAACA,eAAe,GAAG,CAAC,GAAGmG,SAAS,CAAC,CAAC,CAAC;IAEvC;IACA,IAAIkD,oBAAoB,IAAIC,qBAAqB,IAAI,CAAC,IAAIlD,mBAAmB,KAAK,OAAO,EAAE;MACzFvB,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAEuE,oBAAoB,CAAC;MAEnF;MACA,MAAMG,QAAQ,GAAG,IAAI,CAACxJ,eAAe,CAACyJ,SAAS,CAACzH,IAAI,IAAIA,IAAI,KAAKqH,oBAAoB,CAAC;MAEtF,IAAIG,QAAQ,IAAI,CAAC,EAAE;QACjB3E,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE0E,QAAQ,CAAC;QAC1D,IAAI,CAAC3J,qBAAqB,GAAG2J,QAAQ;QACrC,IAAI,CAACvF,gBAAgB,GAAGoF,oBAAoB;QAC5C,IAAI,CAACjF,oBAAoB,GAAGiF,oBAAoB,CAAC,CAAC;MACpD;MACA;MAAA,KACK,IAAIC,qBAAqB,GAAG,IAAI,CAACtJ,eAAe,CAACtB,MAAM,EAAE;QAC5DmG,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEwE,qBAAqB,CAAC;QACzE,IAAI,CAACzJ,qBAAqB,GAAGyJ,qBAAqB;QAClD,IAAI,CAACrF,gBAAgB,GAAG,IAAI,CAACjE,eAAe,CAACsJ,qBAAqB,CAAC;QACnE,IAAI,CAAClF,oBAAoB,GAAG,IAAI,CAACH,gBAAgB;MACnD;MACA;MAAA,KACK;QACHY,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;QACrD,IAAI,CAACjF,qBAAqB,GAAG,CAAC,CAAC;QAC/B,IAAI,CAACoE,gBAAgB,GAAG,IAAI;QAC5B,IAAI,CAACG,oBAAoB,GAAG,IAAI;MAClC;IACF;IAEA;IACAS,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;MACjCjF,qBAAqB,EAAE,IAAI,CAACA,qBAAqB;MACjDoE,gBAAgB,EAAE,IAAI,CAACA,gBAAgB;MACvCG,oBAAoB,EAAE,IAAI,CAACA,oBAAoB;MAC/C1D,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/BJ,cAAc,EAAE,IAAI,CAACA,cAAc;MACnCN,eAAe,EAAE,CAAC,GAAG,IAAI,CAACA,eAAe;KAC1C,CAAC;EACJ;;YAlmBW4D,QAAQ;;mCAARA,SAAQ,EAAA3G,EAAA,CAAAyM,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA3M,EAAA,CAAAyM,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAA7M,EAAA,CAAAyM,iBAAA,CAAAK,EAAA,CAAAC,eAAA;AAAA;;QAARpG,SAAQ;EAAAqG,SAAA;EAAAC,UAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,kBAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MCXjBvN,EAFJ,CAAAC,cAAA,oBAAiC,qBACF,gBAChB;MACTD,EAAA,CAAAE,MAAA,2BACF;MAAAF,EAAA,CAAAG,YAAA,EAAY;MAEVH,EADF,CAAAC,cAAA,qBAAwB,oBACY;MAAtBD,EAAA,CAAAY,UAAA,mBAAA6M,8CAAA;QAAA,OAASD,GAAA,CAAA7C,SAAA,EAAW;MAAA,EAAC;MAC/B3K,EAAA,CAAA0N,SAAA,kBAA0D;MAC1D1N,EAAA,CAAAE,MAAA,cACF;MAGNF,EAHM,CAAAG,YAAA,EAAa,EACD,EACF,EACH;MAEbH,EAAA,CAAAC,cAAA,qBAAqD;MA4NnDD,EA1NA,CAAAI,UAAA,IAAAuN,uBAAA,iBAAkE,KAAAC,wBAAA,kBAKe,KAAAC,wBAAA,iBA4BF,KAAAC,wBAAA,iBAcb,KAAAC,wBAAA,kBAeuB,KAAAC,wBAAA,iBAuFX,KAAAC,wBAAA,kBAuCJ,KAAAC,wBAAA,kBA8BJ;MAyCxElO,EAAA,CAAAG,YAAA,EAAc;;;MAnRFH,EAAA,CAAAS,UAAA,qBAAoB;MAcnBT,EAAA,CAAAM,SAAA,GAAmB;MAAnBN,EAAA,CAAAS,UAAA,oBAAmB;MAExBT,EAAA,CAAAM,SAAA,EAAgB;MAAhBN,EAAA,CAAAS,UAAA,UAAA+M,GAAA,CAAAvL,SAAA,CAAgB;MAKhBjC,EAAA,CAAAM,SAAA,EAAiD;MAAjDN,EAAA,CAAAS,UAAA,UAAA+M,GAAA,CAAAvL,SAAA,kBAAAuL,GAAA,CAAAvL,SAAA,CAAAgG,MAAA,4BAAiD;MA4BjDjI,EAAA,CAAAM,SAAA,EAAgD;MAAhDN,EAAA,CAAAS,UAAA,UAAA+M,GAAA,CAAAvL,SAAA,kBAAAuL,GAAA,CAAAvL,SAAA,CAAAgG,MAAA,2BAAgD;MAchDjI,EAAA,CAAAM,SAAA,EAAmC;MAAnCN,EAAA,CAAAS,UAAA,UAAA+M,GAAA,CAAAvL,SAAA,kBAAAuL,GAAA,CAAAvL,SAAA,CAAAgG,MAAA,cAAmC;MAenCjI,EAAA,CAAAM,SAAA,EAA0D;MAA1DN,EAAA,CAAAS,UAAA,UAAA+M,GAAA,CAAAvL,SAAA,kBAAAuL,GAAA,CAAAvL,SAAA,CAAAgG,MAAA,qCAA0D;MAuF1DjI,EAAA,CAAAM,SAAA,EAA+C;MAA/CN,EAAA,CAAAS,UAAA,UAAA+M,GAAA,CAAAvL,SAAA,kBAAAuL,GAAA,CAAAvL,SAAA,CAAAgG,MAAA,0BAA+C;MAuC/CjI,EAAA,CAAAM,SAAA,EAA2C;MAA3CN,EAAA,CAAAS,UAAA,UAAA+M,GAAA,CAAAvL,SAAA,kBAAAuL,GAAA,CAAAvL,SAAA,CAAAgG,MAAA,sBAA2C;MA8B3CjI,EAAA,CAAAM,SAAA,EAAuC;MAAvCN,EAAA,CAAAS,UAAA,UAAA+M,GAAA,CAAAvL,SAAA,kBAAAuL,GAAA,CAAAvL,SAAA,CAAAgG,MAAA,kBAAuC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}