{"ast": null, "code": "import _asyncToGenerator from \"D:/Godot_Games/cjsc/cjsc-web-client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nvar _FirebaseService;\nimport { initializeApp } from 'firebase/app';\nimport { getAuth, signInAnonymously, onAuthStateChanged } from 'firebase/auth';\nimport { getDatabase, ref, set, get, onValue, update } from 'firebase/database';\nimport { BehaviorSubject } from 'rxjs';\nimport { environment } from '../../environments/environment';\nimport * as i0 from \"@angular/core\";\nexport class FirebaseService {\n  constructor() {\n    this.app = initializeApp(environment.firebaseConfig);\n    this.userSubject = new BehaviorSubject(null);\n    this.user$ = this.userSubject.asObservable();\n    this.gameStateSubject = new BehaviorSubject(null);\n    this.gameState$ = this.gameStateSubject.asObservable();\n    this.gameId = null;\n    this.playerName = null;\n    this.isLeader = false;\n    this.auth = getAuth(this.app);\n    this.db = getDatabase(this.app);\n    // Listen for auth state changes\n    onAuthStateChanged(this.auth, user => {\n      this.userSubject.next(user);\n      // If we have a game ID and user, set up game state listener\n      if (this.gameId && user) {\n        this.setupGameListener();\n      }\n    });\n    // Check for stored player info\n    this.loadPlayerInfo();\n  }\n  // Sign in anonymously\n  signIn() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const result = yield signInAnonymously(_this.auth);\n        return result.user;\n      } catch (error) {\n        console.error('Error signing in:', error);\n        throw error;\n      }\n    })();\n  }\n  // Join a game with room code\n  joinGame(roomCode, playerName) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        console.log(`Attempting to join game with room code: ${roomCode}, player name: ${playerName}`);\n        // Make sure user is signed in\n        if (!_this2.auth.currentUser) {\n          console.log('User not signed in, signing in anonymously');\n          const user = yield _this2.signIn();\n          console.log('Anonymous sign-in successful, user:', user.uid);\n        } else {\n          console.log('User already signed in:', _this2.auth.currentUser.uid);\n        }\n        // Convert room code to uppercase\n        roomCode = roomCode.toUpperCase();\n        console.log(`Looking for game with room code: ${roomCode}`);\n        // Check if game exists\n        const gamesRef = ref(_this2.db, '/');\n        const gamesSnapshot = yield get(gamesRef);\n        if (!gamesSnapshot.exists()) {\n          console.error('No games found in database');\n          return false;\n        }\n        console.log('Games found in database, searching for matching room code');\n        // Find game with matching room code\n        let gameId = null;\n        gamesSnapshot.forEach(childSnapshot => {\n          const gameData = childSnapshot.val();\n          console.log(`Checking game ${childSnapshot.key}, room code: ${gameData.room_code}`);\n          if (gameData.room_code === roomCode) {\n            gameId = childSnapshot.key;\n            console.log(`Found matching game with ID: ${gameId}`);\n            return true; // Break the forEach loop\n          }\n          return false;\n        });\n        if (!gameId) {\n          console.error(`No game found with room code: ${roomCode}`);\n          return false;\n        }\n        // Store game ID and player name\n        _this2.gameId = gameId;\n        _this2.playerName = playerName;\n        console.log(`Storing game ID: ${gameId} and player name: ${playerName}`);\n        _this2.savePlayerInfo();\n        // Add player to game\n        console.log(`Adding player ${playerName} to game ${gameId}`);\n        yield _this2.addPlayerToGame(playerName);\n        // Set up game state listener\n        console.log('Setting up game state listener');\n        _this2.setupGameListener();\n        // Manually fetch the initial game state to ensure we have it\n        console.log('Manually fetching initial game state');\n        const gameRef = ref(_this2.db, `${gameId}`);\n        const gameSnapshot = yield get(gameRef);\n        if (gameSnapshot.exists()) {\n          const gameData = gameSnapshot.val();\n          console.log('Initial game state fetched:', gameData);\n          _this2.gameStateSubject.next(gameData);\n        } else {\n          console.error('Failed to fetch initial game state');\n        }\n        console.log('Join game process completed successfully');\n        return true;\n      } catch (error) {\n        console.error('Error joining game:', error);\n        return false;\n      }\n    })();\n  }\n  // Add player to game\n  addPlayerToGame(playerName) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this3.gameId) {\n        console.error('Cannot add player to game: gameId is null');\n        return;\n      }\n      try {\n        console.log(`Adding player ${playerName} to game ${_this3.gameId}`);\n        // Get current players\n        const playersRef = ref(_this3.db, `${_this3.gameId}/players`);\n        const playersSnapshot = yield get(playersRef);\n        let players = [];\n        if (playersSnapshot.exists()) {\n          players = playersSnapshot.val() || [];\n          console.log('Current players:', players);\n        } else {\n          console.log('No players found, creating new players array');\n        }\n        // Check if player is already in the game\n        if (!players.includes(playerName)) {\n          console.log(`Player ${playerName} not in game, adding to players array`);\n          players.push(playerName);\n          yield set(playersRef, players);\n          console.log(`Player ${playerName} added to game`);\n        } else {\n          console.log(`Player ${playerName} already in game`);\n        }\n        // Check if this is the first player (leader)\n        _this3.isLeader = players.length === 1 || players[0] === playerName;\n        console.log(`Player ${playerName} is ${_this3.isLeader ? 'the leader' : 'not the leader'}`);\n        // Initialize player score if not already set\n        const playerScoreRef = ref(_this3.db, `${_this3.gameId}/player_scores/${playerName}`);\n        const scoreSnapshot = yield get(playerScoreRef);\n        if (!scoreSnapshot.exists()) {\n          console.log(`Initializing score for player ${playerName}`);\n          yield set(playerScoreRef, 0);\n        }\n      } catch (error) {\n        console.error('Error adding player to game:', error);\n      }\n    })();\n  }\n  // Set up listener for game state changes\n  setupGameListener() {\n    if (!this.gameId) {\n      console.error('Cannot setup game listener: gameId is null');\n      return;\n    }\n    console.log(`Setting up game listener for gameId: ${this.gameId}`);\n    const gameRef = ref(this.db, `${this.gameId}`);\n    onValue(gameRef, snapshot => {\n      console.log('Game data received from Firebase:', snapshot.exists() ? 'Data exists' : 'No data');\n      const gameData = snapshot.val();\n      if (gameData) {\n        console.log('Game status:', gameData.status);\n        console.log('Game data:', gameData);\n        this.gameStateSubject.next(gameData);\n      } else {\n        console.error('Game data is null or undefined');\n      }\n    }, error => {\n      console.error('Error in game listener:', error);\n    });\n    console.log('Game listener setup complete');\n  }\n  // Update game status\n  updateGameStatus(status) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this4.gameId) return;\n      try {\n        console.log(`===== UPDATING GAME STATUS TO: ${status} =====`);\n        // Get current game state to log for debugging\n        const gameRef = ref(_this4.db, `${_this4.gameId}`);\n        const gameSnapshot = yield get(gameRef);\n        if (gameSnapshot.exists()) {\n          const gameData = gameSnapshot.val();\n          console.log(`Current game state before status update:`, {\n            currentStatus: gameData.status,\n            currentJudgeIndex: gameData.current_judge_index || 0,\n            players: gameData.players || [],\n            currentRound: gameData.current_round || 1\n          });\n        }\n        // Update the status\n        const statusRef = ref(_this4.db, `${_this4.gameId}/status`);\n        yield set(statusRef, status);\n        console.log(`Game status updated to: ${status}`);\n        console.log(`===== END UPDATING GAME STATUS =====`);\n      } catch (error) {\n        console.error('Error updating game status:', error);\n        console.log(`===== END UPDATING GAME STATUS (WITH ERROR) =====`);\n      }\n    })();\n  }\n  // Update prompt - DISABLED: Only Godot client should update prompts\n  // This function is kept for reference but should not be used\n  updatePrompt(_prompt) {\n    return _asyncToGenerator(function* () {\n      console.warn('WARNING: Web client should not update prompts. This is handled by the Godot client.');\n      return;\n    })();\n  }\n  // Update current prompt to \"Incoming Prompt!\"\n  // This is used when transitioning to \"Waiting for Prompt\" state\n  updateCurrentPromptToIncoming() {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this5.gameId) return;\n      try {\n        console.log(`===== UPDATING CURRENT PROMPT TO \"Incoming Prompt!\" =====`);\n        // Set the current_prompt to an object with \"Incoming Prompt!\" as the prompt\n        const promptRef = ref(_this5.db, `${_this5.gameId}/current_prompt`);\n        yield set(promptRef, {\n          prompt: \"Incoming Prompt!\"\n        });\n        console.log(`Current prompt updated to \"Incoming Prompt!\"`);\n        console.log(`===== END UPDATING CURRENT PROMPT =====`);\n      } catch (error) {\n        console.error('Error updating current prompt:', error);\n        console.log(`===== END UPDATING CURRENT PROMPT (WITH ERROR) =====`);\n      }\n    })();\n  }\n  // Submit player response\n  submitResponse(responseText, responseIndex) {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this6.gameId || !_this6.playerName) return;\n      try {\n        console.log(`==== SUBMITTING RESPONSE IN FIREBASE SERVICE ====`);\n        console.log(`Player ${_this6.playerName} is submitting response index ${responseIndex}: \"${responseText}\"`);\n        // 1. Add to submitted responses\n        const responsesRef = ref(_this6.db, `${_this6.gameId}/submitted_responses`);\n        const responsesSnapshot = yield get(responsesRef);\n        let responses = [];\n        if (responsesSnapshot.exists()) {\n          responses = responsesSnapshot.val() || [];\n        }\n        // Check if this player has already submitted a response\n        const existingResponseIndex = responses.findIndex(r => r.player_name === _this6.playerName);\n        if (existingResponseIndex >= 0) {\n          // Replace the existing response\n          console.log(`Player ${_this6.playerName} already submitted a response, replacing it`);\n          responses[existingResponseIndex] = {\n            text: responseText,\n            player_name: _this6.playerName\n          };\n        } else {\n          // Add a new response\n          console.log(`Adding new response for player ${_this6.playerName}`);\n          responses.push({\n            text: responseText,\n            player_name: _this6.playerName\n          });\n        }\n        yield set(responsesRef, responses);\n        console.log(`Updated submitted_responses:`, responses);\n        // We no longer need to update player_response_texts, player_responses, or response_indices\n        // as the web client is now managing responses locally\n        console.log(`==== END SUBMITTING RESPONSE IN FIREBASE SERVICE ====`);\n        // Check if all players have submitted responses after a short delay\n        // This ensures Firebase has time to update before we check\n        setTimeout(() => {\n          console.log('Checking if all players have submitted responses after a short delay');\n          _this6.checkAllResponsesSubmitted();\n        }, 500);\n      } catch (error) {\n        console.error('Error submitting response:', error);\n        throw error;\n      }\n    })();\n  }\n  // Check if all players have submitted responses\n  checkAllResponsesSubmitted() {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this7.gameId) return;\n      try {\n        console.log(`===== CHECKING ALL RESPONSES SUBMITTED =====`);\n        // Get current game state\n        const gameRef = ref(_this7.db, `${_this7.gameId}`);\n        const gameSnapshot = yield get(gameRef);\n        if (!gameSnapshot.exists()) {\n          console.log(`Game data doesn't exist, aborting check`);\n          console.log(`===== END CHECKING ALL RESPONSES SUBMITTED =====`);\n          return;\n        }\n        const gameData = gameSnapshot.val();\n        // Only proceed if we're in the right state\n        if (gameData.status !== 'Waiting for Player Responses') {\n          console.log(`Game status is ${gameData.status}, not checking responses`);\n          console.log(`===== END CHECKING ALL RESPONSES SUBMITTED =====`);\n          return;\n        }\n        const players = gameData.players || [];\n        const responses = gameData.submitted_responses || [];\n        // Find the judge\n        const judgeIndex = gameData.current_judge_index || 0;\n        const judge = players[judgeIndex];\n        // Count non-judge players\n        const nonJudgePlayers = players.filter(p => p !== judge);\n        console.log('Response check:', {\n          totalPlayers: players.length,\n          nonJudgePlayers: nonJudgePlayers.length,\n          submittedResponses: responses.length,\n          judge: judge\n        });\n        // Special case: if there's only one player (plus judge), auto-advance\n        if (nonJudgePlayers.length === 0) {\n          console.log('No non-judge players, auto-advancing to Ready for Judging');\n          yield _this7.updateGameStatus('Ready for Judging');\n          console.log(`===== END CHECKING ALL RESPONSES SUBMITTED =====`);\n          return;\n        }\n        // Special case: if all players have submitted (equal number of responses and non-judge players)\n        // This is a simpler check that should work in most cases\n        if (responses.length >= nonJudgePlayers.length) {\n          console.log('All players appear to have submitted responses, changing status to Ready for Judging');\n          yield _this7.updateGameStatus('Ready for Judging');\n          console.log(`===== END CHECKING ALL RESPONSES SUBMITTED =====`);\n          return;\n        }\n        // More detailed check if the simple check didn't pass\n        // Check if each player has submitted exactly one response\n        const playerResponseCount = {};\n        // Initialize counts to zero\n        nonJudgePlayers.forEach(player => {\n          playerResponseCount[player] = 0;\n        });\n        // Count responses for each player\n        responses.forEach(response => {\n          if (playerResponseCount[response.player_name] !== undefined) {\n            playerResponseCount[response.player_name]++;\n          }\n        });\n        // Check if all non-judge players have submitted at least one response\n        const allPlayersHaveAtLeastOneSubmission = Object.values(playerResponseCount).every(count => count > 0);\n        console.log('Player response counts:', playerResponseCount);\n        console.log('All players have at least one submission:', allPlayersHaveAtLeastOneSubmission);\n        // Update status if all non-judge players have submitted at least one response\n        if (allPlayersHaveAtLeastOneSubmission) {\n          console.log('All players have submitted at least one response, changing status to Ready for Judging');\n          yield _this7.updateGameStatus('Ready for Judging');\n        } else {\n          console.log('Waiting for more players to submit responses');\n        }\n        console.log(`===== END CHECKING ALL RESPONSES SUBMITTED =====`);\n      } catch (error) {\n        console.error('Error checking responses:', error);\n        console.log(`===== END CHECKING ALL RESPONSES SUBMITTED (WITH ERROR) =====`);\n      }\n    })();\n  }\n  // Judge selects a winning response\n  selectWinner(responseIndex) {\n    var _this8 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this8.gameId) return;\n      try {\n        console.log(`===== SELECT WINNER CALLED FOR RESPONSE INDEX ${responseIndex} =====`);\n        // Get the response at the given index\n        const responsesRef = ref(_this8.db, `${_this8.gameId}/submitted_responses/${responseIndex}`);\n        const responseSnapshot = yield get(responsesRef);\n        if (!responseSnapshot.exists()) {\n          console.log(`Response at index ${responseIndex} does not exist, aborting selectWinner`);\n          console.log(`===== END SELECT WINNER (ABORTED) =====`);\n          return;\n        }\n        const response = responseSnapshot.val();\n        console.log(`Selected winning response:`, response);\n        // Update player score\n        const playerScoreRef = ref(_this8.db, `${_this8.gameId}/player_scores/${response.player_name}`);\n        const scoreSnapshot = yield get(playerScoreRef);\n        let currentScore = 0;\n        if (scoreSnapshot.exists()) {\n          currentScore = scoreSnapshot.val() || 0;\n        }\n        // Calculate new score\n        const newScore = currentScore + 1;\n        console.log(`Updating score for ${response.player_name} from ${currentScore} to ${newScore}`);\n        // Check if player has reached 5 points (game over)\n        const gameOver = newScore >= 5;\n        console.log(`Game over check: ${gameOver} (${newScore} >= 5)`);\n        // Update winner in the game\n        const updates = {\n          [`${_this8.gameId}/winner`]: response.player_name,\n          [`${_this8.gameId}/winning_response`]: response.text,\n          [`${_this8.gameId}/player_scores/${response.player_name}`]: newScore,\n          [`${_this8.gameId}/status`]: gameOver ? 'Game Over' : 'Winner Chosen'\n        };\n        // If game is over, also set the game winner\n        if (gameOver) {\n          updates[`${_this8.gameId}/game_winner`] = response.player_name;\n          console.log(`Game over! ${response.player_name} has won with ${newScore} points!`);\n        }\n        console.log(`Updating Firebase with winner data:`, updates);\n        yield update(ref(_this8.db), updates);\n        console.log(`Firebase update completed`);\n        // Godot client now handles the countdown and new round logic\n        // No need for web client timeout\n        console.log(`===== END SELECT WINNER =====`);\n      } catch (error) {\n        console.error('Error selecting winner:', error);\n        console.log(`===== END SELECT WINNER (WITH ERROR) =====`);\n      }\n    })();\n  }\n  // Skip judging (can be called by any player)\n  skipJudging() {\n    var _this9 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this9.gameId) return;\n      try {\n        console.log(`===== SKIP JUDGING CALLED =====`);\n        // Update the game status to \"Winner Chosen\" with no winner\n        const updates = {\n          [`${_this9.gameId}/winner`]: \"No Winner\",\n          [`${_this9.gameId}/winning_response`]: \"Judging was skipped\",\n          [`${_this9.gameId}/status`]: 'Winner Chosen'\n        };\n        console.log(`Updating Firebase with skip data:`, updates);\n        yield update(ref(_this9.db), updates);\n        console.log(`Firebase update completed`);\n        console.log('Judging skipped, no winner selected');\n        // Godot client now handles the countdown and new round logic\n        // No need for web client timeout\n        console.log(`===== END SKIP JUDGING =====`);\n      } catch (error) {\n        console.error('Error skipping judging:', error);\n        console.log(`===== END SKIP JUDGING (WITH ERROR) =====`);\n      }\n    })();\n  }\n  // Helper method to check game status before starting a new round\n  // This helps prevent race conditions where multiple calls to startNewGame might occur\n  checkGameStatusBeforeNewRound() {\n    var _this10 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this10.gameId) return;\n      try {\n        console.log(`===== CHECKING GAME STATUS BEFORE NEW ROUND =====`);\n        // Get current game state\n        const gameRef = ref(_this10.db, `${_this10.gameId}`);\n        const gameSnapshot = yield get(gameRef);\n        if (!gameSnapshot.exists()) {\n          console.log('Game data does not exist, aborting check');\n          console.log(`===== END CHECKING GAME STATUS (ABORTED) =====`);\n          return;\n        }\n        const gameData = gameSnapshot.val();\n        console.log(`Current game status: ${gameData.status}`);\n        // Only proceed if we're still in Winner Chosen state\n        // This prevents multiple calls to startNewGame\n        if (gameData.status === 'Winner Chosen') {\n          console.log('Game is still in Winner Chosen state, starting new round');\n          // Update the current prompt to \"Incoming Prompt!\"\n          yield _this10.updateCurrentPromptToIncoming();\n          console.log('Current prompt updated to \"Incoming Prompt!\" before starting new game');\n          yield _this10.startNewGame();\n        } else {\n          console.log(`Game is in ${gameData.status} state, not starting new round`);\n        }\n        console.log(`===== END CHECKING GAME STATUS =====`);\n      } catch (error) {\n        console.error('Error checking game status:', error);\n        console.log(`===== END CHECKING GAME STATUS (WITH ERROR) =====`);\n      }\n    })();\n  }\n  // Leader starts a new game\n  startNewGame() {\n    var _this11 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this11.gameId || !_this11.isLeader) return;\n      try {\n        console.log(`===== START NEW GAME CALLED =====`);\n        // Get current game state\n        const gameRef = ref(_this11.db, `${_this11.gameId}`);\n        const gameSnapshot = yield get(gameRef);\n        if (!gameSnapshot.exists()) {\n          console.log('Game data does not exist, aborting startNewGame');\n          console.log(`===== END START NEW GAME (ABORTED) =====`);\n          return;\n        }\n        const gameData = gameSnapshot.val();\n        // Get current judge index and players array\n        const currentJudgeIndex = gameData.current_judge_index || 0;\n        const players = gameData.players || [];\n        // Log detailed information about the current state\n        console.log('Current game state:', {\n          currentJudgeIndex,\n          players,\n          currentRound: gameData.current_round || 1,\n          status: gameData.status\n        });\n        // Validate players array\n        if (!players || players.length < 2) {\n          console.error('Invalid players array:', players);\n          console.log(`===== END START NEW GAME (INVALID PLAYERS) =====`);\n          return;\n        }\n        // Calculate the next judge index (round-robin style)\n        let nextJudgeIndex = currentJudgeIndex + 1;\n        if (nextJudgeIndex >= players.length) {\n          nextJudgeIndex = 0;\n        }\n        console.log(`Updating judge from ${players[currentJudgeIndex]} (index ${currentJudgeIndex}) to ${players[nextJudgeIndex]} (index ${nextJudgeIndex})`);\n        console.log(`Full players array: ${JSON.stringify(players)}`);\n        // Update the judge index, clear submitted responses, and increment round\n        const updates = {\n          [`${_this11.gameId}/current_judge_index`]: nextJudgeIndex,\n          [`${_this11.gameId}/submitted_responses`]: [],\n          [`${_this11.gameId}/current_round`]: (gameData.current_round || 0) + 1\n        };\n        // Clear any response indices, player_response_texts, and player_responses\n        // This ensures we don't have any leftover data from the old approach\n        updates[`${_this11.gameId}/response_indices`] = null;\n        updates[`${_this11.gameId}/player_response_texts`] = null;\n        updates[`${_this11.gameId}/player_responses`] = null;\n        updates[`${_this11.gameId}/used_responses`] = null;\n        yield update(ref(_this11.db), updates);\n        console.log('Updated judge index, cleared responses, and incremented round');\n        // Update the current prompt to \"Incoming Prompt!\"\n        yield _this11.updateCurrentPromptToIncoming();\n        console.log('Current prompt updated to \"Incoming Prompt!\" before changing game status');\n        // Set game status to \"Waiting for Prompt\" to let Godot client know to choose a prompt\n        yield _this11.updateGameStatus('Waiting for Prompt');\n        console.log(`===== END START NEW GAME =====`);\n      } catch (error) {\n        console.error('Error starting new game:', error);\n        console.log(`===== END START NEW GAME (WITH ERROR) =====`);\n      }\n    })();\n  }\n  // Leader resets the game\n  resetGame() {\n    var _this12 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this12.gameId || !_this12.isLeader) return;\n      try {\n        // Reset player scores\n        const playerScoresRef = ref(_this12.db, `${_this12.gameId}/player_scores`);\n        yield set(playerScoresRef, {});\n        // Reset other game state\n        const updates = {\n          [`${_this12.gameId}/current_round`]: 1,\n          [`${_this12.gameId}/current_judge_index`]: 0,\n          // Reset judge index to 0\n          [`${_this12.gameId}/submitted_responses`]: [],\n          [`${_this12.gameId}/winner`]: null,\n          [`${_this12.gameId}/winning_response`]: null,\n          [`${_this12.gameId}/game_winner`]: null,\n          // Clear the game winner\n          // IMPORTANT: Set status to \"Waiting for Prompt\" instead of \"Ready\"\n          // This ensures the Godot client will select a new prompt\n          [`${_this12.gameId}/status`]: 'Waiting for Prompt'\n        };\n        // Clear any response indices, player_response_texts, and player_responses\n        // This ensures we don't have any leftover data from the old approach\n        updates[`${_this12.gameId}/response_indices`] = null;\n        updates[`${_this12.gameId}/player_response_texts`] = null;\n        updates[`${_this12.gameId}/player_responses`] = null;\n        updates[`${_this12.gameId}/used_responses`] = null;\n        yield update(ref(_this12.db), updates);\n        // Update the current prompt to \"Incoming Prompt!\"\n        yield _this12.updateCurrentPromptToIncoming();\n        console.log('Current prompt updated to \"Incoming Prompt!\"');\n        console.log('Game reset: judge index set to 0, round set to 1, scores reset, waiting for new prompt');\n      } catch (error) {\n        console.error('Error resetting game:', error);\n      }\n    })();\n  }\n  // Leader closes the game\n  closeGame() {\n    var _this13 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this13.gameId || !_this13.isLeader) return;\n      try {\n        yield _this13.updateGameStatus('Closed');\n      } catch (error) {\n        console.error('Error closing game:', error);\n      }\n    })();\n  }\n  // Save player info to local storage\n  savePlayerInfo() {\n    if (this.gameId && this.playerName) {\n      localStorage.setItem('gameId', this.gameId);\n      localStorage.setItem('playerName', this.playerName);\n      localStorage.setItem('isLeader', String(this.isLeader));\n    }\n  }\n  // Load player info from local storage\n  loadPlayerInfo() {\n    console.log('Loading player info from localStorage');\n    this.gameId = localStorage.getItem('gameId');\n    this.playerName = localStorage.getItem('playerName');\n    this.isLeader = localStorage.getItem('isLeader') === 'true';\n    console.log(`Loaded gameId: ${this.gameId || 'null'}`);\n    console.log(`Loaded playerName: ${this.playerName || 'null'}`);\n    console.log(`Loaded isLeader: ${this.isLeader}`);\n    // If we have a gameId but no game state listener, set it up\n    if (this.gameId && this.auth.currentUser) {\n      console.log('Found existing game session, setting up game listener');\n      this.setupGameListener();\n      // Manually fetch the initial game state\n      this.verifyGameData().then(() => {\n        console.log('Initial game state verified after loading player info');\n      });\n    }\n  }\n  // Clear player info from local storage\n  clearPlayerInfo() {\n    localStorage.removeItem('gameId');\n    localStorage.removeItem('playerName');\n    localStorage.removeItem('isLeader');\n    this.gameId = null;\n    this.playerName = null;\n    this.isLeader = false;\n  }\n  // Getters\n  get currentGameId() {\n    return this.gameId;\n  }\n  get currentPlayerName() {\n    return this.playerName;\n  }\n  get isGameLeader() {\n    return this.isLeader;\n  }\n  // Verify and fetch the latest game data directly from Firebase\n  verifyGameData() {\n    var _this14 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this14.gameId) return;\n      try {\n        console.log(`===== VERIFYING GAME DATA =====`);\n        // Get the latest game data directly from Firebase\n        const gameRef = ref(_this14.db, `${_this14.gameId}`);\n        const snapshot = yield get(gameRef);\n        if (!snapshot.exists()) {\n          console.log(`Game data doesn't exist, aborting verification`);\n          console.log(`===== END VERIFYING GAME DATA =====`);\n          return;\n        }\n        // Get the latest game data\n        const gameData = snapshot.val();\n        // Log the current prompt data for debugging\n        console.log('Current prompt in Firebase:', gameData.current_prompt);\n        // Manually trigger the game state update to ensure our local state is in sync\n        _this14.gameStateSubject.next(gameData);\n        // If no prompt is found and we're in Ready or Waiting for Player Responses state, log a warning\n        if ((!gameData.current_prompt || !gameData.current_prompt.prompt) && (gameData.status === 'Ready' || gameData.status === 'Waiting for Player Responses')) {\n          console.warn('No prompt found in Firebase during verification, game may need to restart prompt selection');\n          // If we're the leader, we could potentially trigger a new prompt selection\n          if (_this14.isLeader && (gameData.status === 'Ready' || gameData.status === 'Waiting for Player Responses')) {\n            console.log('Leader detected missing prompt, falling back to Waiting for Prompt state');\n            yield _this14.updateGameStatus('Waiting for Prompt');\n          }\n        }\n        console.log(`===== END VERIFYING GAME DATA =====`);\n      } catch (error) {\n        console.error('Error verifying game data:', error);\n      }\n    })();\n  }\n  // Method updatePlayerResponses has been removed\n  // Player responses are now managed locally by the web client\n  // This reduces unnecessary database writes\n  // Remove player from game\n  removePlayerFromGame() {\n    var _this15 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this15.gameId || !_this15.playerName) return;\n      try {\n        console.log(`===== REMOVING PLAYER FROM GAME =====`);\n        console.log(`Removing player ${_this15.playerName} from game ${_this15.gameId}`);\n        // Get current players\n        const playersRef = ref(_this15.db, `${_this15.gameId}/players`);\n        const playersSnapshot = yield get(playersRef);\n        if (!playersSnapshot.exists()) {\n          console.log('No players found, nothing to remove');\n          console.log(`===== END REMOVING PLAYER FROM GAME =====`);\n          return;\n        }\n        let players = playersSnapshot.val() || [];\n        console.log('Current players:', players);\n        // Check if player is in the game\n        const playerIndex = players.indexOf(_this15.playerName);\n        if (playerIndex === -1) {\n          console.log(`Player ${_this15.playerName} not found in game`);\n          console.log(`===== END REMOVING PLAYER FROM GAME =====`);\n          return;\n        }\n        // Remove player from array\n        players.splice(playerIndex, 1);\n        console.log('Updated players array:', players);\n        // Update players array in Firebase\n        yield set(playersRef, players);\n        console.log(`Player ${_this15.playerName} removed from game`);\n        // Clear player info from local storage\n        _this15.clearPlayerInfo();\n        console.log(`===== END REMOVING PLAYER FROM GAME =====`);\n      } catch (error) {\n        console.error('Error removing player from game:', error);\n        console.log(`===== END REMOVING PLAYER FROM GAME (WITH ERROR) =====`);\n      }\n    })();\n  }\n}\n_FirebaseService = FirebaseService;\n_FirebaseService.ɵfac = function FirebaseService_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _FirebaseService)();\n};\n_FirebaseService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n  token: _FirebaseService,\n  factory: _FirebaseService.ɵfac,\n  providedIn: 'root'\n});", "map": {"version": 3, "names": ["initializeApp", "getAuth", "signInAnonymously", "onAuthStateChanged", "getDatabase", "ref", "set", "get", "onValue", "update", "BehaviorSubject", "environment", "FirebaseService", "constructor", "app", "firebaseConfig", "userSubject", "user$", "asObservable", "gameStateSubject", "gameState$", "gameId", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "auth", "db", "user", "next", "setupGameListener", "loadPlayerInfo", "signIn", "_this", "_asyncToGenerator", "result", "error", "console", "joinGame", "roomCode", "_this2", "log", "currentUser", "uid", "toUpperCase", "gamesRef", "gamesSnapshot", "exists", "for<PERSON>ach", "childSnapshot", "gameData", "val", "key", "room_code", "savePlayerInfo", "addPlayerToGame", "gameRef", "gameSnapshot", "_this3", "playersRef", "playersSnapshot", "players", "includes", "push", "length", "playerScoreRef", "scoreSnapshot", "snapshot", "status", "updateGameStatus", "_this4", "currentStatus", "currentJudgeIndex", "current_judge_index", "currentRound", "current_round", "statusRef", "updatePrompt", "_prompt", "warn", "updateCurrentPromptToIncoming", "_this5", "promptRef", "prompt", "submitResponse", "responseText", "responseIndex", "_this6", "responsesRef", "responsesSnapshot", "responses", "existingResponseIndex", "findIndex", "r", "player_name", "text", "setTimeout", "checkAllResponsesSubmitted", "_this7", "submitted_responses", "judge<PERSON>ndex", "judge", "nonJudgePlayers", "filter", "p", "totalPlayers", "submittedResponses", "playerResponseCount", "player", "response", "undefined", "allPlayersHaveAtLeastOneSubmission", "Object", "values", "every", "count", "<PERSON><PERSON><PERSON><PERSON>", "_this8", "responseSnapshot", "currentScore", "newScore", "gameOver", "updates", "skipJudging", "_this9", "checkGameStatusBeforeNewRound", "_this10", "startNewGame", "_this11", "nextJudgeIndex", "JSON", "stringify", "resetGame", "_this12", "playerScoresRef", "closeGame", "_this13", "localStorage", "setItem", "String", "getItem", "verifyGameData", "then", "clearPlayerInfo", "removeItem", "currentGameId", "currentPlayerName", "isGameLeader", "_this14", "current_prompt", "removePlayerFromGame", "_this15", "playerIndex", "indexOf", "splice", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Godot_Games\\cjsc\\cjsc-web-client\\src\\app\\services\\firebase.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { initializeApp } from 'firebase/app';\nimport {\n  getAuth,\n  signInAnonymously,\n  onAuthStateChanged,\n  Auth,\n  User\n} from 'firebase/auth';\nimport {\n  getDatabase,\n  ref,\n  set,\n  get,\n  onValue,\n  update,\n  Database\n} from 'firebase/database';\nimport { BehaviorSubject } from 'rxjs';\nimport { environment } from '../../environments/environment';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class FirebaseService {\n  private app = initializeApp(environment.firebaseConfig);\n  private auth: Auth;\n  private db: Database;\n\n  private userSubject = new BehaviorSubject<User | null>(null);\n  public user$ = this.userSubject.asObservable();\n\n  private gameStateSubject = new BehaviorSubject<any>(null);\n  public gameState$ = this.gameStateSubject.asObservable();\n\n  private gameId: string | null = null;\n  private playerName: string | null = null;\n  private isLeader = false;\n\n  constructor() {\n    this.auth = getAuth(this.app);\n    this.db = getDatabase(this.app);\n\n    // Listen for auth state changes\n    onAuthStateChanged(this.auth, (user) => {\n      this.userSubject.next(user);\n\n      // If we have a game ID and user, set up game state listener\n      if (this.gameId && user) {\n        this.setupGameListener();\n      }\n    });\n\n    // Check for stored player info\n    this.loadPlayerInfo();\n  }\n\n  // Sign in anonymously\n  async signIn(): Promise<User> {\n    try {\n      const result = await signInAnonymously(this.auth);\n      return result.user;\n    } catch (error) {\n      console.error('Error signing in:', error);\n      throw error;\n    }\n  }\n\n  // Join a game with room code\n  async joinGame(roomCode: string, playerName: string): Promise<boolean> {\n    try {\n      console.log(`Attempting to join game with room code: ${roomCode}, player name: ${playerName}`);\n\n      // Make sure user is signed in\n      if (!this.auth.currentUser) {\n        console.log('User not signed in, signing in anonymously');\n        const user = await this.signIn();\n        console.log('Anonymous sign-in successful, user:', user.uid);\n      } else {\n        console.log('User already signed in:', this.auth.currentUser.uid);\n      }\n\n      // Convert room code to uppercase\n      roomCode = roomCode.toUpperCase();\n      console.log(`Looking for game with room code: ${roomCode}`);\n\n      // Check if game exists\n      const gamesRef = ref(this.db, '/');\n      const gamesSnapshot = await get(gamesRef);\n\n      if (!gamesSnapshot.exists()) {\n        console.error('No games found in database');\n        return false;\n      }\n\n      console.log('Games found in database, searching for matching room code');\n\n      // Find game with matching room code\n      let gameId = null;\n\n      gamesSnapshot.forEach((childSnapshot) => {\n        const gameData = childSnapshot.val();\n        console.log(`Checking game ${childSnapshot.key}, room code: ${gameData.room_code}`);\n\n        if (gameData.room_code === roomCode) {\n          gameId = childSnapshot.key;\n          console.log(`Found matching game with ID: ${gameId}`);\n          return true; // Break the forEach loop\n        }\n        return false;\n      });\n\n      if (!gameId) {\n        console.error(`No game found with room code: ${roomCode}`);\n        return false;\n      }\n\n      // Store game ID and player name\n      this.gameId = gameId;\n      this.playerName = playerName;\n      console.log(`Storing game ID: ${gameId} and player name: ${playerName}`);\n      this.savePlayerInfo();\n\n      // Add player to game\n      console.log(`Adding player ${playerName} to game ${gameId}`);\n      await this.addPlayerToGame(playerName);\n\n      // Set up game state listener\n      console.log('Setting up game state listener');\n      this.setupGameListener();\n\n      // Manually fetch the initial game state to ensure we have it\n      console.log('Manually fetching initial game state');\n      const gameRef = ref(this.db, `${gameId}`);\n      const gameSnapshot = await get(gameRef);\n\n      if (gameSnapshot.exists()) {\n        const gameData = gameSnapshot.val();\n        console.log('Initial game state fetched:', gameData);\n        this.gameStateSubject.next(gameData);\n      } else {\n        console.error('Failed to fetch initial game state');\n      }\n\n      console.log('Join game process completed successfully');\n      return true;\n    } catch (error) {\n      console.error('Error joining game:', error);\n      return false;\n    }\n  }\n\n  // Add player to game\n  private async addPlayerToGame(playerName: string): Promise<void> {\n    if (!this.gameId) {\n      console.error('Cannot add player to game: gameId is null');\n      return;\n    }\n\n    try {\n      console.log(`Adding player ${playerName} to game ${this.gameId}`);\n\n      // Get current players\n      const playersRef = ref(this.db, `${this.gameId}/players`);\n      const playersSnapshot = await get(playersRef);\n\n      let players: string[] = [];\n      if (playersSnapshot.exists()) {\n        players = playersSnapshot.val() || [];\n        console.log('Current players:', players);\n      } else {\n        console.log('No players found, creating new players array');\n      }\n\n      // Check if player is already in the game\n      if (!players.includes(playerName)) {\n        console.log(`Player ${playerName} not in game, adding to players array`);\n        players.push(playerName);\n        await set(playersRef, players);\n        console.log(`Player ${playerName} added to game`);\n      } else {\n        console.log(`Player ${playerName} already in game`);\n      }\n\n      // Check if this is the first player (leader)\n      this.isLeader = players.length === 1 || players[0] === playerName;\n      console.log(`Player ${playerName} is ${this.isLeader ? 'the leader' : 'not the leader'}`);\n\n      // Initialize player score if not already set\n      const playerScoreRef = ref(this.db, `${this.gameId}/player_scores/${playerName}`);\n      const scoreSnapshot = await get(playerScoreRef);\n\n      if (!scoreSnapshot.exists()) {\n        console.log(`Initializing score for player ${playerName}`);\n        await set(playerScoreRef, 0);\n      }\n\n    } catch (error) {\n      console.error('Error adding player to game:', error);\n    }\n  }\n\n  // Set up listener for game state changes\n  private setupGameListener(): void {\n    if (!this.gameId) {\n      console.error('Cannot setup game listener: gameId is null');\n      return;\n    }\n\n    console.log(`Setting up game listener for gameId: ${this.gameId}`);\n\n    const gameRef = ref(this.db, `${this.gameId}`);\n    onValue(gameRef, (snapshot) => {\n      console.log('Game data received from Firebase:', snapshot.exists() ? 'Data exists' : 'No data');\n\n      const gameData = snapshot.val();\n      if (gameData) {\n        console.log('Game status:', gameData.status);\n        console.log('Game data:', gameData);\n        this.gameStateSubject.next(gameData);\n      } else {\n        console.error('Game data is null or undefined');\n      }\n    }, (error) => {\n      console.error('Error in game listener:', error);\n    });\n\n    console.log('Game listener setup complete');\n  }\n\n  // Update game status\n  async updateGameStatus(status: string): Promise<void> {\n    if (!this.gameId) return;\n\n    try {\n      console.log(`===== UPDATING GAME STATUS TO: ${status} =====`);\n\n      // Get current game state to log for debugging\n      const gameRef = ref(this.db, `${this.gameId}`);\n      const gameSnapshot = await get(gameRef);\n\n      if (gameSnapshot.exists()) {\n        const gameData = gameSnapshot.val();\n        console.log(`Current game state before status update:`, {\n          currentStatus: gameData.status,\n          currentJudgeIndex: gameData.current_judge_index || 0,\n          players: gameData.players || [],\n          currentRound: gameData.current_round || 1\n        });\n      }\n\n      // Update the status\n      const statusRef = ref(this.db, `${this.gameId}/status`);\n      await set(statusRef, status);\n      console.log(`Game status updated to: ${status}`);\n\n      console.log(`===== END UPDATING GAME STATUS =====`);\n    } catch (error) {\n      console.error('Error updating game status:', error);\n      console.log(`===== END UPDATING GAME STATUS (WITH ERROR) =====`);\n    }\n  }\n\n  // Update prompt - DISABLED: Only Godot client should update prompts\n  // This function is kept for reference but should not be used\n  async updatePrompt(_prompt: string): Promise<void> {\n    console.warn('WARNING: Web client should not update prompts. This is handled by the Godot client.');\n    return;\n  }\n\n  // Update current prompt to \"Incoming Prompt!\"\n  // This is used when transitioning to \"Waiting for Prompt\" state\n  async updateCurrentPromptToIncoming(): Promise<void> {\n    if (!this.gameId) return;\n\n    try {\n      console.log(`===== UPDATING CURRENT PROMPT TO \"Incoming Prompt!\" =====`);\n\n      // Set the current_prompt to an object with \"Incoming Prompt!\" as the prompt\n      const promptRef = ref(this.db, `${this.gameId}/current_prompt`);\n      await set(promptRef, { prompt: \"Incoming Prompt!\" });\n\n      console.log(`Current prompt updated to \"Incoming Prompt!\"`);\n      console.log(`===== END UPDATING CURRENT PROMPT =====`);\n    } catch (error) {\n      console.error('Error updating current prompt:', error);\n      console.log(`===== END UPDATING CURRENT PROMPT (WITH ERROR) =====`);\n    }\n  }\n\n  // Submit player response\n  async submitResponse(responseText: string, responseIndex: number): Promise<void> {\n    if (!this.gameId || !this.playerName) return;\n\n    try {\n      console.log(`==== SUBMITTING RESPONSE IN FIREBASE SERVICE ====`);\n      console.log(`Player ${this.playerName} is submitting response index ${responseIndex}: \"${responseText}\"`);\n\n      // 1. Add to submitted responses\n      const responsesRef = ref(this.db, `${this.gameId}/submitted_responses`);\n      const responsesSnapshot = await get(responsesRef);\n\n      let responses: any[] = [];\n      if (responsesSnapshot.exists()) {\n        responses = responsesSnapshot.val() || [];\n      }\n\n      // Check if this player has already submitted a response\n      const existingResponseIndex = responses.findIndex(r => r.player_name === this.playerName);\n\n      if (existingResponseIndex >= 0) {\n        // Replace the existing response\n        console.log(`Player ${this.playerName} already submitted a response, replacing it`);\n        responses[existingResponseIndex] = {\n          text: responseText,\n          player_name: this.playerName\n        };\n      } else {\n        // Add a new response\n        console.log(`Adding new response for player ${this.playerName}`);\n        responses.push({\n          text: responseText,\n          player_name: this.playerName\n        });\n      }\n\n      await set(responsesRef, responses);\n      console.log(`Updated submitted_responses:`, responses);\n\n      // We no longer need to update player_response_texts, player_responses, or response_indices\n      // as the web client is now managing responses locally\n\n      console.log(`==== END SUBMITTING RESPONSE IN FIREBASE SERVICE ====`);\n\n      // Check if all players have submitted responses after a short delay\n      // This ensures Firebase has time to update before we check\n      setTimeout(() => {\n        console.log('Checking if all players have submitted responses after a short delay');\n        this.checkAllResponsesSubmitted();\n      }, 500);\n    } catch (error) {\n      console.error('Error submitting response:', error);\n      throw error;\n    }\n  }\n\n  // Check if all players have submitted responses\n  private async checkAllResponsesSubmitted(): Promise<void> {\n    if (!this.gameId) return;\n\n    try {\n      console.log(`===== CHECKING ALL RESPONSES SUBMITTED =====`);\n\n      // Get current game state\n      const gameRef = ref(this.db, `${this.gameId}`);\n      const gameSnapshot = await get(gameRef);\n\n      if (!gameSnapshot.exists()) {\n        console.log(`Game data doesn't exist, aborting check`);\n        console.log(`===== END CHECKING ALL RESPONSES SUBMITTED =====`);\n        return;\n      }\n\n      const gameData = gameSnapshot.val();\n\n      // Only proceed if we're in the right state\n      if (gameData.status !== 'Waiting for Player Responses') {\n        console.log(`Game status is ${gameData.status}, not checking responses`);\n        console.log(`===== END CHECKING ALL RESPONSES SUBMITTED =====`);\n        return;\n      }\n\n      const players = gameData.players || [];\n      const responses = gameData.submitted_responses || [];\n\n      // Find the judge\n      const judgeIndex = gameData.current_judge_index || 0;\n      const judge = players[judgeIndex];\n\n      // Count non-judge players\n      const nonJudgePlayers = players.filter((p: string) => p !== judge);\n\n      console.log('Response check:', {\n        totalPlayers: players.length,\n        nonJudgePlayers: nonJudgePlayers.length,\n        submittedResponses: responses.length,\n        judge: judge\n      });\n\n      // Special case: if there's only one player (plus judge), auto-advance\n      if (nonJudgePlayers.length === 0) {\n        console.log('No non-judge players, auto-advancing to Ready for Judging');\n        await this.updateGameStatus('Ready for Judging');\n        console.log(`===== END CHECKING ALL RESPONSES SUBMITTED =====`);\n        return;\n      }\n\n      // Special case: if all players have submitted (equal number of responses and non-judge players)\n      // This is a simpler check that should work in most cases\n      if (responses.length >= nonJudgePlayers.length) {\n        console.log('All players appear to have submitted responses, changing status to Ready for Judging');\n        await this.updateGameStatus('Ready for Judging');\n        console.log(`===== END CHECKING ALL RESPONSES SUBMITTED =====`);\n        return;\n      }\n\n      // More detailed check if the simple check didn't pass\n      // Check if each player has submitted exactly one response\n      const playerResponseCount: { [key: string]: number } = {};\n\n      // Initialize counts to zero\n      nonJudgePlayers.forEach((player: string) => {\n        playerResponseCount[player] = 0;\n      });\n\n      // Count responses for each player\n      responses.forEach((response: { player_name: string; text: string }) => {\n        if (playerResponseCount[response.player_name] !== undefined) {\n          playerResponseCount[response.player_name]++;\n        }\n      });\n\n      // Check if all non-judge players have submitted at least one response\n      const allPlayersHaveAtLeastOneSubmission = Object.values(playerResponseCount).every(count => count > 0);\n\n      console.log('Player response counts:', playerResponseCount);\n      console.log('All players have at least one submission:', allPlayersHaveAtLeastOneSubmission);\n\n      // Update status if all non-judge players have submitted at least one response\n      if (allPlayersHaveAtLeastOneSubmission) {\n        console.log('All players have submitted at least one response, changing status to Ready for Judging');\n        await this.updateGameStatus('Ready for Judging');\n      } else {\n        console.log('Waiting for more players to submit responses');\n      }\n\n      console.log(`===== END CHECKING ALL RESPONSES SUBMITTED =====`);\n    } catch (error) {\n      console.error('Error checking responses:', error);\n      console.log(`===== END CHECKING ALL RESPONSES SUBMITTED (WITH ERROR) =====`);\n    }\n  }\n\n  // Judge selects a winning response\n  async selectWinner(responseIndex: number): Promise<void> {\n    if (!this.gameId) return;\n\n    try {\n      console.log(`===== SELECT WINNER CALLED FOR RESPONSE INDEX ${responseIndex} =====`);\n\n      // Get the response at the given index\n      const responsesRef = ref(this.db, `${this.gameId}/submitted_responses/${responseIndex}`);\n      const responseSnapshot = await get(responsesRef);\n\n      if (!responseSnapshot.exists()) {\n        console.log(`Response at index ${responseIndex} does not exist, aborting selectWinner`);\n        console.log(`===== END SELECT WINNER (ABORTED) =====`);\n        return;\n      }\n\n      const response = responseSnapshot.val();\n      console.log(`Selected winning response:`, response);\n\n      // Update player score\n      const playerScoreRef = ref(this.db, `${this.gameId}/player_scores/${response.player_name}`);\n      const scoreSnapshot = await get(playerScoreRef);\n\n      let currentScore = 0;\n      if (scoreSnapshot.exists()) {\n        currentScore = scoreSnapshot.val() || 0;\n      }\n\n      // Calculate new score\n      const newScore = currentScore + 1;\n      console.log(`Updating score for ${response.player_name} from ${currentScore} to ${newScore}`);\n\n      // Check if player has reached 5 points (game over)\n      const gameOver = newScore >= 5;\n      console.log(`Game over check: ${gameOver} (${newScore} >= 5)`);\n\n      // Update winner in the game\n      const updates = {\n        [`${this.gameId}/winner`]: response.player_name,\n        [`${this.gameId}/winning_response`]: response.text,\n        [`${this.gameId}/player_scores/${response.player_name}`]: newScore,\n        [`${this.gameId}/status`]: gameOver ? 'Game Over' : 'Winner Chosen'\n      };\n\n      // If game is over, also set the game winner\n      if (gameOver) {\n        updates[`${this.gameId}/game_winner`] = response.player_name;\n        console.log(`Game over! ${response.player_name} has won with ${newScore} points!`);\n      }\n\n      console.log(`Updating Firebase with winner data:`, updates);\n      await update(ref(this.db), updates);\n      console.log(`Firebase update completed`);\n\n      // Godot client now handles the countdown and new round logic\n      // No need for web client timeout\n\n      console.log(`===== END SELECT WINNER =====`);\n    } catch (error) {\n      console.error('Error selecting winner:', error);\n      console.log(`===== END SELECT WINNER (WITH ERROR) =====`);\n    }\n  }\n\n  // Skip judging (can be called by any player)\n  async skipJudging(): Promise<void> {\n    if (!this.gameId) return;\n\n    try {\n      console.log(`===== SKIP JUDGING CALLED =====`);\n\n      // Update the game status to \"Winner Chosen\" with no winner\n      const updates = {\n        [`${this.gameId}/winner`]: \"No Winner\",\n        [`${this.gameId}/winning_response`]: \"Judging was skipped\",\n        [`${this.gameId}/status`]: 'Winner Chosen'\n      };\n\n      console.log(`Updating Firebase with skip data:`, updates);\n      await update(ref(this.db), updates);\n      console.log(`Firebase update completed`);\n\n      console.log('Judging skipped, no winner selected');\n\n      // Godot client now handles the countdown and new round logic\n      // No need for web client timeout\n\n      console.log(`===== END SKIP JUDGING =====`);\n    } catch (error) {\n      console.error('Error skipping judging:', error);\n      console.log(`===== END SKIP JUDGING (WITH ERROR) =====`);\n    }\n  }\n\n  // Helper method to check game status before starting a new round\n  // This helps prevent race conditions where multiple calls to startNewGame might occur\n  private async checkGameStatusBeforeNewRound(): Promise<void> {\n    if (!this.gameId) return;\n\n    try {\n      console.log(`===== CHECKING GAME STATUS BEFORE NEW ROUND =====`);\n\n      // Get current game state\n      const gameRef = ref(this.db, `${this.gameId}`);\n      const gameSnapshot = await get(gameRef);\n\n      if (!gameSnapshot.exists()) {\n        console.log('Game data does not exist, aborting check');\n        console.log(`===== END CHECKING GAME STATUS (ABORTED) =====`);\n        return;\n      }\n\n      const gameData = gameSnapshot.val();\n      console.log(`Current game status: ${gameData.status}`);\n\n      // Only proceed if we're still in Winner Chosen state\n      // This prevents multiple calls to startNewGame\n      if (gameData.status === 'Winner Chosen') {\n        console.log('Game is still in Winner Chosen state, starting new round');\n\n        // Update the current prompt to \"Incoming Prompt!\"\n        await this.updateCurrentPromptToIncoming();\n        console.log('Current prompt updated to \"Incoming Prompt!\" before starting new game');\n\n        await this.startNewGame();\n      } else {\n        console.log(`Game is in ${gameData.status} state, not starting new round`);\n      }\n\n      console.log(`===== END CHECKING GAME STATUS =====`);\n    } catch (error) {\n      console.error('Error checking game status:', error);\n      console.log(`===== END CHECKING GAME STATUS (WITH ERROR) =====`);\n    }\n  }\n\n  // Leader starts a new game\n  async startNewGame(): Promise<void> {\n    if (!this.gameId || !this.isLeader) return;\n\n    try {\n      console.log(`===== START NEW GAME CALLED =====`);\n\n      // Get current game state\n      const gameRef = ref(this.db, `${this.gameId}`);\n      const gameSnapshot = await get(gameRef);\n\n      if (!gameSnapshot.exists()) {\n        console.log('Game data does not exist, aborting startNewGame');\n        console.log(`===== END START NEW GAME (ABORTED) =====`);\n        return;\n      }\n\n      const gameData = gameSnapshot.val();\n\n      // Get current judge index and players array\n      const currentJudgeIndex = gameData.current_judge_index || 0;\n      const players = gameData.players || [];\n\n      // Log detailed information about the current state\n      console.log('Current game state:', {\n        currentJudgeIndex,\n        players,\n        currentRound: gameData.current_round || 1,\n        status: gameData.status\n      });\n\n      // Validate players array\n      if (!players || players.length < 2) {\n        console.error('Invalid players array:', players);\n        console.log(`===== END START NEW GAME (INVALID PLAYERS) =====`);\n        return;\n      }\n\n      // Calculate the next judge index (round-robin style)\n      let nextJudgeIndex = currentJudgeIndex + 1;\n      if (nextJudgeIndex >= players.length) {\n        nextJudgeIndex = 0;\n      }\n\n      console.log(`Updating judge from ${players[currentJudgeIndex]} (index ${currentJudgeIndex}) to ${players[nextJudgeIndex]} (index ${nextJudgeIndex})`);\n      console.log(`Full players array: ${JSON.stringify(players)}`);\n\n      // Update the judge index, clear submitted responses, and increment round\n      const updates = {\n        [`${this.gameId}/current_judge_index`]: nextJudgeIndex,\n        [`${this.gameId}/submitted_responses`]: [],\n        [`${this.gameId}/current_round`]: (gameData.current_round || 0) + 1\n      };\n\n      // Clear any response indices, player_response_texts, and player_responses\n      // This ensures we don't have any leftover data from the old approach\n      updates[`${this.gameId}/response_indices`] = null;\n      updates[`${this.gameId}/player_response_texts`] = null;\n      updates[`${this.gameId}/player_responses`] = null;\n      updates[`${this.gameId}/used_responses`] = null;\n\n      await update(ref(this.db), updates);\n\n      console.log('Updated judge index, cleared responses, and incremented round');\n\n      // Update the current prompt to \"Incoming Prompt!\"\n      await this.updateCurrentPromptToIncoming();\n      console.log('Current prompt updated to \"Incoming Prompt!\" before changing game status');\n\n      // Set game status to \"Waiting for Prompt\" to let Godot client know to choose a prompt\n      await this.updateGameStatus('Waiting for Prompt');\n\n      console.log(`===== END START NEW GAME =====`);\n    } catch (error) {\n      console.error('Error starting new game:', error);\n      console.log(`===== END START NEW GAME (WITH ERROR) =====`);\n    }\n  }\n\n  // Leader resets the game\n  async resetGame(): Promise<void> {\n    if (!this.gameId || !this.isLeader) return;\n\n    try {\n      // Reset player scores\n      const playerScoresRef = ref(this.db, `${this.gameId}/player_scores`);\n      await set(playerScoresRef, {});\n\n      // Reset other game state\n      const updates = {\n        [`${this.gameId}/current_round`]: 1,\n        [`${this.gameId}/current_judge_index`]: 0, // Reset judge index to 0\n        [`${this.gameId}/submitted_responses`]: [],\n        [`${this.gameId}/winner`]: null,\n        [`${this.gameId}/winning_response`]: null,\n        [`${this.gameId}/game_winner`]: null, // Clear the game winner\n        // IMPORTANT: Set status to \"Waiting for Prompt\" instead of \"Ready\"\n        // This ensures the Godot client will select a new prompt\n        [`${this.gameId}/status`]: 'Waiting for Prompt'\n      };\n\n      // Clear any response indices, player_response_texts, and player_responses\n      // This ensures we don't have any leftover data from the old approach\n      updates[`${this.gameId}/response_indices`] = null;\n      updates[`${this.gameId}/player_response_texts`] = null;\n      updates[`${this.gameId}/player_responses`] = null;\n      updates[`${this.gameId}/used_responses`] = null;\n\n      await update(ref(this.db), updates);\n\n      // Update the current prompt to \"Incoming Prompt!\"\n      await this.updateCurrentPromptToIncoming();\n      console.log('Current prompt updated to \"Incoming Prompt!\"');\n\n      console.log('Game reset: judge index set to 0, round set to 1, scores reset, waiting for new prompt');\n    } catch (error) {\n      console.error('Error resetting game:', error);\n    }\n  }\n\n  // Leader closes the game\n  async closeGame(): Promise<void> {\n    if (!this.gameId || !this.isLeader) return;\n\n    try {\n      await this.updateGameStatus('Closed');\n    } catch (error) {\n      console.error('Error closing game:', error);\n    }\n  }\n\n  // Save player info to local storage\n  private savePlayerInfo(): void {\n    if (this.gameId && this.playerName) {\n      localStorage.setItem('gameId', this.gameId);\n      localStorage.setItem('playerName', this.playerName);\n      localStorage.setItem('isLeader', String(this.isLeader));\n    }\n  }\n\n  // Load player info from local storage\n  private loadPlayerInfo(): void {\n    console.log('Loading player info from localStorage');\n\n    this.gameId = localStorage.getItem('gameId');\n    this.playerName = localStorage.getItem('playerName');\n    this.isLeader = localStorage.getItem('isLeader') === 'true';\n\n    console.log(`Loaded gameId: ${this.gameId || 'null'}`);\n    console.log(`Loaded playerName: ${this.playerName || 'null'}`);\n    console.log(`Loaded isLeader: ${this.isLeader}`);\n\n    // If we have a gameId but no game state listener, set it up\n    if (this.gameId && this.auth.currentUser) {\n      console.log('Found existing game session, setting up game listener');\n      this.setupGameListener();\n\n      // Manually fetch the initial game state\n      this.verifyGameData().then(() => {\n        console.log('Initial game state verified after loading player info');\n      });\n    }\n  }\n\n  // Clear player info from local storage\n  clearPlayerInfo(): void {\n    localStorage.removeItem('gameId');\n    localStorage.removeItem('playerName');\n    localStorage.removeItem('isLeader');\n    this.gameId = null;\n    this.playerName = null;\n    this.isLeader = false;\n  }\n\n  // Getters\n  get currentGameId(): string | null {\n    return this.gameId;\n  }\n\n  get currentPlayerName(): string | null {\n    return this.playerName;\n  }\n\n  get isGameLeader(): boolean {\n    return this.isLeader;\n  }\n\n  // Verify and fetch the latest game data directly from Firebase\n  async verifyGameData(): Promise<void> {\n    if (!this.gameId) return;\n\n    try {\n      console.log(`===== VERIFYING GAME DATA =====`);\n\n      // Get the latest game data directly from Firebase\n      const gameRef = ref(this.db, `${this.gameId}`);\n      const snapshot = await get(gameRef);\n\n      if (!snapshot.exists()) {\n        console.log(`Game data doesn't exist, aborting verification`);\n        console.log(`===== END VERIFYING GAME DATA =====`);\n        return;\n      }\n\n      // Get the latest game data\n      const gameData = snapshot.val();\n\n      // Log the current prompt data for debugging\n      console.log('Current prompt in Firebase:', gameData.current_prompt);\n\n      // Manually trigger the game state update to ensure our local state is in sync\n      this.gameStateSubject.next(gameData);\n\n      // If no prompt is found and we're in Ready or Waiting for Player Responses state, log a warning\n      if ((!gameData.current_prompt || !gameData.current_prompt.prompt) &&\n          (gameData.status === 'Ready' || gameData.status === 'Waiting for Player Responses')) {\n        console.warn('No prompt found in Firebase during verification, game may need to restart prompt selection');\n\n        // If we're the leader, we could potentially trigger a new prompt selection\n        if (this.isLeader && (gameData.status === 'Ready' || gameData.status === 'Waiting for Player Responses')) {\n          console.log('Leader detected missing prompt, falling back to Waiting for Prompt state');\n          await this.updateGameStatus('Waiting for Prompt');\n        }\n      }\n\n      console.log(`===== END VERIFYING GAME DATA =====`);\n    } catch (error) {\n      console.error('Error verifying game data:', error);\n    }\n  }\n\n  // Method updatePlayerResponses has been removed\n  // Player responses are now managed locally by the web client\n  // This reduces unnecessary database writes\n\n  // Remove player from game\n  async removePlayerFromGame(): Promise<void> {\n    if (!this.gameId || !this.playerName) return;\n\n    try {\n      console.log(`===== REMOVING PLAYER FROM GAME =====`);\n      console.log(`Removing player ${this.playerName} from game ${this.gameId}`);\n\n      // Get current players\n      const playersRef = ref(this.db, `${this.gameId}/players`);\n      const playersSnapshot = await get(playersRef);\n\n      if (!playersSnapshot.exists()) {\n        console.log('No players found, nothing to remove');\n        console.log(`===== END REMOVING PLAYER FROM GAME =====`);\n        return;\n      }\n\n      let players: string[] = playersSnapshot.val() || [];\n      console.log('Current players:', players);\n\n      // Check if player is in the game\n      const playerIndex = players.indexOf(this.playerName);\n      if (playerIndex === -1) {\n        console.log(`Player ${this.playerName} not found in game`);\n        console.log(`===== END REMOVING PLAYER FROM GAME =====`);\n        return;\n      }\n\n      // Remove player from array\n      players.splice(playerIndex, 1);\n      console.log('Updated players array:', players);\n\n      // Update players array in Firebase\n      await set(playersRef, players);\n      console.log(`Player ${this.playerName} removed from game`);\n\n      // Clear player info from local storage\n      this.clearPlayerInfo();\n\n      console.log(`===== END REMOVING PLAYER FROM GAME =====`);\n    } catch (error) {\n      console.error('Error removing player from game:', error);\n      console.log(`===== END REMOVING PLAYER FROM GAME (WITH ERROR) =====`);\n    }\n  }\n}\n\n\n\n"], "mappings": ";;AACA,SAASA,aAAa,QAAQ,cAAc;AAC5C,SACEC,OAAO,EACPC,iBAAiB,EACjBC,kBAAkB,QAGb,eAAe;AACtB,SACEC,WAAW,EACXC,GAAG,EACHC,GAAG,EACHC,GAAG,EACHC,OAAO,EACPC,MAAM,QAED,mBAAmB;AAC1B,SAASC,eAAe,QAAQ,MAAM;AACtC,SAASC,WAAW,QAAQ,gCAAgC;;AAK5D,OAAM,MAAOC,eAAe;EAe1BC,YAAA;IAdQ,KAAAC,GAAG,GAAGd,aAAa,CAACW,WAAW,CAACI,cAAc,CAAC;IAI/C,KAAAC,WAAW,GAAG,IAAIN,eAAe,CAAc,IAAI,CAAC;IACrD,KAAAO,KAAK,GAAG,IAAI,CAACD,WAAW,CAACE,YAAY,EAAE;IAEtC,KAAAC,gBAAgB,GAAG,IAAIT,eAAe,CAAM,IAAI,CAAC;IAClD,KAAAU,UAAU,GAAG,IAAI,CAACD,gBAAgB,CAACD,YAAY,EAAE;IAEhD,KAAAG,MAAM,GAAkB,IAAI;IAC5B,KAAAC,UAAU,GAAkB,IAAI;IAChC,KAAAC,QAAQ,GAAG,KAAK;IAGtB,IAAI,CAACC,IAAI,GAAGvB,OAAO,CAAC,IAAI,CAACa,GAAG,CAAC;IAC7B,IAAI,CAACW,EAAE,GAAGrB,WAAW,CAAC,IAAI,CAACU,GAAG,CAAC;IAE/B;IACAX,kBAAkB,CAAC,IAAI,CAACqB,IAAI,EAAGE,IAAI,IAAI;MACrC,IAAI,CAACV,WAAW,CAACW,IAAI,CAACD,IAAI,CAAC;MAE3B;MACA,IAAI,IAAI,CAACL,MAAM,IAAIK,IAAI,EAAE;QACvB,IAAI,CAACE,iBAAiB,EAAE;MAC1B;IACF,CAAC,CAAC;IAEF;IACA,IAAI,CAACC,cAAc,EAAE;EACvB;EAEA;EACMC,MAAMA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACV,IAAI;QACF,MAAMC,MAAM,SAAS/B,iBAAiB,CAAC6B,KAAI,CAACP,IAAI,CAAC;QACjD,OAAOS,MAAM,CAACP,IAAI;MACpB,CAAC,CAAC,OAAOQ,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;QACzC,MAAMA,KAAK;MACb;IAAC;EACH;EAEA;EACME,QAAQA,CAACC,QAAgB,EAAEf,UAAkB;IAAA,IAAAgB,MAAA;IAAA,OAAAN,iBAAA;MACjD,IAAI;QACFG,OAAO,CAACI,GAAG,CAAC,2CAA2CF,QAAQ,kBAAkBf,UAAU,EAAE,CAAC;QAE9F;QACA,IAAI,CAACgB,MAAI,CAACd,IAAI,CAACgB,WAAW,EAAE;UAC1BL,OAAO,CAACI,GAAG,CAAC,4CAA4C,CAAC;UACzD,MAAMb,IAAI,SAASY,MAAI,CAACR,MAAM,EAAE;UAChCK,OAAO,CAACI,GAAG,CAAC,qCAAqC,EAAEb,IAAI,CAACe,GAAG,CAAC;QAC9D,CAAC,MAAM;UACLN,OAAO,CAACI,GAAG,CAAC,yBAAyB,EAAED,MAAI,CAACd,IAAI,CAACgB,WAAW,CAACC,GAAG,CAAC;QACnE;QAEA;QACAJ,QAAQ,GAAGA,QAAQ,CAACK,WAAW,EAAE;QACjCP,OAAO,CAACI,GAAG,CAAC,oCAAoCF,QAAQ,EAAE,CAAC;QAE3D;QACA,MAAMM,QAAQ,GAAGtC,GAAG,CAACiC,MAAI,CAACb,EAAE,EAAE,GAAG,CAAC;QAClC,MAAMmB,aAAa,SAASrC,GAAG,CAACoC,QAAQ,CAAC;QAEzC,IAAI,CAACC,aAAa,CAACC,MAAM,EAAE,EAAE;UAC3BV,OAAO,CAACD,KAAK,CAAC,4BAA4B,CAAC;UAC3C,OAAO,KAAK;QACd;QAEAC,OAAO,CAACI,GAAG,CAAC,2DAA2D,CAAC;QAExE;QACA,IAAIlB,MAAM,GAAG,IAAI;QAEjBuB,aAAa,CAACE,OAAO,CAAEC,aAAa,IAAI;UACtC,MAAMC,QAAQ,GAAGD,aAAa,CAACE,GAAG,EAAE;UACpCd,OAAO,CAACI,GAAG,CAAC,iBAAiBQ,aAAa,CAACG,GAAG,gBAAgBF,QAAQ,CAACG,SAAS,EAAE,CAAC;UAEnF,IAAIH,QAAQ,CAACG,SAAS,KAAKd,QAAQ,EAAE;YACnChB,MAAM,GAAG0B,aAAa,CAACG,GAAG;YAC1Bf,OAAO,CAACI,GAAG,CAAC,gCAAgClB,MAAM,EAAE,CAAC;YACrD,OAAO,IAAI,CAAC,CAAC;UACf;UACA,OAAO,KAAK;QACd,CAAC,CAAC;QAEF,IAAI,CAACA,MAAM,EAAE;UACXc,OAAO,CAACD,KAAK,CAAC,iCAAiCG,QAAQ,EAAE,CAAC;UAC1D,OAAO,KAAK;QACd;QAEA;QACAC,MAAI,CAACjB,MAAM,GAAGA,MAAM;QACpBiB,MAAI,CAAChB,UAAU,GAAGA,UAAU;QAC5Ba,OAAO,CAACI,GAAG,CAAC,oBAAoBlB,MAAM,qBAAqBC,UAAU,EAAE,CAAC;QACxEgB,MAAI,CAACc,cAAc,EAAE;QAErB;QACAjB,OAAO,CAACI,GAAG,CAAC,iBAAiBjB,UAAU,YAAYD,MAAM,EAAE,CAAC;QAC5D,MAAMiB,MAAI,CAACe,eAAe,CAAC/B,UAAU,CAAC;QAEtC;QACAa,OAAO,CAACI,GAAG,CAAC,gCAAgC,CAAC;QAC7CD,MAAI,CAACV,iBAAiB,EAAE;QAExB;QACAO,OAAO,CAACI,GAAG,CAAC,sCAAsC,CAAC;QACnD,MAAMe,OAAO,GAAGjD,GAAG,CAACiC,MAAI,CAACb,EAAE,EAAE,GAAGJ,MAAM,EAAE,CAAC;QACzC,MAAMkC,YAAY,SAAShD,GAAG,CAAC+C,OAAO,CAAC;QAEvC,IAAIC,YAAY,CAACV,MAAM,EAAE,EAAE;UACzB,MAAMG,QAAQ,GAAGO,YAAY,CAACN,GAAG,EAAE;UACnCd,OAAO,CAACI,GAAG,CAAC,6BAA6B,EAAES,QAAQ,CAAC;UACpDV,MAAI,CAACnB,gBAAgB,CAACQ,IAAI,CAACqB,QAAQ,CAAC;QACtC,CAAC,MAAM;UACLb,OAAO,CAACD,KAAK,CAAC,oCAAoC,CAAC;QACrD;QAEAC,OAAO,CAACI,GAAG,CAAC,0CAA0C,CAAC;QACvD,OAAO,IAAI;MACb,CAAC,CAAC,OAAOL,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3C,OAAO,KAAK;MACd;IAAC;EACH;EAEA;EACcmB,eAAeA,CAAC/B,UAAkB;IAAA,IAAAkC,MAAA;IAAA,OAAAxB,iBAAA;MAC9C,IAAI,CAACwB,MAAI,CAACnC,MAAM,EAAE;QAChBc,OAAO,CAACD,KAAK,CAAC,2CAA2C,CAAC;QAC1D;MACF;MAEA,IAAI;QACFC,OAAO,CAACI,GAAG,CAAC,iBAAiBjB,UAAU,YAAYkC,MAAI,CAACnC,MAAM,EAAE,CAAC;QAEjE;QACA,MAAMoC,UAAU,GAAGpD,GAAG,CAACmD,MAAI,CAAC/B,EAAE,EAAE,GAAG+B,MAAI,CAACnC,MAAM,UAAU,CAAC;QACzD,MAAMqC,eAAe,SAASnD,GAAG,CAACkD,UAAU,CAAC;QAE7C,IAAIE,OAAO,GAAa,EAAE;QAC1B,IAAID,eAAe,CAACb,MAAM,EAAE,EAAE;UAC5Bc,OAAO,GAAGD,eAAe,CAACT,GAAG,EAAE,IAAI,EAAE;UACrCd,OAAO,CAACI,GAAG,CAAC,kBAAkB,EAAEoB,OAAO,CAAC;QAC1C,CAAC,MAAM;UACLxB,OAAO,CAACI,GAAG,CAAC,8CAA8C,CAAC;QAC7D;QAEA;QACA,IAAI,CAACoB,OAAO,CAACC,QAAQ,CAACtC,UAAU,CAAC,EAAE;UACjCa,OAAO,CAACI,GAAG,CAAC,UAAUjB,UAAU,uCAAuC,CAAC;UACxEqC,OAAO,CAACE,IAAI,CAACvC,UAAU,CAAC;UACxB,MAAMhB,GAAG,CAACmD,UAAU,EAAEE,OAAO,CAAC;UAC9BxB,OAAO,CAACI,GAAG,CAAC,UAAUjB,UAAU,gBAAgB,CAAC;QACnD,CAAC,MAAM;UACLa,OAAO,CAACI,GAAG,CAAC,UAAUjB,UAAU,kBAAkB,CAAC;QACrD;QAEA;QACAkC,MAAI,CAACjC,QAAQ,GAAGoC,OAAO,CAACG,MAAM,KAAK,CAAC,IAAIH,OAAO,CAAC,CAAC,CAAC,KAAKrC,UAAU;QACjEa,OAAO,CAACI,GAAG,CAAC,UAAUjB,UAAU,OAAOkC,MAAI,CAACjC,QAAQ,GAAG,YAAY,GAAG,gBAAgB,EAAE,CAAC;QAEzF;QACA,MAAMwC,cAAc,GAAG1D,GAAG,CAACmD,MAAI,CAAC/B,EAAE,EAAE,GAAG+B,MAAI,CAACnC,MAAM,kBAAkBC,UAAU,EAAE,CAAC;QACjF,MAAM0C,aAAa,SAASzD,GAAG,CAACwD,cAAc,CAAC;QAE/C,IAAI,CAACC,aAAa,CAACnB,MAAM,EAAE,EAAE;UAC3BV,OAAO,CAACI,GAAG,CAAC,iCAAiCjB,UAAU,EAAE,CAAC;UAC1D,MAAMhB,GAAG,CAACyD,cAAc,EAAE,CAAC,CAAC;QAC9B;MAEF,CAAC,CAAC,OAAO7B,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACtD;IAAC;EACH;EAEA;EACQN,iBAAiBA,CAAA;IACvB,IAAI,CAAC,IAAI,CAACP,MAAM,EAAE;MAChBc,OAAO,CAACD,KAAK,CAAC,4CAA4C,CAAC;MAC3D;IACF;IAEAC,OAAO,CAACI,GAAG,CAAC,wCAAwC,IAAI,CAAClB,MAAM,EAAE,CAAC;IAElE,MAAMiC,OAAO,GAAGjD,GAAG,CAAC,IAAI,CAACoB,EAAE,EAAE,GAAG,IAAI,CAACJ,MAAM,EAAE,CAAC;IAC9Cb,OAAO,CAAC8C,OAAO,EAAGW,QAAQ,IAAI;MAC5B9B,OAAO,CAACI,GAAG,CAAC,mCAAmC,EAAE0B,QAAQ,CAACpB,MAAM,EAAE,GAAG,aAAa,GAAG,SAAS,CAAC;MAE/F,MAAMG,QAAQ,GAAGiB,QAAQ,CAAChB,GAAG,EAAE;MAC/B,IAAID,QAAQ,EAAE;QACZb,OAAO,CAACI,GAAG,CAAC,cAAc,EAAES,QAAQ,CAACkB,MAAM,CAAC;QAC5C/B,OAAO,CAACI,GAAG,CAAC,YAAY,EAAES,QAAQ,CAAC;QACnC,IAAI,CAAC7B,gBAAgB,CAACQ,IAAI,CAACqB,QAAQ,CAAC;MACtC,CAAC,MAAM;QACLb,OAAO,CAACD,KAAK,CAAC,gCAAgC,CAAC;MACjD;IACF,CAAC,EAAGA,KAAK,IAAI;MACXC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD,CAAC,CAAC;IAEFC,OAAO,CAACI,GAAG,CAAC,8BAA8B,CAAC;EAC7C;EAEA;EACM4B,gBAAgBA,CAACD,MAAc;IAAA,IAAAE,MAAA;IAAA,OAAApC,iBAAA;MACnC,IAAI,CAACoC,MAAI,CAAC/C,MAAM,EAAE;MAElB,IAAI;QACFc,OAAO,CAACI,GAAG,CAAC,kCAAkC2B,MAAM,QAAQ,CAAC;QAE7D;QACA,MAAMZ,OAAO,GAAGjD,GAAG,CAAC+D,MAAI,CAAC3C,EAAE,EAAE,GAAG2C,MAAI,CAAC/C,MAAM,EAAE,CAAC;QAC9C,MAAMkC,YAAY,SAAShD,GAAG,CAAC+C,OAAO,CAAC;QAEvC,IAAIC,YAAY,CAACV,MAAM,EAAE,EAAE;UACzB,MAAMG,QAAQ,GAAGO,YAAY,CAACN,GAAG,EAAE;UACnCd,OAAO,CAACI,GAAG,CAAC,0CAA0C,EAAE;YACtD8B,aAAa,EAAErB,QAAQ,CAACkB,MAAM;YAC9BI,iBAAiB,EAAEtB,QAAQ,CAACuB,mBAAmB,IAAI,CAAC;YACpDZ,OAAO,EAAEX,QAAQ,CAACW,OAAO,IAAI,EAAE;YAC/Ba,YAAY,EAAExB,QAAQ,CAACyB,aAAa,IAAI;WACzC,CAAC;QACJ;QAEA;QACA,MAAMC,SAAS,GAAGrE,GAAG,CAAC+D,MAAI,CAAC3C,EAAE,EAAE,GAAG2C,MAAI,CAAC/C,MAAM,SAAS,CAAC;QACvD,MAAMf,GAAG,CAACoE,SAAS,EAAER,MAAM,CAAC;QAC5B/B,OAAO,CAACI,GAAG,CAAC,2BAA2B2B,MAAM,EAAE,CAAC;QAEhD/B,OAAO,CAACI,GAAG,CAAC,sCAAsC,CAAC;MACrD,CAAC,CAAC,OAAOL,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnDC,OAAO,CAACI,GAAG,CAAC,mDAAmD,CAAC;MAClE;IAAC;EACH;EAEA;EACA;EACMoC,YAAYA,CAACC,OAAe;IAAA,OAAA5C,iBAAA;MAChCG,OAAO,CAAC0C,IAAI,CAAC,qFAAqF,CAAC;MACnG;IAAO;EACT;EAEA;EACA;EACMC,6BAA6BA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAA/C,iBAAA;MACjC,IAAI,CAAC+C,MAAI,CAAC1D,MAAM,EAAE;MAElB,IAAI;QACFc,OAAO,CAACI,GAAG,CAAC,2DAA2D,CAAC;QAExE;QACA,MAAMyC,SAAS,GAAG3E,GAAG,CAAC0E,MAAI,CAACtD,EAAE,EAAE,GAAGsD,MAAI,CAAC1D,MAAM,iBAAiB,CAAC;QAC/D,MAAMf,GAAG,CAAC0E,SAAS,EAAE;UAAEC,MAAM,EAAE;QAAkB,CAAE,CAAC;QAEpD9C,OAAO,CAACI,GAAG,CAAC,8CAA8C,CAAC;QAC3DJ,OAAO,CAACI,GAAG,CAAC,yCAAyC,CAAC;MACxD,CAAC,CAAC,OAAOL,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtDC,OAAO,CAACI,GAAG,CAAC,sDAAsD,CAAC;MACrE;IAAC;EACH;EAEA;EACM2C,cAAcA,CAACC,YAAoB,EAAEC,aAAqB;IAAA,IAAAC,MAAA;IAAA,OAAArD,iBAAA;MAC9D,IAAI,CAACqD,MAAI,CAAChE,MAAM,IAAI,CAACgE,MAAI,CAAC/D,UAAU,EAAE;MAEtC,IAAI;QACFa,OAAO,CAACI,GAAG,CAAC,mDAAmD,CAAC;QAChEJ,OAAO,CAACI,GAAG,CAAC,UAAU8C,MAAI,CAAC/D,UAAU,iCAAiC8D,aAAa,MAAMD,YAAY,GAAG,CAAC;QAEzG;QACA,MAAMG,YAAY,GAAGjF,GAAG,CAACgF,MAAI,CAAC5D,EAAE,EAAE,GAAG4D,MAAI,CAAChE,MAAM,sBAAsB,CAAC;QACvE,MAAMkE,iBAAiB,SAAShF,GAAG,CAAC+E,YAAY,CAAC;QAEjD,IAAIE,SAAS,GAAU,EAAE;QACzB,IAAID,iBAAiB,CAAC1C,MAAM,EAAE,EAAE;UAC9B2C,SAAS,GAAGD,iBAAiB,CAACtC,GAAG,EAAE,IAAI,EAAE;QAC3C;QAEA;QACA,MAAMwC,qBAAqB,GAAGD,SAAS,CAACE,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACC,WAAW,KAAKP,MAAI,CAAC/D,UAAU,CAAC;QAEzF,IAAImE,qBAAqB,IAAI,CAAC,EAAE;UAC9B;UACAtD,OAAO,CAACI,GAAG,CAAC,UAAU8C,MAAI,CAAC/D,UAAU,6CAA6C,CAAC;UACnFkE,SAAS,CAACC,qBAAqB,CAAC,GAAG;YACjCI,IAAI,EAAEV,YAAY;YAClBS,WAAW,EAAEP,MAAI,CAAC/D;WACnB;QACH,CAAC,MAAM;UACL;UACAa,OAAO,CAACI,GAAG,CAAC,kCAAkC8C,MAAI,CAAC/D,UAAU,EAAE,CAAC;UAChEkE,SAAS,CAAC3B,IAAI,CAAC;YACbgC,IAAI,EAAEV,YAAY;YAClBS,WAAW,EAAEP,MAAI,CAAC/D;WACnB,CAAC;QACJ;QAEA,MAAMhB,GAAG,CAACgF,YAAY,EAAEE,SAAS,CAAC;QAClCrD,OAAO,CAACI,GAAG,CAAC,8BAA8B,EAAEiD,SAAS,CAAC;QAEtD;QACA;QAEArD,OAAO,CAACI,GAAG,CAAC,uDAAuD,CAAC;QAEpE;QACA;QACAuD,UAAU,CAAC,MAAK;UACd3D,OAAO,CAACI,GAAG,CAAC,sEAAsE,CAAC;UACnF8C,MAAI,CAACU,0BAA0B,EAAE;QACnC,CAAC,EAAE,GAAG,CAAC;MACT,CAAC,CAAC,OAAO7D,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClD,MAAMA,KAAK;MACb;IAAC;EACH;EAEA;EACc6D,0BAA0BA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAhE,iBAAA;MACtC,IAAI,CAACgE,MAAI,CAAC3E,MAAM,EAAE;MAElB,IAAI;QACFc,OAAO,CAACI,GAAG,CAAC,8CAA8C,CAAC;QAE3D;QACA,MAAMe,OAAO,GAAGjD,GAAG,CAAC2F,MAAI,CAACvE,EAAE,EAAE,GAAGuE,MAAI,CAAC3E,MAAM,EAAE,CAAC;QAC9C,MAAMkC,YAAY,SAAShD,GAAG,CAAC+C,OAAO,CAAC;QAEvC,IAAI,CAACC,YAAY,CAACV,MAAM,EAAE,EAAE;UAC1BV,OAAO,CAACI,GAAG,CAAC,yCAAyC,CAAC;UACtDJ,OAAO,CAACI,GAAG,CAAC,kDAAkD,CAAC;UAC/D;QACF;QAEA,MAAMS,QAAQ,GAAGO,YAAY,CAACN,GAAG,EAAE;QAEnC;QACA,IAAID,QAAQ,CAACkB,MAAM,KAAK,8BAA8B,EAAE;UACtD/B,OAAO,CAACI,GAAG,CAAC,kBAAkBS,QAAQ,CAACkB,MAAM,0BAA0B,CAAC;UACxE/B,OAAO,CAACI,GAAG,CAAC,kDAAkD,CAAC;UAC/D;QACF;QAEA,MAAMoB,OAAO,GAAGX,QAAQ,CAACW,OAAO,IAAI,EAAE;QACtC,MAAM6B,SAAS,GAAGxC,QAAQ,CAACiD,mBAAmB,IAAI,EAAE;QAEpD;QACA,MAAMC,UAAU,GAAGlD,QAAQ,CAACuB,mBAAmB,IAAI,CAAC;QACpD,MAAM4B,KAAK,GAAGxC,OAAO,CAACuC,UAAU,CAAC;QAEjC;QACA,MAAME,eAAe,GAAGzC,OAAO,CAAC0C,MAAM,CAAEC,CAAS,IAAKA,CAAC,KAAKH,KAAK,CAAC;QAElEhE,OAAO,CAACI,GAAG,CAAC,iBAAiB,EAAE;UAC7BgE,YAAY,EAAE5C,OAAO,CAACG,MAAM;UAC5BsC,eAAe,EAAEA,eAAe,CAACtC,MAAM;UACvC0C,kBAAkB,EAAEhB,SAAS,CAAC1B,MAAM;UACpCqC,KAAK,EAAEA;SACR,CAAC;QAEF;QACA,IAAIC,eAAe,CAACtC,MAAM,KAAK,CAAC,EAAE;UAChC3B,OAAO,CAACI,GAAG,CAAC,2DAA2D,CAAC;UACxE,MAAMyD,MAAI,CAAC7B,gBAAgB,CAAC,mBAAmB,CAAC;UAChDhC,OAAO,CAACI,GAAG,CAAC,kDAAkD,CAAC;UAC/D;QACF;QAEA;QACA;QACA,IAAIiD,SAAS,CAAC1B,MAAM,IAAIsC,eAAe,CAACtC,MAAM,EAAE;UAC9C3B,OAAO,CAACI,GAAG,CAAC,sFAAsF,CAAC;UACnG,MAAMyD,MAAI,CAAC7B,gBAAgB,CAAC,mBAAmB,CAAC;UAChDhC,OAAO,CAACI,GAAG,CAAC,kDAAkD,CAAC;UAC/D;QACF;QAEA;QACA;QACA,MAAMkE,mBAAmB,GAA8B,EAAE;QAEzD;QACAL,eAAe,CAACtD,OAAO,CAAE4D,MAAc,IAAI;UACzCD,mBAAmB,CAACC,MAAM,CAAC,GAAG,CAAC;QACjC,CAAC,CAAC;QAEF;QACAlB,SAAS,CAAC1C,OAAO,CAAE6D,QAA+C,IAAI;UACpE,IAAIF,mBAAmB,CAACE,QAAQ,CAACf,WAAW,CAAC,KAAKgB,SAAS,EAAE;YAC3DH,mBAAmB,CAACE,QAAQ,CAACf,WAAW,CAAC,EAAE;UAC7C;QACF,CAAC,CAAC;QAEF;QACA,MAAMiB,kCAAkC,GAAGC,MAAM,CAACC,MAAM,CAACN,mBAAmB,CAAC,CAACO,KAAK,CAACC,KAAK,IAAIA,KAAK,GAAG,CAAC,CAAC;QAEvG9E,OAAO,CAACI,GAAG,CAAC,yBAAyB,EAAEkE,mBAAmB,CAAC;QAC3DtE,OAAO,CAACI,GAAG,CAAC,2CAA2C,EAAEsE,kCAAkC,CAAC;QAE5F;QACA,IAAIA,kCAAkC,EAAE;UACtC1E,OAAO,CAACI,GAAG,CAAC,wFAAwF,CAAC;UACrG,MAAMyD,MAAI,CAAC7B,gBAAgB,CAAC,mBAAmB,CAAC;QAClD,CAAC,MAAM;UACLhC,OAAO,CAACI,GAAG,CAAC,8CAA8C,CAAC;QAC7D;QAEAJ,OAAO,CAACI,GAAG,CAAC,kDAAkD,CAAC;MACjE,CAAC,CAAC,OAAOL,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjDC,OAAO,CAACI,GAAG,CAAC,+DAA+D,CAAC;MAC9E;IAAC;EACH;EAEA;EACM2E,YAAYA,CAAC9B,aAAqB;IAAA,IAAA+B,MAAA;IAAA,OAAAnF,iBAAA;MACtC,IAAI,CAACmF,MAAI,CAAC9F,MAAM,EAAE;MAElB,IAAI;QACFc,OAAO,CAACI,GAAG,CAAC,iDAAiD6C,aAAa,QAAQ,CAAC;QAEnF;QACA,MAAME,YAAY,GAAGjF,GAAG,CAAC8G,MAAI,CAAC1F,EAAE,EAAE,GAAG0F,MAAI,CAAC9F,MAAM,wBAAwB+D,aAAa,EAAE,CAAC;QACxF,MAAMgC,gBAAgB,SAAS7G,GAAG,CAAC+E,YAAY,CAAC;QAEhD,IAAI,CAAC8B,gBAAgB,CAACvE,MAAM,EAAE,EAAE;UAC9BV,OAAO,CAACI,GAAG,CAAC,qBAAqB6C,aAAa,wCAAwC,CAAC;UACvFjD,OAAO,CAACI,GAAG,CAAC,yCAAyC,CAAC;UACtD;QACF;QAEA,MAAMoE,QAAQ,GAAGS,gBAAgB,CAACnE,GAAG,EAAE;QACvCd,OAAO,CAACI,GAAG,CAAC,4BAA4B,EAAEoE,QAAQ,CAAC;QAEnD;QACA,MAAM5C,cAAc,GAAG1D,GAAG,CAAC8G,MAAI,CAAC1F,EAAE,EAAE,GAAG0F,MAAI,CAAC9F,MAAM,kBAAkBsF,QAAQ,CAACf,WAAW,EAAE,CAAC;QAC3F,MAAM5B,aAAa,SAASzD,GAAG,CAACwD,cAAc,CAAC;QAE/C,IAAIsD,YAAY,GAAG,CAAC;QACpB,IAAIrD,aAAa,CAACnB,MAAM,EAAE,EAAE;UAC1BwE,YAAY,GAAGrD,aAAa,CAACf,GAAG,EAAE,IAAI,CAAC;QACzC;QAEA;QACA,MAAMqE,QAAQ,GAAGD,YAAY,GAAG,CAAC;QACjClF,OAAO,CAACI,GAAG,CAAC,sBAAsBoE,QAAQ,CAACf,WAAW,SAASyB,YAAY,OAAOC,QAAQ,EAAE,CAAC;QAE7F;QACA,MAAMC,QAAQ,GAAGD,QAAQ,IAAI,CAAC;QAC9BnF,OAAO,CAACI,GAAG,CAAC,oBAAoBgF,QAAQ,KAAKD,QAAQ,QAAQ,CAAC;QAE9D;QACA,MAAME,OAAO,GAAG;UACd,CAAC,GAAGL,MAAI,CAAC9F,MAAM,SAAS,GAAGsF,QAAQ,CAACf,WAAW;UAC/C,CAAC,GAAGuB,MAAI,CAAC9F,MAAM,mBAAmB,GAAGsF,QAAQ,CAACd,IAAI;UAClD,CAAC,GAAGsB,MAAI,CAAC9F,MAAM,kBAAkBsF,QAAQ,CAACf,WAAW,EAAE,GAAG0B,QAAQ;UAClE,CAAC,GAAGH,MAAI,CAAC9F,MAAM,SAAS,GAAGkG,QAAQ,GAAG,WAAW,GAAG;SACrD;QAED;QACA,IAAIA,QAAQ,EAAE;UACZC,OAAO,CAAC,GAAGL,MAAI,CAAC9F,MAAM,cAAc,CAAC,GAAGsF,QAAQ,CAACf,WAAW;UAC5DzD,OAAO,CAACI,GAAG,CAAC,cAAcoE,QAAQ,CAACf,WAAW,iBAAiB0B,QAAQ,UAAU,CAAC;QACpF;QAEAnF,OAAO,CAACI,GAAG,CAAC,qCAAqC,EAAEiF,OAAO,CAAC;QAC3D,MAAM/G,MAAM,CAACJ,GAAG,CAAC8G,MAAI,CAAC1F,EAAE,CAAC,EAAE+F,OAAO,CAAC;QACnCrF,OAAO,CAACI,GAAG,CAAC,2BAA2B,CAAC;QAExC;QACA;QAEAJ,OAAO,CAACI,GAAG,CAAC,+BAA+B,CAAC;MAC9C,CAAC,CAAC,OAAOL,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/CC,OAAO,CAACI,GAAG,CAAC,4CAA4C,CAAC;MAC3D;IAAC;EACH;EAEA;EACMkF,WAAWA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAA1F,iBAAA;MACf,IAAI,CAAC0F,MAAI,CAACrG,MAAM,EAAE;MAElB,IAAI;QACFc,OAAO,CAACI,GAAG,CAAC,iCAAiC,CAAC;QAE9C;QACA,MAAMiF,OAAO,GAAG;UACd,CAAC,GAAGE,MAAI,CAACrG,MAAM,SAAS,GAAG,WAAW;UACtC,CAAC,GAAGqG,MAAI,CAACrG,MAAM,mBAAmB,GAAG,qBAAqB;UAC1D,CAAC,GAAGqG,MAAI,CAACrG,MAAM,SAAS,GAAG;SAC5B;QAEDc,OAAO,CAACI,GAAG,CAAC,mCAAmC,EAAEiF,OAAO,CAAC;QACzD,MAAM/G,MAAM,CAACJ,GAAG,CAACqH,MAAI,CAACjG,EAAE,CAAC,EAAE+F,OAAO,CAAC;QACnCrF,OAAO,CAACI,GAAG,CAAC,2BAA2B,CAAC;QAExCJ,OAAO,CAACI,GAAG,CAAC,qCAAqC,CAAC;QAElD;QACA;QAEAJ,OAAO,CAACI,GAAG,CAAC,8BAA8B,CAAC;MAC7C,CAAC,CAAC,OAAOL,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/CC,OAAO,CAACI,GAAG,CAAC,2CAA2C,CAAC;MAC1D;IAAC;EACH;EAEA;EACA;EACcoF,6BAA6BA,CAAA;IAAA,IAAAC,OAAA;IAAA,OAAA5F,iBAAA;MACzC,IAAI,CAAC4F,OAAI,CAACvG,MAAM,EAAE;MAElB,IAAI;QACFc,OAAO,CAACI,GAAG,CAAC,mDAAmD,CAAC;QAEhE;QACA,MAAMe,OAAO,GAAGjD,GAAG,CAACuH,OAAI,CAACnG,EAAE,EAAE,GAAGmG,OAAI,CAACvG,MAAM,EAAE,CAAC;QAC9C,MAAMkC,YAAY,SAAShD,GAAG,CAAC+C,OAAO,CAAC;QAEvC,IAAI,CAACC,YAAY,CAACV,MAAM,EAAE,EAAE;UAC1BV,OAAO,CAACI,GAAG,CAAC,0CAA0C,CAAC;UACvDJ,OAAO,CAACI,GAAG,CAAC,gDAAgD,CAAC;UAC7D;QACF;QAEA,MAAMS,QAAQ,GAAGO,YAAY,CAACN,GAAG,EAAE;QACnCd,OAAO,CAACI,GAAG,CAAC,wBAAwBS,QAAQ,CAACkB,MAAM,EAAE,CAAC;QAEtD;QACA;QACA,IAAIlB,QAAQ,CAACkB,MAAM,KAAK,eAAe,EAAE;UACvC/B,OAAO,CAACI,GAAG,CAAC,0DAA0D,CAAC;UAEvE;UACA,MAAMqF,OAAI,CAAC9C,6BAA6B,EAAE;UAC1C3C,OAAO,CAACI,GAAG,CAAC,uEAAuE,CAAC;UAEpF,MAAMqF,OAAI,CAACC,YAAY,EAAE;QAC3B,CAAC,MAAM;UACL1F,OAAO,CAACI,GAAG,CAAC,cAAcS,QAAQ,CAACkB,MAAM,gCAAgC,CAAC;QAC5E;QAEA/B,OAAO,CAACI,GAAG,CAAC,sCAAsC,CAAC;MACrD,CAAC,CAAC,OAAOL,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnDC,OAAO,CAACI,GAAG,CAAC,mDAAmD,CAAC;MAClE;IAAC;EACH;EAEA;EACMsF,YAAYA,CAAA;IAAA,IAAAC,OAAA;IAAA,OAAA9F,iBAAA;MAChB,IAAI,CAAC8F,OAAI,CAACzG,MAAM,IAAI,CAACyG,OAAI,CAACvG,QAAQ,EAAE;MAEpC,IAAI;QACFY,OAAO,CAACI,GAAG,CAAC,mCAAmC,CAAC;QAEhD;QACA,MAAMe,OAAO,GAAGjD,GAAG,CAACyH,OAAI,CAACrG,EAAE,EAAE,GAAGqG,OAAI,CAACzG,MAAM,EAAE,CAAC;QAC9C,MAAMkC,YAAY,SAAShD,GAAG,CAAC+C,OAAO,CAAC;QAEvC,IAAI,CAACC,YAAY,CAACV,MAAM,EAAE,EAAE;UAC1BV,OAAO,CAACI,GAAG,CAAC,iDAAiD,CAAC;UAC9DJ,OAAO,CAACI,GAAG,CAAC,0CAA0C,CAAC;UACvD;QACF;QAEA,MAAMS,QAAQ,GAAGO,YAAY,CAACN,GAAG,EAAE;QAEnC;QACA,MAAMqB,iBAAiB,GAAGtB,QAAQ,CAACuB,mBAAmB,IAAI,CAAC;QAC3D,MAAMZ,OAAO,GAAGX,QAAQ,CAACW,OAAO,IAAI,EAAE;QAEtC;QACAxB,OAAO,CAACI,GAAG,CAAC,qBAAqB,EAAE;UACjC+B,iBAAiB;UACjBX,OAAO;UACPa,YAAY,EAAExB,QAAQ,CAACyB,aAAa,IAAI,CAAC;UACzCP,MAAM,EAAElB,QAAQ,CAACkB;SAClB,CAAC;QAEF;QACA,IAAI,CAACP,OAAO,IAAIA,OAAO,CAACG,MAAM,GAAG,CAAC,EAAE;UAClC3B,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEyB,OAAO,CAAC;UAChDxB,OAAO,CAACI,GAAG,CAAC,kDAAkD,CAAC;UAC/D;QACF;QAEA;QACA,IAAIwF,cAAc,GAAGzD,iBAAiB,GAAG,CAAC;QAC1C,IAAIyD,cAAc,IAAIpE,OAAO,CAACG,MAAM,EAAE;UACpCiE,cAAc,GAAG,CAAC;QACpB;QAEA5F,OAAO,CAACI,GAAG,CAAC,uBAAuBoB,OAAO,CAACW,iBAAiB,CAAC,WAAWA,iBAAiB,QAAQX,OAAO,CAACoE,cAAc,CAAC,WAAWA,cAAc,GAAG,CAAC;QACrJ5F,OAAO,CAACI,GAAG,CAAC,uBAAuByF,IAAI,CAACC,SAAS,CAACtE,OAAO,CAAC,EAAE,CAAC;QAE7D;QACA,MAAM6D,OAAO,GAAG;UACd,CAAC,GAAGM,OAAI,CAACzG,MAAM,sBAAsB,GAAG0G,cAAc;UACtD,CAAC,GAAGD,OAAI,CAACzG,MAAM,sBAAsB,GAAG,EAAE;UAC1C,CAAC,GAAGyG,OAAI,CAACzG,MAAM,gBAAgB,GAAG,CAAC2B,QAAQ,CAACyB,aAAa,IAAI,CAAC,IAAI;SACnE;QAED;QACA;QACA+C,OAAO,CAAC,GAAGM,OAAI,CAACzG,MAAM,mBAAmB,CAAC,GAAG,IAAI;QACjDmG,OAAO,CAAC,GAAGM,OAAI,CAACzG,MAAM,wBAAwB,CAAC,GAAG,IAAI;QACtDmG,OAAO,CAAC,GAAGM,OAAI,CAACzG,MAAM,mBAAmB,CAAC,GAAG,IAAI;QACjDmG,OAAO,CAAC,GAAGM,OAAI,CAACzG,MAAM,iBAAiB,CAAC,GAAG,IAAI;QAE/C,MAAMZ,MAAM,CAACJ,GAAG,CAACyH,OAAI,CAACrG,EAAE,CAAC,EAAE+F,OAAO,CAAC;QAEnCrF,OAAO,CAACI,GAAG,CAAC,+DAA+D,CAAC;QAE5E;QACA,MAAMuF,OAAI,CAAChD,6BAA6B,EAAE;QAC1C3C,OAAO,CAACI,GAAG,CAAC,0EAA0E,CAAC;QAEvF;QACA,MAAMuF,OAAI,CAAC3D,gBAAgB,CAAC,oBAAoB,CAAC;QAEjDhC,OAAO,CAACI,GAAG,CAAC,gCAAgC,CAAC;MAC/C,CAAC,CAAC,OAAOL,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChDC,OAAO,CAACI,GAAG,CAAC,6CAA6C,CAAC;MAC5D;IAAC;EACH;EAEA;EACM2F,SAASA,CAAA;IAAA,IAAAC,OAAA;IAAA,OAAAnG,iBAAA;MACb,IAAI,CAACmG,OAAI,CAAC9G,MAAM,IAAI,CAAC8G,OAAI,CAAC5G,QAAQ,EAAE;MAEpC,IAAI;QACF;QACA,MAAM6G,eAAe,GAAG/H,GAAG,CAAC8H,OAAI,CAAC1G,EAAE,EAAE,GAAG0G,OAAI,CAAC9G,MAAM,gBAAgB,CAAC;QACpE,MAAMf,GAAG,CAAC8H,eAAe,EAAE,EAAE,CAAC;QAE9B;QACA,MAAMZ,OAAO,GAAG;UACd,CAAC,GAAGW,OAAI,CAAC9G,MAAM,gBAAgB,GAAG,CAAC;UACnC,CAAC,GAAG8G,OAAI,CAAC9G,MAAM,sBAAsB,GAAG,CAAC;UAAE;UAC3C,CAAC,GAAG8G,OAAI,CAAC9G,MAAM,sBAAsB,GAAG,EAAE;UAC1C,CAAC,GAAG8G,OAAI,CAAC9G,MAAM,SAAS,GAAG,IAAI;UAC/B,CAAC,GAAG8G,OAAI,CAAC9G,MAAM,mBAAmB,GAAG,IAAI;UACzC,CAAC,GAAG8G,OAAI,CAAC9G,MAAM,cAAc,GAAG,IAAI;UAAE;UACtC;UACA;UACA,CAAC,GAAG8G,OAAI,CAAC9G,MAAM,SAAS,GAAG;SAC5B;QAED;QACA;QACAmG,OAAO,CAAC,GAAGW,OAAI,CAAC9G,MAAM,mBAAmB,CAAC,GAAG,IAAI;QACjDmG,OAAO,CAAC,GAAGW,OAAI,CAAC9G,MAAM,wBAAwB,CAAC,GAAG,IAAI;QACtDmG,OAAO,CAAC,GAAGW,OAAI,CAAC9G,MAAM,mBAAmB,CAAC,GAAG,IAAI;QACjDmG,OAAO,CAAC,GAAGW,OAAI,CAAC9G,MAAM,iBAAiB,CAAC,GAAG,IAAI;QAE/C,MAAMZ,MAAM,CAACJ,GAAG,CAAC8H,OAAI,CAAC1G,EAAE,CAAC,EAAE+F,OAAO,CAAC;QAEnC;QACA,MAAMW,OAAI,CAACrD,6BAA6B,EAAE;QAC1C3C,OAAO,CAACI,GAAG,CAAC,8CAA8C,CAAC;QAE3DJ,OAAO,CAACI,GAAG,CAAC,wFAAwF,CAAC;MACvG,CAAC,CAAC,OAAOL,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC/C;IAAC;EACH;EAEA;EACMmG,SAASA,CAAA;IAAA,IAAAC,OAAA;IAAA,OAAAtG,iBAAA;MACb,IAAI,CAACsG,OAAI,CAACjH,MAAM,IAAI,CAACiH,OAAI,CAAC/G,QAAQ,EAAE;MAEpC,IAAI;QACF,MAAM+G,OAAI,CAACnE,gBAAgB,CAAC,QAAQ,CAAC;MACvC,CAAC,CAAC,OAAOjC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC7C;IAAC;EACH;EAEA;EACQkB,cAAcA,CAAA;IACpB,IAAI,IAAI,CAAC/B,MAAM,IAAI,IAAI,CAACC,UAAU,EAAE;MAClCiH,YAAY,CAACC,OAAO,CAAC,QAAQ,EAAE,IAAI,CAACnH,MAAM,CAAC;MAC3CkH,YAAY,CAACC,OAAO,CAAC,YAAY,EAAE,IAAI,CAAClH,UAAU,CAAC;MACnDiH,YAAY,CAACC,OAAO,CAAC,UAAU,EAAEC,MAAM,CAAC,IAAI,CAAClH,QAAQ,CAAC,CAAC;IACzD;EACF;EAEA;EACQM,cAAcA,CAAA;IACpBM,OAAO,CAACI,GAAG,CAAC,uCAAuC,CAAC;IAEpD,IAAI,CAAClB,MAAM,GAAGkH,YAAY,CAACG,OAAO,CAAC,QAAQ,CAAC;IAC5C,IAAI,CAACpH,UAAU,GAAGiH,YAAY,CAACG,OAAO,CAAC,YAAY,CAAC;IACpD,IAAI,CAACnH,QAAQ,GAAGgH,YAAY,CAACG,OAAO,CAAC,UAAU,CAAC,KAAK,MAAM;IAE3DvG,OAAO,CAACI,GAAG,CAAC,kBAAkB,IAAI,CAAClB,MAAM,IAAI,MAAM,EAAE,CAAC;IACtDc,OAAO,CAACI,GAAG,CAAC,sBAAsB,IAAI,CAACjB,UAAU,IAAI,MAAM,EAAE,CAAC;IAC9Da,OAAO,CAACI,GAAG,CAAC,oBAAoB,IAAI,CAAChB,QAAQ,EAAE,CAAC;IAEhD;IACA,IAAI,IAAI,CAACF,MAAM,IAAI,IAAI,CAACG,IAAI,CAACgB,WAAW,EAAE;MACxCL,OAAO,CAACI,GAAG,CAAC,uDAAuD,CAAC;MACpE,IAAI,CAACX,iBAAiB,EAAE;MAExB;MACA,IAAI,CAAC+G,cAAc,EAAE,CAACC,IAAI,CAAC,MAAK;QAC9BzG,OAAO,CAACI,GAAG,CAAC,uDAAuD,CAAC;MACtE,CAAC,CAAC;IACJ;EACF;EAEA;EACAsG,eAAeA,CAAA;IACbN,YAAY,CAACO,UAAU,CAAC,QAAQ,CAAC;IACjCP,YAAY,CAACO,UAAU,CAAC,YAAY,CAAC;IACrCP,YAAY,CAACO,UAAU,CAAC,UAAU,CAAC;IACnC,IAAI,CAACzH,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,QAAQ,GAAG,KAAK;EACvB;EAEA;EACA,IAAIwH,aAAaA,CAAA;IACf,OAAO,IAAI,CAAC1H,MAAM;EACpB;EAEA,IAAI2H,iBAAiBA,CAAA;IACnB,OAAO,IAAI,CAAC1H,UAAU;EACxB;EAEA,IAAI2H,YAAYA,CAAA;IACd,OAAO,IAAI,CAAC1H,QAAQ;EACtB;EAEA;EACMoH,cAAcA,CAAA;IAAA,IAAAO,OAAA;IAAA,OAAAlH,iBAAA;MAClB,IAAI,CAACkH,OAAI,CAAC7H,MAAM,EAAE;MAElB,IAAI;QACFc,OAAO,CAACI,GAAG,CAAC,iCAAiC,CAAC;QAE9C;QACA,MAAMe,OAAO,GAAGjD,GAAG,CAAC6I,OAAI,CAACzH,EAAE,EAAE,GAAGyH,OAAI,CAAC7H,MAAM,EAAE,CAAC;QAC9C,MAAM4C,QAAQ,SAAS1D,GAAG,CAAC+C,OAAO,CAAC;QAEnC,IAAI,CAACW,QAAQ,CAACpB,MAAM,EAAE,EAAE;UACtBV,OAAO,CAACI,GAAG,CAAC,gDAAgD,CAAC;UAC7DJ,OAAO,CAACI,GAAG,CAAC,qCAAqC,CAAC;UAClD;QACF;QAEA;QACA,MAAMS,QAAQ,GAAGiB,QAAQ,CAAChB,GAAG,EAAE;QAE/B;QACAd,OAAO,CAACI,GAAG,CAAC,6BAA6B,EAAES,QAAQ,CAACmG,cAAc,CAAC;QAEnE;QACAD,OAAI,CAAC/H,gBAAgB,CAACQ,IAAI,CAACqB,QAAQ,CAAC;QAEpC;QACA,IAAI,CAAC,CAACA,QAAQ,CAACmG,cAAc,IAAI,CAACnG,QAAQ,CAACmG,cAAc,CAAClE,MAAM,MAC3DjC,QAAQ,CAACkB,MAAM,KAAK,OAAO,IAAIlB,QAAQ,CAACkB,MAAM,KAAK,8BAA8B,CAAC,EAAE;UACvF/B,OAAO,CAAC0C,IAAI,CAAC,4FAA4F,CAAC;UAE1G;UACA,IAAIqE,OAAI,CAAC3H,QAAQ,KAAKyB,QAAQ,CAACkB,MAAM,KAAK,OAAO,IAAIlB,QAAQ,CAACkB,MAAM,KAAK,8BAA8B,CAAC,EAAE;YACxG/B,OAAO,CAACI,GAAG,CAAC,0EAA0E,CAAC;YACvF,MAAM2G,OAAI,CAAC/E,gBAAgB,CAAC,oBAAoB,CAAC;UACnD;QACF;QAEAhC,OAAO,CAACI,GAAG,CAAC,qCAAqC,CAAC;MACpD,CAAC,CAAC,OAAOL,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MACpD;IAAC;EACH;EAEA;EACA;EACA;EAEA;EACMkH,oBAAoBA,CAAA;IAAA,IAAAC,OAAA;IAAA,OAAArH,iBAAA;MACxB,IAAI,CAACqH,OAAI,CAAChI,MAAM,IAAI,CAACgI,OAAI,CAAC/H,UAAU,EAAE;MAEtC,IAAI;QACFa,OAAO,CAACI,GAAG,CAAC,uCAAuC,CAAC;QACpDJ,OAAO,CAACI,GAAG,CAAC,mBAAmB8G,OAAI,CAAC/H,UAAU,cAAc+H,OAAI,CAAChI,MAAM,EAAE,CAAC;QAE1E;QACA,MAAMoC,UAAU,GAAGpD,GAAG,CAACgJ,OAAI,CAAC5H,EAAE,EAAE,GAAG4H,OAAI,CAAChI,MAAM,UAAU,CAAC;QACzD,MAAMqC,eAAe,SAASnD,GAAG,CAACkD,UAAU,CAAC;QAE7C,IAAI,CAACC,eAAe,CAACb,MAAM,EAAE,EAAE;UAC7BV,OAAO,CAACI,GAAG,CAAC,qCAAqC,CAAC;UAClDJ,OAAO,CAACI,GAAG,CAAC,2CAA2C,CAAC;UACxD;QACF;QAEA,IAAIoB,OAAO,GAAaD,eAAe,CAACT,GAAG,EAAE,IAAI,EAAE;QACnDd,OAAO,CAACI,GAAG,CAAC,kBAAkB,EAAEoB,OAAO,CAAC;QAExC;QACA,MAAM2F,WAAW,GAAG3F,OAAO,CAAC4F,OAAO,CAACF,OAAI,CAAC/H,UAAU,CAAC;QACpD,IAAIgI,WAAW,KAAK,CAAC,CAAC,EAAE;UACtBnH,OAAO,CAACI,GAAG,CAAC,UAAU8G,OAAI,CAAC/H,UAAU,oBAAoB,CAAC;UAC1Da,OAAO,CAACI,GAAG,CAAC,2CAA2C,CAAC;UACxD;QACF;QAEA;QACAoB,OAAO,CAAC6F,MAAM,CAACF,WAAW,EAAE,CAAC,CAAC;QAC9BnH,OAAO,CAACI,GAAG,CAAC,wBAAwB,EAAEoB,OAAO,CAAC;QAE9C;QACA,MAAMrD,GAAG,CAACmD,UAAU,EAAEE,OAAO,CAAC;QAC9BxB,OAAO,CAACI,GAAG,CAAC,UAAU8G,OAAI,CAAC/H,UAAU,oBAAoB,CAAC;QAE1D;QACA+H,OAAI,CAACR,eAAe,EAAE;QAEtB1G,OAAO,CAACI,GAAG,CAAC,2CAA2C,CAAC;MAC1D,CAAC,CAAC,OAAOL,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxDC,OAAO,CAACI,GAAG,CAAC,wDAAwD,CAAC;MACvE;IAAC;EACH;;mBAp0BW3B,eAAe;;mCAAfA,gBAAe;AAAA;;SAAfA,gBAAe;EAAA6I,OAAA,EAAf7I,gBAAe,CAAA8I,IAAA;EAAAC,UAAA,EAFd;AAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}