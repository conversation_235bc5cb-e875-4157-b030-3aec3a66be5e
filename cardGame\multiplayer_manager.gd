extends Node

# References
var firebase
var card_game

# Game state
var players = []
var player_answers = {}
var response_indices_by_player = {}

# Signals
signal player_joined(player_name)
signal player_answer_selected(player_name, response)
signal game_created(game_id, room_code)

func _ready():
	# Get references
	firebase = get_node("/root/FirebaseManager")

	# Connect signals
	# Note: We removed the authenticated signal since we simplified the Firebase manager
	firebase.game_created.connect(_on_game_created)
	firebase.player_joined.connect(_handle_firebase_player_joined)
	firebase.player_answer_selected.connect(_on_player_answer_selected)
	firebase.database_error.connect(_on_database_error)

	# Connect to game status changes
	if firebase.has_signal("game_status_changed"):
		print("Connecting to game_status_changed signal in multiplayer_manager")
		firebase.game_status_changed.connect(_on_game_status_changed)
	else:
		push_error("Signal game_status_changed not found in firebase_manager")

	print("Multiplayer manager initialized")

func initialize_game(game_node):
	card_game = game_node
	response_indices_by_player.clear()
	print("Cleared response indices by player")

func start_new_game(force_new_room_code: bool = false):
	# Clear previous game state
	players = []
	player_answers = {}
	response_indices_by_player.clear()
	print("Cleared response indices by player for new game")

	# Create a new game in Firebase
	# We're passing an empty dictionary for response_indices_by_player
	# as we no longer want to store this in Firebase
	# force_new_room_code determines if we generate a new room code (different players) or reuse existing (same players)
	firebase.create_game({}, force_new_room_code)

	# Clear any existing response indices in Firebase
	if firebase and firebase.current_game_id != "":
		# Wait a moment to ensure the game is created
		await get_tree().create_timer(0.5).timeout

		# Clear response indices, player_response_texts, and player_responses
		firebase.update_game_data("response_indices", null)
		firebase.update_game_data("player_response_texts", null)
		firebase.update_game_data("player_responses", null)
		firebase.update_game_data("used_responses", null)
		print("Cleared response indices and related data in Firebase")

	# After a short delay, check if the room code has been set and update the UI
	# Note: We don't need to emit the signal here anymore since it will be emitted by _on_game_created
	await get_tree().create_timer(0.5).timeout
	print("[ROOM CODE DEBUG] Multiplayer manager: After delay, room code is: ", firebase.current_room_code)

# This function is no longer needed since we simplified the Firebase manager
# func _on_firebase_authenticated():
# 	print("Firebase authenticated, ready to create game")

func _on_game_created(game_id, room_code):
	print("Game created with ID: " + game_id + ", Room Code: " + room_code)
	response_indices_by_player.clear()
	print("Cleared response indices by player after game creation")

	# Always use the room code from Firebase to ensure consistency
	var firebase_room_code = firebase.current_room_code
	print("[ROOM CODE DEBUG] Multiplayer manager using Firebase room code: ", firebase_room_code)

	# Emit our own signal with the Firebase room code
	print("[ROOM CODE DEBUG] Multiplayer manager emitting game_created with room code: ", firebase_room_code)
	emit_signal("game_created", game_id, firebase_room_code)

	# Update UI to show room code
	if card_game:
		print("[ROOM CODE DEBUG] Multiplayer manager calling card_game.show_room_code with: ", firebase_room_code)
		card_game.show_room_code(game_id, firebase_room_code)  # Using Firebase room code

func _handle_firebase_player_joined(player_name):
	print("MultiplayerManager: Player joined - ", player_name)
	# Add player to the list if not already present
	if not player_name in players:
		print("Player joined: ", player_name)
		players.append(player_name)

		# Notify the game
		emit_signal("player_joined", player_name)

		if card_game:
			card_game.on_player_joined(player_name)

func _on_player_answer_selected(player_name, answer_index):
	if not player_answers.has(player_name) or player_answers[player_name] != answer_index:
		print("Player ", player_name, " selected answer index: ", answer_index)
		player_answers[player_name] = answer_index

		# Get the actual response from the index
		var response = get_response_from_index(player_name, answer_index)

		# Notify the game
		emit_signal("player_answer_selected", player_name, response)

		if card_game:
			card_game.on_player_answer_selected(player_name, response)

func assign_cards_to_player(player_name):
	print("Skipping card assignment for player: " + player_name)
	# No longer assigning cards or updating Firebase

func get_response_from_index(player_name, index):
	# Get the actual response text from the index
	if card_game:
		return card_game.get_response_by_index(player_name, index)
	return "Unknown response"

func get_room_code():
	var code = firebase.current_room_code
	print("[ROOM CODE DEBUG] Multiplayer manager get_room_code returning: ", code)
	return code

# Game state update functions
func update_game_status(status):
	print("\n\nMULTIPLAYER MANAGER: update_game_status called with status: ", status)

	if not firebase:
		print("ERROR: Firebase reference is null")
		return

	if firebase.current_game_id == "":
		print("ERROR: No current game ID in Firebase")
		return

	print("Updating game status to: ", status)
	print("Current game ID: ", firebase.current_game_id)
	firebase.update_game_data("status", status)
	print("Game status update call completed")

	# No need to verify the status update - removed verify_game_status call
	# as it doesn't exist in our simplified firebase_manager.gd

func update_current_round(round_number):
	if firebase and firebase.current_game_id != "":
		print("Updating current round to: ", round_number)
		firebase.update_game_data("current_round", round_number)

func update_current_judge(judge_name):
	if firebase and firebase.current_game_id != "":
		print("Updating current judge to: ", judge_name)
		firebase.update_game_data("current_judge", judge_name)

func update_current_judge_index(judge_index):
	if firebase and firebase.current_game_id != "":
		print("Updating current judge index to: ", judge_index)
		firebase.update_current_judge_index(judge_index)

func update_current_prompt(prompt):
	if firebase and firebase.current_game_id != "":
		print("\n\n==== UPDATING CURRENT PROMPT IN MULTIPLAYER MANAGER ====")
		print("Updating current prompt: ", prompt)
		print("Firebase game ID: ", firebase.current_game_id)

		# Make sure the prompt is properly formatted
		var formatted_prompt

		if typeof(prompt) == TYPE_DICTIONARY and prompt.has("prompt"):
			# Already in the correct format
			formatted_prompt = {"prompt": prompt.get("prompt")}
			print("Prompt already in correct format: ", formatted_prompt)
		else:
			# Convert to the correct format
			formatted_prompt = {"prompt": str(prompt)}
			print("Converted prompt to correct format: ", formatted_prompt)

		print("Final formatted prompt for Firebase: ", formatted_prompt)

		# Update the prompt in Firebase
		firebase.update_game_data("current_prompt", formatted_prompt)
		print("Prompt update sent to Firebase")
		print("==== END UPDATING CURRENT PROMPT ====\n\n")

func update_player_response_indices(player_name, _indices):
	# No longer updating response indices in Firebase
	# The web client now manages responses locally
	print("SKIPPING update of response indices for player: ", player_name)
	# We're keeping this function for backward compatibility, but it no longer does anything

func update_timer_end(end_time):
	if firebase and firebase.current_game_id != "":
		print("Updating timer end to: ", end_time)
		firebase.update_game_data("timer_end", end_time)

func update_round_winner(player_name, card_index, round_number):
	if firebase and firebase.current_game_id != "":
		print("Updating round winner to: ", player_name)
		var winner_data = {
			"player": player_name,
			"card_index": card_index,
			"round": round_number
		}
		firebase.update_game_data("round_winner", winner_data)

func update_player_score(player_name, score):
	if firebase and firebase.current_game_id != "":
		print("Updating score for player ", player_name, " to ", score)
		firebase.update_game_data("player_scores/" + player_name, score)

func update_game_winner(player_name):
	if firebase and firebase.current_game_id != "":
		print("Setting game winner to: ", player_name)
		firebase.update_game_data("winner", player_name)

func _on_database_error(error_code, error_message):
	print("Multiplayer manager: Database error: ", error_code, " - ", error_message)

	# Notify the card game if available
	if card_game and card_game.has_method("on_database_error"):
		card_game.on_database_error(error_code, error_message)

# Handle game status changes from Firebase
func _on_game_status_changed(new_status):
	print("MULTIPLAYER MANAGER: Game status changed to: ", new_status)

	# Forward the status change to the card game
	if card_game and card_game.has_method("handle_game_state_change"):
		print("Forwarding game status change to card game")
		card_game.handle_game_state_change(new_status)
	else:
		print("WARNING: Cannot forward game status change - card_game reference is null or missing handle_game_state_change method")

# Handle game listener updates
func _on_game_listener_completed(_result, response_code, _headers, body):
	if response_code != 200:
		print("Game listener error: ", response_code)
		return

	# Process game updates
	var response = JSON.parse_string(body.get_string_from_utf8())
	if response != null and typeof(response) == TYPE_DICTIONARY:
		# Check for game status changes
		if response.has("status") and card_game:
			var new_status = response.status

			# Only process if the status has changed
			if new_status != card_game.game_status:
				print("Game status changed from ", card_game.game_status, " to ", new_status)

				# Note: We're now handling this via the game_status_changed signal
				# This code is kept for backward compatibility
				if card_game.has_method("handle_game_state_change"):
					print("Calling handle_game_state_change from _on_game_listener_completed")
					card_game.handle_game_state_change(new_status)

# Get the winner of the current game from Firebase
func get_game_winner():
	if firebase and firebase.current_game_id != "":
		# This is now an async function that returns a Promise
		return await firebase.get_game_winner()
	return ""

# Get the winning response from Firebase
func get_winning_response():
	if firebase and firebase.current_game_id != "":
		# This is now an async function that returns a Promise
		return await firebase.get_winning_response()
	return ""

# Update the round winner in Firebase
func update_round_winner_name(winner_name):
	if firebase and firebase.current_game_id != "":
		firebase.update_game_data("winner", winner_name)

# Update the winning response in Firebase
func update_winning_response(response):
	if firebase and firebase.current_game_id != "":
		firebase.update_game_data("winning_response", response)

# Get submitted responses from Firebase cache (no more direct calls)
func get_submitted_responses():
	if not firebase or firebase.current_game_id == "":
		print("ERROR: Cannot get submitted responses - Firebase not initialized or no game ID")
		return []

	# Use cached data from firebase_manager instead of making HTTP requests
	return firebase.get_submitted_responses()
