extends Node
class_name Player

var pname: String = ""
var score: int = 0
var hand: Array = []

func _init(player_name: String = ""):
	pname = player_name  # Fixed: use different parameter name
	hand = []

func add_card_to_hand(card):
	if hand.size() < 5:
		hand.append(card)
	else:
		print("Hand is full!")

func remove_card_from_hand(card):
	if card in hand:
		hand.erase(card)
	else:
		print("Card not found in hand!")

func clear_hand():
	hand.clear()
