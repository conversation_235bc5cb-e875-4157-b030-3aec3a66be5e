extends Node

# Firebase configuration
const DEFAULT_DATABASE_URL = "https://cjsc-a3e91-default-rtdb.firebaseio.com"
var current_database_url = DEFAULT_DATABASE_URL

# Game state
var current_game_id = ""
var current_room_code = ""
var last_firebase_status = ""

# HTTP request nodes
var database_request
var game_listener
var players_listener
var answers_listener
var stream_request  # New: For Firebase streaming

# Cached game data (updated via streaming)
var cached_game_data = {}
var cached_players = []
var cached_submitted_responses = []
var cached_judge_index = 0
var cached_game_status = ""
var cached_current_round = 0
var cached_winner = ""
var cached_winning_response = ""

# Streaming state
var is_streaming = false
var stream_buffer = ""

# Signals
signal game_created(game_id, room_code)
signal player_joined(player_name)
signal player_answer_selected(player_name, answer_index)
signal player_response_used(player_name, response_index)
signal database_error(error_code, error_message)
signal game_status_changed(new_status)
signal submitted_responses_updated(submitted_responses_data)
signal judge_index_updated(new_judge_index)
signal players_updated(players_data)
signal current_round_updated(round_number)
signal winner_updated(winner_name)
signal winning_response_updated(response_data)
signal new_game_different_players()

func _ready():
	# Initialize HTTP request nodes
	database_request = HTTPRequest.new()
	game_listener = HTTPRequest.new()
	players_listener = HTTPRequest.new()
	answers_listener = HTTPRequest.new()
	stream_request = HTTPRequest.new()  # New: For Firebase streaming

	add_child(database_request)
	add_child(game_listener)
	add_child(players_listener)
	add_child(answers_listener)
	add_child(stream_request)

	# Connect signals
	database_request.request_completed.connect(_on_database_request_completed)
	game_listener.request_completed.connect(_on_game_listener_completed)
	players_listener.request_completed.connect(_on_players_listener_completed)
	answers_listener.request_completed.connect(_on_answers_listener_completed)

	# Connect streaming signals
	stream_request.request_completed.connect(_on_stream_request_completed)

	print("Firebase manager initialized")

# Generate a random 5-letter room code
func generate_room_code(force_new: bool = false):
	print("\n\n[ROOM CODE DEBUG] Firebase manager: generate_room_code called (force_new: ", force_new, ")\n\n")

	# Check if we already have a room code and we're not forcing a new one
	if current_room_code != "" and current_room_code.length() == 5 and not force_new:
		print("[ROOM CODE DEBUG] Firebase manager: Already have a room code: ", current_room_code)
		print("[ROOM CODE DEBUG] Firebase manager: Returning existing room code instead of generating a new one")
		return current_room_code

	var first_letter_chars = "AM" # First letter must be A or M
	var chars = "ABCDEFGHJKLMNPQRSTUVWXYZ23456789" # Removed similar looking characters
	var code = ""
	var rng = RandomNumberGenerator.new()
	rng.randomize()

	# Set first letter to A or M
	var first_idx = rng.randi_range(0, first_letter_chars.length() - 1)
	code += first_letter_chars[first_idx]

	# Generate the remaining 4 random characters
	for i in range(4):
		var idx = rng.randi_range(0, chars.length() - 1)
		code += chars[idx]

	print("\n\n[ROOM CODE DEBUG] Firebase manager: Generated NEW room code: ", code, "\n\n")
	return code

# Create a new game in Firebase
func create_game(response_indices_by_player = {}, force_new_room_code: bool = false):
	# Generate a random 5-letter room code
	var room_code = generate_room_code(force_new_room_code)
	current_room_code = room_code
	print("[ROOM CODE DEBUG] Firebase generated room code: ", room_code, " (force_new: ", force_new_room_code, ")")

	# Create game object
	var game_data = {
		"timestamp": Time.get_unix_time_from_system(),
		"room_code": room_code,
		"status": "Waiting for Players",
		"players": [],
		"player_scores": {},
		"player_answers": {},
		"response_indices": response_indices_by_player,
		"submitted_responses": []
	}

	# Save to Firebase
	var url = current_database_url + "/.json"
	print("Database URL: ", url)

	var body = JSON.stringify(game_data)
	print("Game data: ", body)

	var headers = ["Content-Type: application/json"]

	print("Sending game data to Firebase with room code: ", room_code)
	var error = database_request.request(url, headers, HTTPClient.METHOD_POST, body)
	if error != OK:
		print("HTTP Request Error: ", error)

		# Error code 44 is a common network-related error that might be a false positive
		# We'll log it but continue with the game creation process
		if error == 44:
			print("Warning: Got error code 44, but this might be a false positive. Continuing...")
		else:
			emit_signal("database_error", error, "Failed to create game")
			return

	print("Game creation request sent successfully")

# Handle database request completion
func _on_database_request_completed(result, response_code, headers, body):
	print("Database request completed with result: ", result, ", response code: ", response_code)

	# Check for successful result
	if result != HTTPRequest.RESULT_SUCCESS:
		print("Database request failed with result: ", result)

		# Don't treat this as a fatal error, just log it and continue
		print("Warning: Database request failed, but we'll try to continue anyway")

	# Check for valid response code
	if response_code != 200:
		print("Database request failed with response code: ", response_code)

		# Don't treat this as a fatal error, just log it and continue
		print("Warning: Got non-200 response code, but we'll try to continue anyway")

	var response_text = body.get_string_from_utf8()
	print("Response: ", response_text)

	var response = JSON.parse_string(response_text)
	if response == null:
		print("Failed to parse JSON response")
		emit_signal("database_error", -1, "Failed to parse JSON response")
		return

	if response.has("name"):
		current_game_id = response.name
		print("Game created with ID: ", current_game_id)

		# Start listening for changes
		start_game_listeners()

		# Emit signal with game ID and room code
		emit_signal("game_created", current_game_id, current_room_code)
	else:
		print("Response missing game ID")
		emit_signal("database_error", -1, "Response missing game ID")

# Get the current room code
func get_room_code():
	print("[ROOM CODE DEBUG] Firebase manager: get_room_code returning: ", current_room_code)
	return current_room_code

# Start Firebase streaming listener for real-time updates
func start_firebase_streaming():
	print("Starting Firebase streaming for game ID: ", current_game_id)

	# Check if we have a valid game ID
	if current_game_id == "":
		print("Cannot start streaming: No game ID")
		return

	if is_streaming:
		print("Already streaming, stopping previous stream")
		stop_firebase_streaming()

	# First, get initial data to populate cache
	_fetch_initial_game_data()

	# Set up periodic polling as a simpler alternative to SSE
	# This is more reliable than trying to implement SSE with Godot's HTTPRequest
	_start_polling_timer()

	is_streaming = true
	print("Firebase streaming (polling) started successfully")

# Fetch initial game data to populate cache
func _fetch_initial_game_data():
	print("Fetching initial game data to populate cache...")

	var initial_request = HTTPRequest.new()
	add_child(initial_request)

	var url = current_database_url + "/" + current_game_id + ".json"
	print("Fetching initial data from: ", url)

	initial_request.request_completed.connect(func(result, response_code, headers, body):
		if result == HTTPRequest.RESULT_SUCCESS and response_code == 200:
			var response_text = body.get_string_from_utf8()
			var game_data = JSON.parse_string(response_text)
			if game_data:
				print("Initial game data fetched successfully")
				_update_cached_data(game_data)
			else:
				print("Failed to parse initial game data")
		else:
			print("Failed to fetch initial game data - Result: ", result, ", Code: ", response_code)

		initial_request.queue_free()
	)

	initial_request.request(url)

# Start polling timer for regular updates (more reliable than SSE)
func _start_polling_timer():
	# Create a timer for periodic updates (every 2 seconds - much less frequent than before)
	var polling_timer = Timer.new()
	polling_timer.name = "FirebasePollingTimer"
	polling_timer.wait_time = 2.0  # Poll every 2 seconds instead of 0.5
	polling_timer.one_shot = false
	polling_timer.autostart = true
	polling_timer.timeout.connect(_poll_firebase_data)
	add_child(polling_timer)
	print("Started Firebase polling timer with 2-second interval")

# Poll Firebase data periodically
func _poll_firebase_data():
	if not is_streaming or current_game_id == "":
		return

	var poll_request = HTTPRequest.new()
	add_child(poll_request)

	var url = current_database_url + "/" + current_game_id + ".json"

	poll_request.request_completed.connect(func(result, response_code, headers, body):
		if result == HTTPRequest.RESULT_SUCCESS and response_code == 200:
			var response_text = body.get_string_from_utf8()
			var game_data = JSON.parse_string(response_text)
			if game_data:
				_update_cached_data(game_data)

		poll_request.queue_free()
	)

	poll_request.request(url)

# Stop Firebase streaming
func stop_firebase_streaming():
	if is_streaming:
		print("Stopping Firebase streaming")
		is_streaming = false

		# Stop the polling timer
		var polling_timer = get_node_or_null("FirebasePollingTimer")
		if polling_timer:
			polling_timer.queue_free()
			print("Stopped Firebase polling timer")

# Start listening for changes to the game (legacy method, now calls streaming)
func start_game_listeners():
	print("Starting game listeners for game ID: ", current_game_id)
	start_firebase_streaming()

# Handle game listener completion
func _on_game_listener_completed(result, response_code, headers, body):
	if result != HTTPRequest.RESULT_SUCCESS:
		print("Game listener failed with result: ", result)
		print("Warning: Game listener failed, but we'll try to continue anyway")

	if response_code != 200:
		print("Game listener failed with response code: ", response_code)
		print("Warning: Game listener got non-200 response code, but we'll try to continue anyway")

	# Process the response
	var response_text = body.get_string_from_utf8()

	# Parse the response
	var response = JSON.parse_string(response_text)
	if response != null and typeof(response) == TYPE_DICTIONARY:
		print("DEBUG: Firebase listener received data with keys: ", response.keys())
		# Check for game status changes
		if response.has("status"):
			var new_status = response.status

			# Only emit the signal if the status has changed
			if new_status != last_firebase_status:
				print("Status changed from ", last_firebase_status, " to ", new_status)
				last_firebase_status = new_status
				emit_signal("game_status_changed", new_status)

		# Check for used responses
		if response.has("used_responses") and typeof(response.used_responses) == TYPE_DICTIONARY:
			var used_responses = response.used_responses
			print("Found used responses: ", used_responses)

			# Store the last processed used responses to avoid duplicate processing
			var last_processed_used_responses = {}

			# Process each player's used responses
			for player_name in used_responses.keys():
				var player_used_indices = used_responses[player_name]
				if typeof(player_used_indices) == TYPE_ARRAY and player_used_indices.size() > 0:
					print("Player ", player_name, " has used responses: ", player_used_indices)

					# Get the last processed indices for this player
					var last_processed_indices = []
					if last_processed_used_responses.has(player_name):
						last_processed_indices = last_processed_used_responses[player_name]

					# Process only new indices
					for index in player_used_indices:
						if not index in last_processed_indices:
							print("Processing NEW used response index ", index, " for player ", player_name)
							emit_signal("player_response_used", player_name, index)

							# Add to last processed indices
							last_processed_indices.append(index)

					# Update last processed indices for this player
					last_processed_used_responses[player_name] = last_processed_indices

			# Clear used responses after processing to ensure they're only processed once
			if used_responses.size() > 0:
				print("Clearing used responses after processing")
				update_game_data("used_responses", {})

		# Check for submitted responses changes
		if response.has("submitted_responses") and typeof(response.submitted_responses) == TYPE_ARRAY:
			var submitted_responses = response.submitted_responses
			print("Firebase submitted responses array: ", submitted_responses)

			# Emit signal with submitted responses data
			if submitted_responses.size() > 0:
				print("Emitting submitted_responses_updated signal with ", submitted_responses.size(), " responses")
				emit_signal("submitted_responses_updated", submitted_responses)

		# Check for judge index changes
		if response.has("current_judge_index"):
			var new_judge_index = response.current_judge_index
			print("Firebase judge index: ", new_judge_index)

			# Emit the dedicated judge index updated signal
			emit_signal("judge_index_updated", new_judge_index)

		# Check for player changes
		if response.has("players") and typeof(response.players) == TYPE_ARRAY:
			var players = response.players
			print("Firebase players array: ", players)

			# Notify about each player
			for player_name in players:
				if typeof(player_name) == TYPE_STRING:
					print("Emitting player_joined signal for: ", player_name)
					emit_signal("player_joined", player_name)

	# Restart the listener after a short delay
	await get_tree().create_timer(1.0).timeout
	if current_game_id != "":
		var game_url = current_database_url + "/" + current_game_id + ".json"
		game_listener.request(game_url)

# Handle players listener completion
func _on_players_listener_completed(result, response_code, headers, body):
	# Placeholder for player updates
	pass

# Handle answers listener completion
func _on_answers_listener_completed(result, response_code, headers, body):
	# Placeholder for answer updates
	pass

# Handle Firebase streaming response (now unused, kept for compatibility)
func _on_stream_request_completed(result, response_code, headers, body):
	# This is now unused since we switched to polling
	print("Stream request completed (unused in polling mode)")

# Update cached data and emit signals when data changes
func _update_cached_data(new_data):
	if typeof(new_data) != TYPE_DICTIONARY:
		print("Invalid data type received from Firebase: ", typeof(new_data))
		return

	print("Updating cached data with keys: ", new_data.keys())

	# Store the complete game data
	cached_game_data = new_data

	# Check for game status changes
	if new_data.has("status"):
		var new_status = new_data.status
		if new_status != cached_game_status:
			print("Game status changed from '", cached_game_status, "' to '", new_status, "'")
			cached_game_status = new_status
			last_firebase_status = new_status
			emit_signal("game_status_changed", new_status)

	# Check for judge index changes
	if new_data.has("current_judge_index"):
		var new_judge_index = new_data.current_judge_index
		# Handle both int and float types
		if typeof(new_judge_index) == TYPE_FLOAT:
			new_judge_index = int(new_judge_index)

		if new_judge_index != cached_judge_index:
			print("Judge index changed from ", cached_judge_index, " to ", new_judge_index)
			cached_judge_index = new_judge_index
			emit_signal("judge_index_updated", new_judge_index)

	# Check for submitted responses changes
	if new_data.has("submitted_responses"):
		var new_responses = new_data.submitted_responses
		if new_responses != cached_submitted_responses:
			print("Submitted responses updated - Count: ", new_responses.size() if new_responses else 0)
			cached_submitted_responses = new_responses if new_responses else []
			emit_signal("submitted_responses_updated", cached_submitted_responses)

	# Check for players changes
	if new_data.has("players"):
		var new_players = new_data.players
		if new_players != cached_players:
			print("Players updated - Count: ", new_players.size() if new_players else 0)
			cached_players = new_players if new_players else []
			emit_signal("players_updated", cached_players)

	# Check for current round changes
	if new_data.has("current_round"):
		var new_round = new_data.current_round
		if typeof(new_round) == TYPE_FLOAT:
			new_round = int(new_round)

		if new_round != cached_current_round:
			print("Current round changed from ", cached_current_round, " to ", new_round)
			cached_current_round = new_round
			emit_signal("current_round_updated", new_round)

	# Check for winner changes
	if new_data.has("winner"):
		var new_winner = new_data.winner
		if new_winner != cached_winner:
			print("Winner updated to: ", new_winner)
			cached_winner = new_winner if new_winner else ""
			emit_signal("winner_updated", cached_winner)

	# Check for winning response changes
	if new_data.has("winning_response"):
		var new_winning_response = new_data.winning_response
		if new_winning_response != cached_winning_response:
			print("Winning response updated")
			cached_winning_response = new_winning_response if new_winning_response else ""
			emit_signal("winning_response_updated", cached_winning_response)

	# Check for reset_game flag (indicates new game with different players)
	if new_data.has("reset_game") and new_data.reset_game == true:
		print("Reset game flag detected - new game with different players requested")
		# Clear the flag immediately to prevent repeated processing
		update_game_data("reset_game", false)
		# Force a new room code generation
		force_new_room_code()
		# Emit a signal to notify the game
		emit_signal("new_game_different_players")

# Update the game data in Firebase
func update_game_data(path, data):
	print("Updating game data")
	print("Path: ", path)
	print("Data: ", data)

	# Check if we have a valid game ID
	if current_game_id == "":
		print("ERROR: Cannot update game data: No game ID")
		return

	# Validate path
	if path == "" or path == null:
		print("ERROR: Cannot update game data: Invalid path")
		return

	var url = current_database_url + "/" + current_game_id + "/" + path + ".json"
	print("Update URL: ", url)

	var body = JSON.stringify(data)
	print("Update data: ", body)

	var update_request = HTTPRequest.new()
	add_child(update_request)
	update_request.request_completed.connect(func(result, response_code, headers, body):
		print("Update request completed with result: ", result, ", response code: ", response_code)

		if result != HTTPRequest.RESULT_SUCCESS:
			print("Update request failed with result: ", result)
			print("Warning: Update request failed, but we'll try to continue anyway")

		if response_code != 200:
			print("Update request failed with response code: ", response_code)
			print("Warning: Update request got non-200 response code, but we'll try to continue anyway")

		# Always consider the update successful for our purposes
		print("Treating update as successful regardless of response")

		# Special handling for status updates
		if path == "status":
			last_firebase_status = data
			emit_signal("game_status_changed", data)

		update_request.queue_free()
	)

	var headers = ["Content-Type: application/json"]
	var error = update_request.request(url, headers, HTTPClient.METHOD_PATCH, body)
	if error != OK:
		print("Update request error: ", error)
		update_request.queue_free()
		return

# Game state update functions
func update_game_status(status):
	print("Updating game status to: ", status)
	update_game_data("status", status)

func update_current_round(round_number):
	print("Updating current round to: ", round_number)
	update_game_data("current_round", round_number)

func update_current_judge(judge_name):
	print("Updating current judge to: ", judge_name)
	update_game_data("current_judge", judge_name)

func update_current_judge_index(judge_index):
	print("Updating current judge index to: ", judge_index)
	update_game_data("current_judge_index", judge_index)

func update_current_prompt(prompt):
	print("Updating current prompt: ", prompt)

	# Make sure the prompt is in the correct format for Firebase
	var formatted_prompt

	if typeof(prompt) == TYPE_DICTIONARY and prompt.has("prompt"):
		# Already in the correct format
		formatted_prompt = prompt
		print("Prompt already in correct format: ", formatted_prompt)
	else:
		# Convert to the correct format
		formatted_prompt = {"prompt": str(prompt)}
		print("Converted prompt to correct format: ", formatted_prompt)

	# Update the prompt in Firebase
	update_game_data("current_prompt", formatted_prompt)

func update_timer_end(end_time):
	print("Updating timer end to: ", end_time)
	update_game_data("timer_end", end_time)

func update_player_response_indices(player_name, indices):
	print("Updating response indices for player: ", player_name)
	var path = "response_indices/" + player_name
	update_game_data(path, indices)

func update_player_answer(player_name, answer):
	print("Updating player answer for: ", player_name)
	var path = "player_answers/" + player_name
	update_game_data(path, answer)

func update_game_winner(winner_name):
	print("Updating game winner to: ", winner_name)
	update_game_data("winner", winner_name)

func update_winning_response(response):
	print("Updating winning response")
	update_game_data("winning_response", response)

# Add a player to the game
func add_player(player_name):
	print("Adding player: ", player_name)

	# Get current players array
	var get_players_request = HTTPRequest.new()
	add_child(get_players_request)

	var url = current_database_url + "/" + current_game_id + "/players.json"

	get_players_request.request_completed.connect(func(result, response_code, headers, body):
		# Handle errors gracefully
		if result != HTTPRequest.RESULT_SUCCESS:
			print("Get players request failed with result: ", result)
			print("Warning: Get players request failed, but we'll try to continue anyway")

			# Create an empty players array and proceed
			var players_array = []
			players_array.append(player_name)

			# Update the players array
			update_game_data("players", players_array)

			# Initialize player score
			update_game_data("player_scores/" + player_name, 0)

			# Emit signal
			emit_signal("player_joined", player_name)
			get_players_request.queue_free()
			return

		if response_code != 200:
			print("Get players request failed with response code: ", response_code)
			print("Warning: Get players request got non-200 response code, but we'll try to continue anyway")

			# Create an empty players array and proceed
			var players_array = []
			players_array.append(player_name)

			# Update the players array
			update_game_data("players", players_array)

			# Initialize player score
			update_game_data("player_scores/" + player_name, 0)

			# Emit signal
			emit_signal("player_joined", player_name)
			get_players_request.queue_free()
			return

		# If we got here, the request was successful
		var response_text = body.get_string_from_utf8()
		var players_array = JSON.parse_string(response_text)

		if players_array == null:
			players_array = []

		# Add the new player if not already in the array
		if not player_name in players_array:
			players_array.append(player_name)

			# Update the players array
			update_game_data("players", players_array)

			# Initialize player score
			update_game_data("player_scores/" + player_name, 0)

			# Emit signal
			emit_signal("player_joined", player_name)

		get_players_request.queue_free()
	)

	get_players_request.request(url)

# Get game data from cache (no more Firebase calls needed)
func get_game_winner():
	print("Getting game winner from cache: ", cached_winner)
	return cached_winner if cached_winner != "" else "No winner available"

func get_current_judge_index():
	print("Getting current judge index from cache: ", cached_judge_index)
	return cached_judge_index

# Debug function to get cached game data (no more HTTP requests)
func debug_get_entire_game_data():
	print("DEBUG: Getting cached game data structure:")
	print(cached_game_data)

	if cached_game_data.has("current_judge_index"):
		print("DEBUG: current_judge_index field exists with value: ", cached_game_data.current_judge_index, " (type: ", typeof(cached_game_data.current_judge_index), ")")
	else:
		print("DEBUG: current_judge_index field NOT FOUND in cached data")

	return cached_game_data

func get_winning_response():
	print("Getting winning response from cache: ", cached_winning_response)
	return cached_winning_response if cached_winning_response != "" else "No winning response available"

# Get players from cache
func get_players():
	print("Getting players from cache: ", cached_players)
	return cached_players

# Get submitted responses from cache
func get_submitted_responses():
	print("Getting submitted responses from cache: ", cached_submitted_responses)
	return cached_submitted_responses

# Get judge index from cache (no more Firebase calls needed)
func update_judge_index_at_round_start():
	var judge_index = get_current_judge_index()
	if judge_index >= 0:
		emit_signal("judge_index_updated", judge_index)
		print("Judge index from cache at round start: " + str(judge_index))

# Manual function to restart Firebase streaming if needed
func restart_firebase_streaming():
	print("Manually restarting Firebase streaming...")
	stop_firebase_streaming()
	await get_tree().create_timer(1.0).timeout
	start_firebase_streaming()

# Get current streaming status
func is_firebase_streaming():
	return is_streaming

# Force an immediate Firebase data refresh
func force_data_refresh():
	print("Forcing immediate Firebase data refresh...")
	_poll_firebase_data()

# Clear cached data for new round
func clear_round_data():
	print("Clearing cached round data...")
	cached_submitted_responses = []
	cached_winner = ""
	cached_winning_response = ""
	print("Cleared submitted responses, winner, and winning response from cache")

# Force generation of a new room code (for new games with different players)
func force_new_room_code():
	print("Forcing generation of new room code...")
	var old_room_code = current_room_code
	current_room_code = ""  # Clear current room code
	var new_room_code = generate_room_code(true)  # Force new generation
	current_room_code = new_room_code
	print("Room code changed from '", old_room_code, "' to '", new_room_code, "'")
	return new_room_code
