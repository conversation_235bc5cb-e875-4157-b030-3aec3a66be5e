.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.prompt {
  font-size: 1.2rem;
  font-weight: bold;
  padding: 15px;
  background-color: var(--ion-color-light);
  border-radius: 8px;
  margin: 10px 0;
}

.winning-response {
  font-size: 1.1rem;
  font-style: italic;
  padding: 15px;
  background-color: var(--ion-color-success-tint);
  border-radius: 8px;
  margin: 10px 0;
  color: var(--ion-color-success-contrast);
}

.error-message {
  color: var(--ion-color-danger);
  font-size: 0.8rem;
  margin: 5px 0 0 16px;
}

.lobby-container, .game-container {
  max-width: 600px;
  margin: 0 auto;
}

// Style for selected responses
.selected-response {
  --background: var(--ion-color-primary-tint);
  --color: var(--ion-color-primary-contrast);

  ion-label {
    font-weight: bold;
  }

  &::part(native) {
    border-left: 4px solid var(--ion-color-primary);
  }
}

// Add hover effect for clickable responses
ion-item[button] {
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover:not(.selected-response) {
    --background: var(--ion-color-light-shade);
  }
}
