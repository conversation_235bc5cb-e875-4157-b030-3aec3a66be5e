<ion-header [translucent]="true">
  <ion-toolbar color="primary">
    <ion-title>
      CJSC
    </ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true" class="ion-padding">
  <div class="join-container">
    <h1 class="ion-text-center">Join a Game</h1>

    <form (ngSubmit)="joinGame()" #joinForm="ngForm">
      <ion-item lines="none">
        <ion-label position="floating">Room Code</ion-label>
        <ion-input
          [(ngModel)]="roomCode"
          name="roomCode"
          required
          minlength="5"
          maxlength="5"
          autocapitalize="characters"
          #roomCodeInput="ngModel">
        </ion-input>
      </ion-item>
      <div *ngIf="roomCodeInput.invalid && (roomCodeInput.dirty || roomCodeInput.touched)" class="error-message">
        <div *ngIf="roomCodeInput.errors?.['required']">
          Room code is required.
        </div>
        <div *ngIf="roomCodeInput.errors?.['minlength']">
          Room code must be at least 5 characters.
        </div>
      </div>

      <ion-item lines="none">
        <ion-label position="floating">Your Name</ion-label>
        <ion-input
          [(ngModel)]="playerName"
          name="playerName"
          required
          minlength="2"
          #playerNameInput="ngModel">
        </ion-input>
      </ion-item>
      <div *ngIf="playerNameInput.invalid && (playerNameInput.dirty || playerNameInput.touched)" class="error-message">
        <div *ngIf="playerNameInput.errors?.['required']">
          Name is required.
        </div>
        <div *ngIf="playerNameInput.errors?.['minlength']">
          Name must be at least 2 characters.
        </div>
      </div>

      <ion-button
        expand="block"
        type="submit"
        [disabled]="joinForm.invalid || isJoining"
        class="ion-margin-top">
        {{ isJoining ? 'Joining...' : 'Join Game' }}
      </ion-button>
    </form>

    <div *ngIf="errorMessage" class="error-message ion-text-center ion-margin-top">
      {{ errorMessage }}
    </div>
  </div>
</ion-content>
