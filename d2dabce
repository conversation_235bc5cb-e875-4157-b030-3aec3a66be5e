[33m1571e22[m[33m ([m[1;36mHEAD[m[33m -> [m[1;32mmain[m[33m, [m[1;31morigin/main[m[33m, [m[1;31morigin/HEAD[m[33m)[m Improved authentication and security, but now prompts are not being issued.  Prompts and displays are not displayed on Godot client.
[33m1f06461[m Removed redundancies, trying to decrease read/writes
[33md2dabce[m Fixed hand persistence.
[33mc11ac52[m Player hand persistence partially working.  Resets when they become judge.  Probably tied to Player Roles function.
[33mcffbf4e[m Fixed round number bug
[33m3bedcf5[m Added real prompts and responses
[33mdf8b36a[m Removed spinners from web client
[33m6db91db[m Removed old, redundant functionality that uploaded enter array of player's hands, twice.
[33m2d49d73[m Fixed bug with old prompt displaying while new prompt loads
[33me036270[m Fixed judge asks function in Godot client
[33m165bf81[m Fixed judge rotation logic and removed lines from ion-items
[33mccd338b[m Upper cased the room code entry box
[33mf7f663a[m Updated Home page with no lines
[33mc52ba90[m Minor UI improvements, hid the multiplayer button in the main menu but did not remove the code.
[33m8ceea31[m Fixed bug where a player's selected response is reset when another player submits a response
[33m486cb55[m Updated Godot UI and improved winning prompt display
[33m5ae6b64[m Minor updates to UI
[33m86bc706[m Added custom responses and minor QoL
[33m5cf4ac6[m Got the prompts to show up on the Godot client and upload to web client.
[33m8e5c5cd[m Fixed QR code display.
[33m9ee1b08[m Many updates to UCG in Godot, added QR code, doesn't work yet.
[33mfefbed4[m Continued updates to untitle card game GUI
[33m3c84698[m Continued updates to untitle card game GUI
[33mcaa7160[m Cleaning up Godot client GUI and game state management (in progress).
[33m87ceea0[m Fixed skip button and removed radials from selections in web client.
[33mc43abd5[m Added dummy data and moved prompts and responses to web client.
[33maf6ed35[m Added new, revamped web client.  Allows for multiple automatic rounds and recognizes when a player wins the game.  Does not replace submitted responses.
[33m93def70[m Merge https://github.com/laboristolabs/cjsc
[33m16df615[m Very minor updates
[33mbc55d23[m removed old web client
[33mcd29d1d[m First round works, sort of
[33mdaebd2c[m Tuesday night commit
[33m861c8b7[m Added most of the multiplayer logic with web client.
[33m06a1917[m Integrated online multiplayer for card game.
[33mc098a26[m Finished offline protype with visuals
[33mb78b0bf[m Fixed typos
[33m438f5ea[m Forgot to save
[33m92dbda7[m Implemented (offline) untitled card game logic.
[33m75fba27[m Updated JSON
[33mb4b42f5[m Partially integrated untitled card game
[33md374f5a[m Started Florida Man
[33m1073c50[m fixed typo
[33m4108f31[m Added nsfw expansion pack, but did not integrate it.
[33mdea9de8[m Added TruthOrTRUTH
[33m7a22e9d[m 2nd first commit
[33ma38761b[m first commit
