import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { GameService } from '../services/game.service';

@Component({
  selector: 'app-home',
  templateUrl: 'home.page.html',
  styleUrls: ['home.page.scss'],
  standalone: false,
})
export class HomePage implements OnInit {
  roomCode: string = '';
  playerName: string = '';
  isJoining: boolean = false;
  errorMessage: string = '';

  constructor(
    private gameService: GameService,
    private router: Router,
    private route: ActivatedRoute
  ) {}

  ngOnInit() {
    // Check for stored player name
    const storedName = localStorage.getItem('playerName');
    if (storedName) {
      this.playerName = storedName;
    }

    // Check for room code in URL parameters
    this.route.queryParams.subscribe(params => {
      if (params['room_code']) {
        console.log('Room code from URL:', params['room_code']);
        this.roomCode = params['room_code'].toUpperCase();
      }
    });
  }

  async joinGame() {
    if (!this.roomCode || !this.playerName) {
      return;
    }

    this.isJoining = true;
    this.errorMessage = '';

    try {
      // Save player name to local storage
      localStorage.setItem('playerName', this.playerName);

      // Ensure room code is uppercase
      const formattedRoomCode = this.roomCode.toUpperCase();

      // Join the game
      const success = await this.gameService.joinGame(formattedRoomCode, this.playerName);

      if (success) {
        // Navigate to game page
        this.router.navigate(['/game']);
      } else {
        this.errorMessage = 'Invalid room code or unable to join the game.';
      }
    } catch (error) {
      console.error('Error joining game:', error);
      this.errorMessage = 'An error occurred while joining the game.';
    } finally {
      this.isJoining = false;
    }
  }
}
