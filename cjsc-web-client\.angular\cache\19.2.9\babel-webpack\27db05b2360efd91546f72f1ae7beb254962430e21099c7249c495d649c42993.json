{"ast": null, "code": "import _asyncToGenerator from \"D:/Godot_Games/cjsc/cjsc-web-client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nvar _GameService;\n// PromptService is no longer used as the Godot client handles prompts\n// import { PromptService } from './prompt.service';\nimport { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./firebase.service\";\nimport * as i2 from \"./response.service\";\nimport * as i3 from \"@angular/router\";\nexport class GameService {\n  constructor(firebaseService, responseService,\n  // promptService is no longer used as the Godot client handles prompts\n  // private promptService: PromptService,\n  router) {\n    this.firebaseService = firebaseService;\n    this.responseService = responseService;\n    this.router = router;\n    this.gameStateSubject = new BehaviorSubject(null);\n    this.gameState$ = this.gameStateSubject.asObservable();\n    this.playerResponsesSubject = new BehaviorSubject([]);\n    this.playerResponses$ = this.playerResponsesSubject.asObservable();\n    this.isJudgeSubject = new BehaviorSubject(false);\n    this.isJudge$ = this.isJudgeSubject.asObservable();\n    // Property to store player responses when they become judge\n    this.savedPlayerResponses = {};\n    console.log('GameService constructor called');\n    // Subscribe to Firebase game state changes\n    this.firebaseService.gameState$.subscribe(gameState => {\n      console.log('GameService received game state update:', gameState ? `Status: ${gameState.status}` : 'null');\n      if (gameState) {\n        console.log('Updating game state in GameService');\n        this.gameStateSubject.next(gameState);\n        this.updatePlayerRole(gameState);\n        this.handleGameStateChange(gameState);\n        // Set up periodic check for all responses submitted\n        this.setupPeriodicCheck(gameState);\n      } else {\n        console.warn('Received null game state in GameService');\n      }\n    });\n  }\n  // Set up periodic check for all responses submitted\n  setupPeriodicCheck(gameState) {\n    // Clear any existing interval\n    if (this.periodicCheckInterval) {\n      clearInterval(this.periodicCheckInterval);\n      this.periodicCheckInterval = null;\n    }\n    // Only set up the interval if we're in the right state and we're the leader\n    if (gameState.status === 'Waiting for Player Responses' && this.isLeader) {\n      console.log('Setting up periodic check for all responses submitted');\n      // Check every 5 seconds\n      this.periodicCheckInterval = setInterval(() => {\n        this.checkAllResponsesSubmitted();\n      }, 5000);\n    }\n  }\n  // Join a game with room code\n  joinGame(roomCode, playerName) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const success = yield _this.firebaseService.joinGame(roomCode, playerName);\n      return success;\n    })();\n  }\n  // Get player's responses from the game state\n  updatePlayerRole(gameState) {\n    const playerName = this.firebaseService.currentPlayerName;\n    if (!playerName) return;\n    console.log(`===== UPDATING PLAYER ROLE =====`);\n    console.log(`Checking responses for player: ${playerName}`);\n    // Check if player is the judge\n    const judgeIndex = gameState.current_judge_index || 0;\n    const isJudge = gameState.players && gameState.players[judgeIndex] === playerName;\n    const wasJudge = this.isJudgeSubject.getValue();\n    this.isJudgeSubject.next(isJudge);\n    // Store current responses before handling judge status change\n    const currentResponsesBeforeChange = this.playerResponsesSubject.getValue();\n    // Create a static property to store responses when player becomes judge\n    if (!this.savedPlayerResponses) {\n      this.savedPlayerResponses = {};\n    }\n    // Skip response handling if player is the judge, but save their responses first\n    if (isJudge) {\n      console.log(`Player ${playerName} is the judge, saving their responses for later`);\n      // Only save non-empty responses\n      if (currentResponsesBeforeChange.length > 0) {\n        console.log(`Saving ${currentResponsesBeforeChange.length} responses for player ${playerName}`);\n        this.savedPlayerResponses[playerName] = [...currentResponsesBeforeChange];\n      }\n      // Clear the current responses since the judge doesn't need them\n      this.playerResponsesSubject.next([]);\n      console.log(`===== END UPDATING PLAYER ROLE (JUDGE) =====`);\n      return;\n    }\n    // If player was previously a judge and is now a regular player, restore their saved responses\n    if (wasJudge && !isJudge && this.savedPlayerResponses && this.savedPlayerResponses[playerName]) {\n      const savedResponses = this.savedPlayerResponses[playerName];\n      if (savedResponses.length > 0) {\n        console.log(`Player ${playerName} was previously a judge, restoring their saved responses:`, savedResponses);\n        this.playerResponsesSubject.next([...savedResponses]);\n        console.log(`===== END UPDATING PLAYER ROLE (RESTORED) =====`);\n        return;\n      } else {\n        console.log(`Player ${playerName} was previously a judge but has no saved responses. Proceeding with normal response assignment.`);\n      }\n    } else if (wasJudge && !isJudge) {\n      console.log(`Player ${playerName} was previously a judge but has no saved responses in the main storage. Checking round changes.`);\n    }\n    // Get current responses\n    const currentResponses = this.playerResponsesSubject.getValue();\n    // Initial assignment of responses if we don't have any\n    if (currentResponses.length === 0 && (gameState.status === 'Ready' || gameState.status === 'Waiting for Player Responses')) {\n      // We need to assign initial responses to this player\n      console.log('Assigning initial responses to player');\n      const newResponses = this.responseService.getRandomResponses(5);\n      this.playerResponsesSubject.next(newResponses);\n      console.log('Assigned initial responses:', newResponses);\n      // No longer updating Firebase with player responses\n      // Responses are now managed locally by the web client\n      console.log('Player responses are now managed locally, skipping Firebase update');\n      console.log(`===== END UPDATING PLAYER ROLE =====`);\n      return;\n    }\n    // Removed localStorage response restoration - Responses should only exist in volatile memory\n    // For backward compatibility, check if we have responses in the game state\n    // First, check if we have direct player responses (new format)\n    if (gameState.player_responses && gameState.player_responses[playerName]) {\n      const responseTexts = gameState.player_responses[playerName];\n      console.log('Found direct player responses:', responseTexts);\n      // Check for duplicates\n      const uniqueResponses = [...new Set(responseTexts)];\n      if (uniqueResponses.length !== responseTexts.length) {\n        console.warn('Duplicate responses found, using unique responses only');\n        this.playerResponsesSubject.next(uniqueResponses);\n      } else {\n        this.playerResponsesSubject.next(responseTexts);\n      }\n      console.log(`===== END UPDATING PLAYER ROLE =====`);\n      return;\n    }\n    // Check for player_response_texts (older format)\n    if (gameState.player_response_texts && gameState.player_response_texts[playerName]) {\n      const responseTexts = gameState.player_response_texts[playerName];\n      console.log('Found player response texts:', responseTexts);\n      this.playerResponsesSubject.next(responseTexts);\n      console.log(`===== END UPDATING PLAYER ROLE =====`);\n      return;\n    }\n    // If we still don't have responses and we're in the right state, assign new ones\n    if (currentResponses.length < 5 && (gameState.status === 'Ready' || gameState.status === 'Waiting for Player Responses')) {\n      console.log(`Player has ${currentResponses.length} responses, need to add ${5 - currentResponses.length} more`);\n      // Keep existing responses and only add new ones to fill up to 5\n      if (currentResponses.length > 0) {\n        // This is the key case where we need to preserve existing responses\n        // Check if there was a round change\n        const previousRound = gameState.current_round ? gameState.current_round - 1 : 0;\n        const currentRound = gameState.current_round || 1;\n        const roundChanged = previousRound > 0 && previousRound !== currentRound;\n        if (roundChanged) {\n          console.log(`Round changed from ${previousRound} to ${currentRound}`);\n          console.log(`Preserving ${currentResponses.length} unused responses from last round`);\n        }\n        // Keep existing responses and add just enough new ones to get to 5\n        const additionalNeeded = 5 - currentResponses.length;\n        console.log(`Adding ${additionalNeeded} new responses to existing ones`);\n        const additionalResponses = this.responseService.getRandomResponses(additionalNeeded);\n        const updatedResponses = [...currentResponses, ...additionalResponses];\n        console.log('Updated response array:', updatedResponses);\n        this.playerResponsesSubject.next(updatedResponses);\n      } else {\n        // No existing responses, assign 5 new ones\n        console.log('No existing responses, assigning 5 new ones');\n        const newResponses = this.responseService.getRandomResponses(5);\n        this.playerResponsesSubject.next(newResponses);\n        console.log('Assigned initial responses:', newResponses);\n      }\n      // No longer updating Firebase with player responses\n      // Responses are now managed locally by the web client\n      console.log('Player responses are now managed locally, skipping Firebase update');\n    }\n    // No longer replenishing responses in any state other than ready/waiting for responses\n    if (currentResponses.length < 5 && !isJudge && gameState.status !== 'Ready' && gameState.status !== 'Waiting for Player Responses') {\n      console.log(`Player has only ${currentResponses.length} responses. Not replenishing in current state: ${gameState.status}`);\n    }\n    console.log(`===== END UPDATING PLAYER ROLE =====`);\n  }\n  // Handle game state changes\n  handleGameStateChange(gameState) {\n    console.log('Game state changed:', gameState.status);\n    // Removed localStorage response restoration - Responses should exist only in volatile memory\n    const currentResponses = this.playerResponsesSubject.getValue();\n    // IMPROVED PROMPT DEBUGGING: More detailed logging about the prompt\n    if (gameState.current_prompt) {\n      console.log('CURRENT PROMPT IN GAME STATE (DETAILED):', {\n        promptObject: gameState.current_prompt,\n        promptText: gameState.current_prompt.prompt,\n        hasPromptProperty: 'prompt' in gameState.current_prompt,\n        promptStringified: JSON.stringify(gameState.current_prompt)\n      });\n    } else {\n      console.log('⚠️ NO PROMPT FOUND IN GAME STATE');\n    }\n    // Log important game state information for debugging\n    if (gameState.status === 'Ready' || gameState.status === 'Waiting for Player Responses' || gameState.status === 'Waiting for Prompt') {\n      console.log('Game state details:', {\n        status: gameState.status,\n        prompt: gameState.current_prompt,\n        responseIndices: gameState.response_indices,\n        allResponses: gameState.all_responses,\n        responses: gameState.responses,\n        player_response_texts: gameState.player_response_texts,\n        player_responses: gameState.player_responses,\n        current_round: gameState.current_round\n      });\n      // Check if the current player has responses\n      const playerName = this.firebaseService.currentPlayerName;\n      if (playerName) {\n        var _gameState$player_res, _gameState$player_res2, _gameState$response_i;\n        console.log('Current player response data:', {\n          player_response_texts: (_gameState$player_res = gameState.player_response_texts) === null || _gameState$player_res === void 0 ? void 0 : _gameState$player_res[playerName],\n          player_responses: (_gameState$player_res2 = gameState.player_responses) === null || _gameState$player_res2 === void 0 ? void 0 : _gameState$player_res2[playerName],\n          response_indices: (_gameState$response_i = gameState.response_indices) === null || _gameState$response_i === void 0 ? void 0 : _gameState$response_i[playerName]\n        });\n      }\n    }\n    switch (gameState.status) {\n      case 'Ready for Judging':\n        // No longer replenishing responses in Ready for Judging state\n        const playerName = this.firebaseService.currentPlayerName;\n        if (playerName) {\n          // Check if player is the judge\n          const judgeIndex = gameState.current_judge_index || 0;\n          const isJudge = gameState.players && gameState.players[judgeIndex] === playerName;\n          // Only log for non-judges\n          if (!isJudge) {\n            // Get current responses\n            const currentResponses = this.playerResponsesSubject.getValue();\n            console.log(`Player has ${currentResponses.length} responses in Ready for Judging state`);\n            console.log('Not replenishing responses as requested');\n          } else {\n            console.log('Player is the judge');\n          }\n        }\n        break;\n      case 'Waiting for Prompt':\n        // Check if we already have a prompt in the game state\n        console.log('Waiting for Prompt state detected, checking for prompt');\n        // If we're the leader, update the current prompt to \"Incoming Prompt!\"\n        if (this.isLeader) {\n          console.log('Leader updating current prompt to \"Incoming Prompt!\"');\n          this.updateCurrentPromptToIncoming().then(() => {\n            console.log('Current prompt updated to \"Incoming Prompt!\" by leader');\n          });\n        }\n        if (gameState.current_prompt && gameState.current_prompt.prompt) {\n          // If we already have a prompt, we can display it\n          console.log('✅ Prompt found during Waiting for Prompt state:', gameState.current_prompt.prompt);\n          // Let the Godot client control the state transitions\n          // The Godot client will transition to \"Ready\" state when it's done with prompt selection\n          console.log('Waiting for Godot client to transition to Ready state');\n        } else {\n          console.log('Waiting for Godot client to select a prompt');\n          // Don't interfere with the Godot client's prompt selection process\n          // The Godot client will handle the entire flow from prompt selection to Ready state\n        }\n        break;\n      case 'Ready':\n        // When the game is ready, ensure we have a prompt, then transition to \"Waiting for Player Responses\"\n        console.log('Ready state detected, checking for prompt');\n        if (!gameState.current_prompt || !gameState.current_prompt.prompt) {\n          console.log('⚠️ WARNING: No prompt found in Ready state, this might cause display issues');\n          // Wait a moment and attempt to fetch the updated game state directly\n          if (this.isLeader) {\n            console.log('Leader is requesting a prompt verification check');\n            setTimeout(() => {\n              this.firebaseService.verifyGameData();\n            }, 1000);\n          }\n        } else {\n          console.log('✅ Prompt found in Ready state:', gameState.current_prompt.prompt);\n        }\n        // When the game is ready, the leader should transition to \"Waiting for Player Responses\"\n        if (this.isLeader) {\n          console.log('Leader is transitioning game to \"Waiting for Player Responses\"');\n          setTimeout(() => {\n            this.firebaseService.updateGameStatus('Waiting for Player Responses');\n          }, 2000); // Increased delay to ensure Firebase has processed the previous state\n        }\n        break;\n      case 'Winner Chosen':\n        // When a winner is chosen for the round, the leader should start a new round after a delay\n        if (this.isLeader) {\n          console.log('Leader will start a new round in 5 seconds...');\n          // Store the timestamp when the winner was chosen\n          const winnerChosenTime = Date.now();\n          localStorage.setItem('winnerChosenTime', winnerChosenTime.toString());\n          // We'll let the UI handle the countdown and trigger the next round\n          // This is now handled in the game.page.ts file\n          console.log('UI will handle countdown and trigger next round');\n        }\n        // IMPORTANT: For non-judges, ensure we preserve the current responses\n        if (!this.isJudgeSubject.getValue()) {\n          const currentResponses = this.playerResponsesSubject.getValue();\n          if (currentResponses.length > 0) {\n            console.log('Preserving player responses at Winner Chosen state:', currentResponses);\n            // Save these responses in the savedPlayerResponses map for the current player\n            // This provides another layer of backup for preserving responses\n            const playerName = this.firebaseService.currentPlayerName;\n            if (playerName) {\n              this.savedPlayerResponses[playerName] = [...currentResponses];\n              console.log(`Backed up ${currentResponses.length} responses for player ${playerName} at Winner Chosen state`);\n            }\n          }\n        }\n        break;\n      case 'Game Over':\n        // Game is over, someone reached 5 points\n        console.log('Game over! Winner:', gameState.game_winner);\n        // No automatic action needed - waiting for leader to choose what to do next\n        break;\n      case 'Closed':\n        // Game is closed, return to home\n        this.firebaseService.clearPlayerInfo();\n        this.router.navigate(['/home']);\n        break;\n    }\n    // Update player responses only if not the judge and not in the middle of selection\n    if (gameState.status === 'Waiting for Player Responses') {\n      console.log('Game status is \"Waiting for Player Responses\", checking if player needs responses');\n      const playerName = this.firebaseService.currentPlayerName;\n      if (playerName) {\n        var _gameState$players;\n        console.log(`Current player: ${playerName}`);\n        // Check if player is the judge\n        const judgeIndex = gameState.current_judge_index || 0;\n        const isJudge = gameState.players && gameState.players[judgeIndex] === playerName;\n        console.log(`Is player the judge? ${isJudge} (judge index: ${judgeIndex}, judge: ${(_gameState$players = gameState.players) === null || _gameState$players === void 0 ? void 0 : _gameState$players[judgeIndex]})`);\n        // Log current response state\n        const currentResponses = this.playerResponsesSubject.getValue();\n        console.log(`Current local responses: ${currentResponses.length > 0 ? currentResponses.length : 'none'}`);\n        if (currentResponses.length > 0) {\n          console.log('Sample responses:', currentResponses.slice(0, 2));\n        }\n        // Only update responses for non-judges\n        if (!isJudge) {\n          var _gameState$player_res3, _gameState$player_res4;\n          // Get player responses from game state (check both formats for backward compatibility)\n          const playerResponseTexts = ((_gameState$player_res3 = gameState.player_responses) === null || _gameState$player_res3 === void 0 ? void 0 : _gameState$player_res3[playerName]) || ((_gameState$player_res4 = gameState.player_response_texts) === null || _gameState$player_res4 === void 0 ? void 0 : _gameState$player_res4[playerName]) || [];\n          const currentResponses = this.playerResponsesSubject.getValue();\n          // IMPORTANT: We want to preserve the existing responses\n          // that the player has accumulated through gameplay\n          // Only update from Firebase if:\n          // 1. There are actually responses in Firebase, AND\n          // 2. The current local responses are empty (never had any responses)\n          if (playerResponseTexts.length > 0 && currentResponses.length === 0) {\n            console.log('Initializing responses from Firebase (empty hand):', playerResponseTexts);\n            this.playerResponsesSubject.next(playerResponseTexts);\n          } else if (currentResponses.length === 0) {\n            // If we don't have any responses locally or in Firebase, assign new ones\n            // This is only for initial setup, not for replenishment\n            console.log('No responses found locally or in Firebase, assigning initial ones');\n            const newResponses = this.responseService.getRandomResponses(5);\n            this.playerResponsesSubject.next(newResponses);\n            console.log('Assigned initial responses:', newResponses);\n            // No longer saving to localStorage - keeping responses only in volatile memory\n          } else if (currentResponses.length < 5) {\n            // We have some responses but need more to reach 5\n            console.log(`Only have ${currentResponses.length} responses, topping up to 5`);\n            const additionalNeeded = 5 - currentResponses.length;\n            const additionalResponses = this.responseService.getRandomResponses(additionalNeeded);\n            const updatedResponses = [...currentResponses, ...additionalResponses];\n            console.log('Updated response array:', updatedResponses);\n            this.playerResponsesSubject.next(updatedResponses);\n          } else {\n            // We have enough responses, preserve them\n            console.log('Preserving existing responses:', currentResponses);\n          }\n        }\n      }\n    }\n  }\n  // Submit a response\n  submitResponse(responseText, responseIndex) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      console.log(`===== SUBMITTING RESPONSE =====`);\n      console.log(`Original response array:`, _this2.playerResponsesSubject.getValue());\n      console.log(`Submitting response: \"${responseText}\" at index ${responseIndex}`);\n      // Get the current responses\n      const currentResponses = _this2.playerResponsesSubject.getValue();\n      let updatedResponses;\n      // Check if this is a custom response (index = -1) or a card response\n      if (responseIndex === -1) {\n        // For custom responses, don't remove any cards from the player's hand\n        console.log('Custom response submitted, keeping all existing responses');\n        updatedResponses = [...currentResponses];\n      } else {\n        // For card responses, remove the selected card and replace it with a new one\n        // Create a new array without the selected response\n        console.log(`Removing response at index ${responseIndex}: \"${currentResponses[responseIndex]}\"`);\n        updatedResponses = currentResponses.filter((_, index) => index !== responseIndex);\n        console.log(`Updated response array after removal:`, updatedResponses);\n        // Get a new random response to replace the used one\n        const newResponse = _this2.responseService.getRandomResponses(1)[0];\n        if (newResponse) {\n          // Make sure the new response doesn't already exist in our hand\n          while (updatedResponses.includes(newResponse)) {\n            console.log(`Response \"${newResponse}\" already in hand, getting a different one`);\n            const anotherResponse = _this2.responseService.getRandomResponses(1)[0];\n            if (anotherResponse && !updatedResponses.includes(anotherResponse)) {\n              console.log(`Using alternative response: \"${anotherResponse}\"`);\n              updatedResponses.push(anotherResponse);\n              console.log(`Added new response: \"${anotherResponse}\"`);\n              break;\n            }\n          }\n          // If we didn't add a response yet, add the original one\n          if (updatedResponses.length < currentResponses.length) {\n            updatedResponses.push(newResponse);\n            console.log(`Added new response: \"${newResponse}\"`);\n          }\n        } else {\n          console.warn('Failed to get a new random response!');\n        }\n        // Log all submissions and updates to track what's happening\n        console.log(\"DEBUG - Card submission flow:\");\n        console.log(\"1. Original responses:\", [...currentResponses]);\n        console.log(\"2. Card being submitted:\", responseText);\n        console.log(\"3. Index of submitted card:\", responseIndex);\n        console.log(\"4. Updated responses after removal:\", [...updatedResponses]);\n      }\n      // Update the subject with the new array - THIS IS CRITICAL\n      // The playerResponsesSubject needs to be updated immediately\n      console.log(`Updating playerResponsesSubject with new array of length ${updatedResponses.length}`);\n      _this2.playerResponsesSubject.next(updatedResponses);\n      console.log(`Final updated response array:`, updatedResponses);\n      try {\n        // Submit to Firebase and wait for the operation to complete\n        yield _this2.firebaseService.submitResponse(responseText, responseIndex);\n        console.log(`Response submitted to Firebase successfully`);\n        // After submitting, manually check if all players have submitted responses\n        // This is a backup in case the automatic check in the Firebase service fails\n        setTimeout(() => {\n          _this2.checkAllResponsesSubmitted();\n        }, 1000); // Wait 1 second to ensure Firebase has updated\n        console.log(`===== END SUBMITTING RESPONSE =====`);\n      } catch (error) {\n        // If there's an error, revert the local array\n        _this2.playerResponsesSubject.next(currentResponses);\n        console.error(`Error submitting response:`, error);\n        console.log(`===== END SUBMITTING RESPONSE (WITH ERROR) =====`);\n        throw error;\n      }\n    })();\n  }\n  // Manually check if all players have submitted responses\n  checkAllResponsesSubmitted() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      console.log(`===== MANUAL CHECK FOR ALL RESPONSES SUBMITTED =====`);\n      // Get the current game state\n      const gameState = _this3.gameStateSubject.getValue();\n      if (!gameState) {\n        console.log(`No game state available, aborting check`);\n        console.log(`===== END MANUAL CHECK FOR ALL RESPONSES SUBMITTED =====`);\n        return;\n      }\n      // Only proceed if we're in the right state\n      if (gameState.status !== 'Waiting for Player Responses') {\n        console.log(`Game status is ${gameState.status}, not checking responses`);\n        console.log(`===== END MANUAL CHECK FOR ALL RESPONSES SUBMITTED =====`);\n        return;\n      }\n      const players = gameState.players || [];\n      const responses = gameState.submitted_responses || [];\n      // Find the judge\n      const judgeIndex = gameState.current_judge_index || 0;\n      const judge = players[judgeIndex];\n      // Count non-judge players\n      const nonJudgePlayers = players.filter(p => p !== judge);\n      console.log('Manual response check:', {\n        totalPlayers: players.length,\n        nonJudgePlayers: nonJudgePlayers.length,\n        submittedResponses: responses.length,\n        judge: judge\n      });\n      // If there are no non-judge players, auto-advance\n      if (nonJudgePlayers.length === 0) {\n        console.log('No non-judge players, auto-advancing to Ready for Judging');\n        if (_this3.isLeader) {\n          yield _this3.firebaseService.updateGameStatus('Ready for Judging');\n        }\n        console.log(`===== END MANUAL CHECK FOR ALL RESPONSES SUBMITTED =====`);\n        return;\n      }\n      // Check if each player has submitted a response\n      const playerResponseCount = {};\n      // Initialize counts to zero\n      nonJudgePlayers.forEach(player => {\n        playerResponseCount[player] = 0;\n      });\n      // Count responses for each player\n      responses.forEach(response => {\n        if (playerResponseCount[response.player_name] !== undefined) {\n          playerResponseCount[response.player_name]++;\n        }\n      });\n      // Check if all players have at least one submission\n      const allPlayersHaveAtLeastOneSubmission = Object.values(playerResponseCount).every(count => count > 0);\n      console.log('Player response counts:', playerResponseCount);\n      console.log('All players have at least one submission:', allPlayersHaveAtLeastOneSubmission);\n      // If all players have submitted at least one response and we're the leader, update the game status\n      if (allPlayersHaveAtLeastOneSubmission && _this3.isLeader) {\n        console.log('All players have submitted at least one response, changing status to Ready for Judging');\n        yield _this3.firebaseService.updateGameStatus('Ready for Judging');\n      } else if (allPlayersHaveAtLeastOneSubmission) {\n        console.log('All players have submitted at least one response, but not the leader');\n      } else {\n        console.log('Waiting for more players to submit responses');\n      }\n      console.log(`===== END MANUAL CHECK FOR ALL RESPONSES SUBMITTED =====`);\n    })();\n  }\n  // Judge selects a winner\n  selectWinner(responseIndex) {\n    return this.firebaseService.selectWinner(responseIndex);\n  }\n  // Skip judging (can be called by any player)\n  skipJudging() {\n    return this.firebaseService.skipJudging();\n  }\n  // Leader starts a new game\n  startNewGame() {\n    // Get the current responses but DO NOT preserve all of them\n    // We only want to preserve the responses that weren't submitted\n    const currentResponses = this.playerResponsesSubject.getValue();\n    const hasResponses = currentResponses.length > 0;\n    // Don't automatically restore all responses - instead, we'll let the \n    // normal response handling in updatePlayerRole handle this\n    if (hasResponses) {\n      console.log('Current responses before new round:', currentResponses);\n      console.log('These will NOT be automatically restored - only non-submitted responses should persist');\n    }\n    return this.firebaseService.startNewGame();\n  }\n  // Leader resets the game\n  resetGame() {\n    // We intentionally don't preserve responses when resetting the game\n    // This gives players a fresh hand of cards when starting a new game\n    console.log('Resetting game, player will receive new responses');\n    return this.firebaseService.resetGame();\n  }\n  // Leader closes the game\n  closeGame() {\n    // Clear any periodic check interval\n    if (this.periodicCheckInterval) {\n      clearInterval(this.periodicCheckInterval);\n      this.periodicCheckInterval = null;\n    }\n    return this.firebaseService.closeGame();\n  }\n  // Player leaves the game\n  leaveGame() {\n    // Clear any periodic check interval\n    if (this.periodicCheckInterval) {\n      clearInterval(this.periodicCheckInterval);\n      this.periodicCheckInterval = null;\n    }\n    return this.firebaseService.removePlayerFromGame();\n  }\n  // Update game status\n  updateGameStatus(status) {\n    console.log(`Updating game status to: ${status}`);\n    return this.firebaseService.updateGameStatus(status);\n  }\n  // Update current prompt to \"Incoming Prompt!\"\n  updateCurrentPromptToIncoming() {\n    console.log('Updating current prompt to \"Incoming Prompt!\"');\n    return this.firebaseService.updateCurrentPromptToIncoming();\n  }\n  // Check if current player is the leader\n  get isLeader() {\n    return this.firebaseService.isGameLeader;\n  }\n  // Get current player name\n  get playerName() {\n    return this.firebaseService.currentPlayerName;\n  }\n  // Get current player responses (volatile memory only)\n  getCurrentResponses() {\n    return this.playerResponsesSubject.getValue();\n  }\n  // Helper method to compare arrays\n  areArraysEqual(arr1, arr2) {\n    if (arr1.length !== arr2.length) return false;\n    for (let i = 0; i < arr1.length; i++) {\n      if (arr1[i] !== arr2[i]) return false;\n    }\n    return true;\n  }\n}\n_GameService = GameService;\n_GameService.ɵfac = function GameService_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _GameService)(i0.ɵɵinject(i1.FirebaseService), i0.ɵɵinject(i2.ResponseService), i0.ɵɵinject(i3.Router));\n};\n_GameService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n  token: _GameService,\n  factory: _GameService.ɵfac,\n  providedIn: 'root'\n});", "map": {"version": 3, "names": ["BehaviorSubject", "GameService", "constructor", "firebaseService", "responseService", "router", "gameStateSubject", "gameState$", "asObservable", "playerResponsesSubject", "playerResponses$", "isJudgeSubject", "isJudge$", "savedPlayerResponses", "console", "log", "subscribe", "gameState", "status", "next", "updatePlayerRole", "handleGameStateChange", "setupPeriodicCheck", "warn", "periodicCheckInterval", "clearInterval", "<PERSON><PERSON><PERSON><PERSON>", "setInterval", "checkAllResponsesSubmitted", "joinGame", "roomCode", "<PERSON><PERSON><PERSON>", "_this", "_asyncToGenerator", "success", "currentPlayerName", "judge<PERSON>ndex", "current_judge_index", "isJudge", "players", "wasJudge", "getValue", "currentResponsesBeforeChange", "length", "savedResponses", "currentResponses", "newResponses", "getRandomResponses", "player_responses", "responseTexts", "uniqueResponses", "Set", "player_response_texts", "previousRound", "current_round", "currentRound", "roundChanged", "additionalNeeded", "additionalResponses", "updatedResponses", "current_prompt", "promptObject", "promptText", "prompt", "hasPromptProperty", "promptStringified", "JSON", "stringify", "responseIndices", "response_indices", "allResponses", "all_responses", "responses", "_gameState$player_res", "_gameState$player_res2", "_gameState$response_i", "updateCurrentPromptToIncoming", "then", "setTimeout", "verifyGameData", "updateGameStatus", "winnerChosenTime", "Date", "now", "localStorage", "setItem", "toString", "game_winner", "clearPlayerInfo", "navigate", "_gameState$players", "slice", "_gameState$player_res3", "_gameState$player_res4", "playerResponseTexts", "submitResponse", "responseText", "responseIndex", "_this2", "filter", "_", "index", "newResponse", "includes", "anotherResponse", "push", "error", "_this3", "submitted_responses", "judge", "nonJudgePlayers", "p", "totalPlayers", "submittedResponses", "playerResponseCount", "for<PERSON>ach", "player", "response", "player_name", "undefined", "allPlayersHaveAtLeastOneSubmission", "Object", "values", "every", "count", "<PERSON><PERSON><PERSON><PERSON>", "skipJudging", "startNewGame", "hasResponses", "resetGame", "closeGame", "leaveGame", "removePlayerFromGame", "isGameLeader", "getCurrentResponses", "areArraysEqual", "arr1", "arr2", "i", "i0", "ɵɵinject", "i1", "FirebaseService", "i2", "ResponseService", "i3", "Router", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Godot_Games\\cjsc\\cjsc-web-client\\src\\app\\services\\game.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { FirebaseService } from './firebase.service';\nimport { ResponseService } from './response.service';\n// PromptService is no longer used as the Godot client handles prompts\n// import { PromptService } from './prompt.service';\nimport { BehaviorSubject } from 'rxjs';\nimport { Router } from '@angular/router';\n\nexport interface GameState {\n  status: string;\n  players: string[];\n  player_scores: { [key: string]: number };\n  submitted_responses: { text: string; player_name: string }[];\n  current_prompt: { prompt: string };\n  current_round?: number;\n  current_judge_index?: number;\n  room_code?: string;\n  winner?: string;\n  winning_response?: string;\n  game_winner?: string; // Overall game winner (player who reached 5 points)\n  response_indices?: { [key: string]: number[] };\n  all_responses?: string[];\n  responses?: { response: string }[];\n  // @deprecated - No longer used for storing player responses, web client manages responses locally\n  player_response_texts?: { [key: string]: string[] };\n  // @deprecated - No longer used for storing player responses, web client manages responses locally\n  player_responses?: { [key: string]: string[] };\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class GameService {\n  private gameStateSubject = new BehaviorSubject<GameState | null>(null);\n  public gameState$ = this.gameStateSubject.asObservable();\n\n  private playerResponsesSubject = new BehaviorSubject<string[]>([]);\n  public playerResponses$ = this.playerResponsesSubject.asObservable();\n\n  private isJudgeSubject = new BehaviorSubject<boolean>(false);\n  public isJudge$ = this.isJudgeSubject.asObservable();\n\n  private periodicCheckInterval: any;\n  \n  // Property to store player responses when they become judge\n  private savedPlayerResponses: { [key: string]: string[] } = {};\n\n  constructor(\n    private firebaseService: FirebaseService,\n    private responseService: ResponseService,\n    // promptService is no longer used as the Godot client handles prompts\n    // private promptService: PromptService,\n    private router: Router\n  ) {\n    console.log('GameService constructor called');\n\n    // Subscribe to Firebase game state changes\n    this.firebaseService.gameState$.subscribe(gameState => {\n      console.log('GameService received game state update:', gameState ? `Status: ${gameState.status}` : 'null');\n\n      if (gameState) {\n        console.log('Updating game state in GameService');\n        this.gameStateSubject.next(gameState);\n        this.updatePlayerRole(gameState);\n        this.handleGameStateChange(gameState);\n\n        // Set up periodic check for all responses submitted\n        this.setupPeriodicCheck(gameState);\n      } else {\n        console.warn('Received null game state in GameService');\n      }\n    });\n  }\n\n  // Set up periodic check for all responses submitted\n  private setupPeriodicCheck(gameState: GameState): void {\n    // Clear any existing interval\n    if (this.periodicCheckInterval) {\n      clearInterval(this.periodicCheckInterval);\n      this.periodicCheckInterval = null;\n    }\n\n    // Only set up the interval if we're in the right state and we're the leader\n    if (gameState.status === 'Waiting for Player Responses' && this.isLeader) {\n      console.log('Setting up periodic check for all responses submitted');\n\n      // Check every 5 seconds\n      this.periodicCheckInterval = setInterval(() => {\n        this.checkAllResponsesSubmitted();\n      }, 5000);\n    }\n  }\n\n  // Join a game with room code\n  async joinGame(roomCode: string, playerName: string): Promise<boolean> {\n    const success = await this.firebaseService.joinGame(roomCode, playerName);\n    return success;\n  }\n\n  // Get player's responses from the game state\n  private updatePlayerRole(gameState: GameState): void {\n    const playerName = this.firebaseService.currentPlayerName;\n    if (!playerName) return;\n\n    console.log(`===== UPDATING PLAYER ROLE =====`);\n    console.log(`Checking responses for player: ${playerName}`);\n\n    // Check if player is the judge\n    const judgeIndex = gameState.current_judge_index || 0;\n    const isJudge = gameState.players && gameState.players[judgeIndex] === playerName;\n    const wasJudge = this.isJudgeSubject.getValue();\n    this.isJudgeSubject.next(isJudge);\n\n    // Store current responses before handling judge status change\n    const currentResponsesBeforeChange = this.playerResponsesSubject.getValue();\n    \n    // Create a static property to store responses when player becomes judge\n    if (!this.savedPlayerResponses) {\n      this.savedPlayerResponses = {};\n    }\n\n    // Skip response handling if player is the judge, but save their responses first\n    if (isJudge) {\n      console.log(`Player ${playerName} is the judge, saving their responses for later`);\n      \n      // Only save non-empty responses\n      if (currentResponsesBeforeChange.length > 0) {\n        console.log(`Saving ${currentResponsesBeforeChange.length} responses for player ${playerName}`);\n        this.savedPlayerResponses[playerName] = [...currentResponsesBeforeChange];\n      }\n      \n      // Clear the current responses since the judge doesn't need them\n      this.playerResponsesSubject.next([]);\n      console.log(`===== END UPDATING PLAYER ROLE (JUDGE) =====`);\n      return;\n    }\n    \n    // If player was previously a judge and is now a regular player, restore their saved responses\n    if (wasJudge && !isJudge && this.savedPlayerResponses && this.savedPlayerResponses[playerName]) {\n      const savedResponses = this.savedPlayerResponses[playerName];\n      if (savedResponses.length > 0) {\n        console.log(`Player ${playerName} was previously a judge, restoring their saved responses:`, savedResponses);\n        this.playerResponsesSubject.next([...savedResponses]);\n        console.log(`===== END UPDATING PLAYER ROLE (RESTORED) =====`);\n        return;\n      } else {\n        console.log(`Player ${playerName} was previously a judge but has no saved responses. Proceeding with normal response assignment.`);\n      }\n    } else if (wasJudge && !isJudge) {\n      console.log(`Player ${playerName} was previously a judge but has no saved responses in the main storage. Checking round changes.`);\n    }\n\n    // Get current responses\n    const currentResponses = this.playerResponsesSubject.getValue();\n\n    // Initial assignment of responses if we don't have any\n    if (currentResponses.length === 0 && (gameState.status === 'Ready' || gameState.status === 'Waiting for Player Responses')) {\n      // We need to assign initial responses to this player\n      console.log('Assigning initial responses to player');\n      const newResponses = this.responseService.getRandomResponses(5);\n      this.playerResponsesSubject.next(newResponses);\n      console.log('Assigned initial responses:', newResponses);\n\n      // No longer updating Firebase with player responses\n      // Responses are now managed locally by the web client\n      console.log('Player responses are now managed locally, skipping Firebase update');\n\n      console.log(`===== END UPDATING PLAYER ROLE =====`);\n      return;\n    }\n\n    // Removed localStorage response restoration - Responses should only exist in volatile memory\n\n    // For backward compatibility, check if we have responses in the game state\n    // First, check if we have direct player responses (new format)\n    if (gameState.player_responses && gameState.player_responses[playerName]) {\n      const responseTexts = gameState.player_responses[playerName];\n      console.log('Found direct player responses:', responseTexts);\n\n      // Check for duplicates\n      const uniqueResponses = [...new Set(responseTexts)];\n      if (uniqueResponses.length !== responseTexts.length) {\n        console.warn('Duplicate responses found, using unique responses only');\n        this.playerResponsesSubject.next(uniqueResponses);\n      } else {\n        this.playerResponsesSubject.next(responseTexts);\n      }\n      console.log(`===== END UPDATING PLAYER ROLE =====`);\n      return;\n    }\n\n    // Check for player_response_texts (older format)\n    if (gameState.player_response_texts && gameState.player_response_texts[playerName]) {\n      const responseTexts = gameState.player_response_texts[playerName];\n      console.log('Found player response texts:', responseTexts);\n      this.playerResponsesSubject.next(responseTexts);\n      console.log(`===== END UPDATING PLAYER ROLE =====`);\n      return;\n    }\n\n    // If we still don't have responses and we're in the right state, assign new ones\n    if (currentResponses.length < 5 && (gameState.status === 'Ready' || gameState.status === 'Waiting for Player Responses')) {\n      console.log(`Player has ${currentResponses.length} responses, need to add ${5 - currentResponses.length} more`);\n      \n      // Keep existing responses and only add new ones to fill up to 5\n      if (currentResponses.length > 0) {\n        // This is the key case where we need to preserve existing responses\n        // Check if there was a round change\n        const previousRound = gameState.current_round ? gameState.current_round - 1 : 0;\n        const currentRound = gameState.current_round || 1;\n        const roundChanged = previousRound > 0 && previousRound !== currentRound;\n        \n        if (roundChanged) {\n          console.log(`Round changed from ${previousRound} to ${currentRound}`);\n          console.log(`Preserving ${currentResponses.length} unused responses from last round`);\n        }\n        \n        // Keep existing responses and add just enough new ones to get to 5\n        const additionalNeeded = 5 - currentResponses.length;\n        console.log(`Adding ${additionalNeeded} new responses to existing ones`);\n        \n        const additionalResponses = this.responseService.getRandomResponses(additionalNeeded);\n        const updatedResponses = [...currentResponses, ...additionalResponses];\n        \n        console.log('Updated response array:', updatedResponses);\n        this.playerResponsesSubject.next(updatedResponses);\n      } else {\n        // No existing responses, assign 5 new ones\n        console.log('No existing responses, assigning 5 new ones');\n        const newResponses = this.responseService.getRandomResponses(5);\n        this.playerResponsesSubject.next(newResponses);\n        console.log('Assigned initial responses:', newResponses);\n      }\n\n      // No longer updating Firebase with player responses\n      // Responses are now managed locally by the web client\n      console.log('Player responses are now managed locally, skipping Firebase update');\n    }\n\n    // No longer replenishing responses in any state other than ready/waiting for responses\n    if (currentResponses.length < 5 && !isJudge && \n        gameState.status !== 'Ready' && gameState.status !== 'Waiting for Player Responses') {\n      console.log(`Player has only ${currentResponses.length} responses. Not replenishing in current state: ${gameState.status}`);\n    }\n\n    console.log(`===== END UPDATING PLAYER ROLE =====`);\n  }\n\n  // Handle game state changes\n  private handleGameStateChange(gameState: GameState): void {\n    console.log('Game state changed:', gameState.status);\n\n    // Removed localStorage response restoration - Responses should exist only in volatile memory\n    const currentResponses = this.playerResponsesSubject.getValue();\n\n    // IMPROVED PROMPT DEBUGGING: More detailed logging about the prompt\n    if (gameState.current_prompt) {\n      console.log('CURRENT PROMPT IN GAME STATE (DETAILED):', {\n        promptObject: gameState.current_prompt,\n        promptText: gameState.current_prompt.prompt,\n        hasPromptProperty: 'prompt' in gameState.current_prompt,\n        promptStringified: JSON.stringify(gameState.current_prompt)\n      });\n    } else {\n      console.log('⚠️ NO PROMPT FOUND IN GAME STATE');\n    }\n\n    // Log important game state information for debugging\n    if (gameState.status === 'Ready' || gameState.status === 'Waiting for Player Responses' || gameState.status === 'Waiting for Prompt') {\n      console.log('Game state details:', {\n        status: gameState.status,\n        prompt: gameState.current_prompt,\n        responseIndices: gameState.response_indices,\n        allResponses: gameState.all_responses,\n        responses: gameState.responses,\n        player_response_texts: gameState.player_response_texts,\n        player_responses: gameState.player_responses,\n        current_round: gameState.current_round\n      });\n\n      // Check if the current player has responses\n      const playerName = this.firebaseService.currentPlayerName;\n      if (playerName) {\n        console.log('Current player response data:', {\n          player_response_texts: gameState.player_response_texts?.[playerName],\n          player_responses: gameState.player_responses?.[playerName],\n          response_indices: gameState.response_indices?.[playerName]\n        });\n      }\n    }\n\n    switch (gameState.status) {\n      case 'Ready for Judging':\n        // No longer replenishing responses in Ready for Judging state\n        const playerName = this.firebaseService.currentPlayerName;\n        if (playerName) {\n          // Check if player is the judge\n          const judgeIndex = gameState.current_judge_index || 0;\n          const isJudge = gameState.players && gameState.players[judgeIndex] === playerName;\n\n          // Only log for non-judges\n          if (!isJudge) {\n            // Get current responses\n            const currentResponses = this.playerResponsesSubject.getValue();\n            console.log(`Player has ${currentResponses.length} responses in Ready for Judging state`);\n            console.log('Not replenishing responses as requested');\n          } else {\n            console.log('Player is the judge');\n          }\n        }\n        break;\n\n      case 'Waiting for Prompt':\n        // Check if we already have a prompt in the game state\n        console.log('Waiting for Prompt state detected, checking for prompt');\n\n        // If we're the leader, update the current prompt to \"Incoming Prompt!\"\n        if (this.isLeader) {\n          console.log('Leader updating current prompt to \"Incoming Prompt!\"');\n          this.updateCurrentPromptToIncoming().then(() => {\n            console.log('Current prompt updated to \"Incoming Prompt!\" by leader');\n          });\n        }\n\n        if (gameState.current_prompt && gameState.current_prompt.prompt) {\n          // If we already have a prompt, we can display it\n          console.log('✅ Prompt found during Waiting for Prompt state:', gameState.current_prompt.prompt);\n\n          // Let the Godot client control the state transitions\n          // The Godot client will transition to \"Ready\" state when it's done with prompt selection\n          console.log('Waiting for Godot client to transition to Ready state');\n        } else {\n          console.log('Waiting for Godot client to select a prompt');\n\n          // Don't interfere with the Godot client's prompt selection process\n          // The Godot client will handle the entire flow from prompt selection to Ready state\n        }\n        break;\n\n      case 'Ready':\n        // When the game is ready, ensure we have a prompt, then transition to \"Waiting for Player Responses\"\n        console.log('Ready state detected, checking for prompt');\n\n        if (!gameState.current_prompt || !gameState.current_prompt.prompt) {\n          console.log('⚠️ WARNING: No prompt found in Ready state, this might cause display issues');\n\n          // Wait a moment and attempt to fetch the updated game state directly\n          if (this.isLeader) {\n            console.log('Leader is requesting a prompt verification check');\n            setTimeout(() => {\n              this.firebaseService.verifyGameData();\n            }, 1000);\n          }\n        } else {\n          console.log('✅ Prompt found in Ready state:', gameState.current_prompt.prompt);\n        }\n\n        // When the game is ready, the leader should transition to \"Waiting for Player Responses\"\n        if (this.isLeader) {\n          console.log('Leader is transitioning game to \"Waiting for Player Responses\"');\n          setTimeout(() => {\n            this.firebaseService.updateGameStatus('Waiting for Player Responses');\n          }, 2000); // Increased delay to ensure Firebase has processed the previous state\n        }\n        break;\n\n      case 'Winner Chosen':\n        // When a winner is chosen for the round, the leader should start a new round after a delay\n        if (this.isLeader) {\n          console.log('Leader will start a new round in 5 seconds...');\n\n          // Store the timestamp when the winner was chosen\n          const winnerChosenTime = Date.now();\n          localStorage.setItem('winnerChosenTime', winnerChosenTime.toString());\n\n          // We'll let the UI handle the countdown and trigger the next round\n          // This is now handled in the game.page.ts file\n          console.log('UI will handle countdown and trigger next round');\n        }\n        \n        // IMPORTANT: For non-judges, ensure we preserve the current responses\n        if (!this.isJudgeSubject.getValue()) {\n          const currentResponses = this.playerResponsesSubject.getValue();\n          if (currentResponses.length > 0) {\n            console.log('Preserving player responses at Winner Chosen state:', currentResponses);\n            \n            // Save these responses in the savedPlayerResponses map for the current player\n            // This provides another layer of backup for preserving responses\n            const playerName = this.firebaseService.currentPlayerName;\n            if (playerName) {\n              this.savedPlayerResponses[playerName] = [...currentResponses];\n              console.log(`Backed up ${currentResponses.length} responses for player ${playerName} at Winner Chosen state`);\n            }\n          }\n        }\n        break;\n\n      case 'Game Over':\n        // Game is over, someone reached 5 points\n        console.log('Game over! Winner:', gameState.game_winner);\n        // No automatic action needed - waiting for leader to choose what to do next\n        break;\n\n      case 'Closed':\n        // Game is closed, return to home\n        this.firebaseService.clearPlayerInfo();\n        this.router.navigate(['/home']);\n        break;\n    }\n\n    // Update player responses only if not the judge and not in the middle of selection\n    if (gameState.status === 'Waiting for Player Responses') {\n      console.log('Game status is \"Waiting for Player Responses\", checking if player needs responses');\n\n      const playerName = this.firebaseService.currentPlayerName;\n      if (playerName) {\n        console.log(`Current player: ${playerName}`);\n\n        // Check if player is the judge\n        const judgeIndex = gameState.current_judge_index || 0;\n        const isJudge = gameState.players && gameState.players[judgeIndex] === playerName;\n        console.log(`Is player the judge? ${isJudge} (judge index: ${judgeIndex}, judge: ${gameState.players?.[judgeIndex]})`);\n\n        // Log current response state\n        const currentResponses = this.playerResponsesSubject.getValue();\n        console.log(`Current local responses: ${currentResponses.length > 0 ? currentResponses.length : 'none'}`);\n        if (currentResponses.length > 0) {\n          console.log('Sample responses:', currentResponses.slice(0, 2));\n        }\n\n        // Only update responses for non-judges\n        if (!isJudge) {\n          // Get player responses from game state (check both formats for backward compatibility)\n          const playerResponseTexts = gameState.player_responses?.[playerName] || gameState.player_response_texts?.[playerName] || [];\n          const currentResponses = this.playerResponsesSubject.getValue();\n\n          // IMPORTANT: We want to preserve the existing responses\n          // that the player has accumulated through gameplay\n          // Only update from Firebase if:\n          // 1. There are actually responses in Firebase, AND\n          // 2. The current local responses are empty (never had any responses)\n          if (playerResponseTexts.length > 0 && currentResponses.length === 0) {\n            console.log('Initializing responses from Firebase (empty hand):', playerResponseTexts);\n            this.playerResponsesSubject.next(playerResponseTexts);\n          } else if (currentResponses.length === 0) {\n            // If we don't have any responses locally or in Firebase, assign new ones\n            // This is only for initial setup, not for replenishment\n            console.log('No responses found locally or in Firebase, assigning initial ones');\n            const newResponses = this.responseService.getRandomResponses(5);\n            this.playerResponsesSubject.next(newResponses);\n            console.log('Assigned initial responses:', newResponses);\n\n            // No longer saving to localStorage - keeping responses only in volatile memory\n          } else if (currentResponses.length < 5) {\n            // We have some responses but need more to reach 5\n            console.log(`Only have ${currentResponses.length} responses, topping up to 5`);\n            const additionalNeeded = 5 - currentResponses.length;\n            const additionalResponses = this.responseService.getRandomResponses(additionalNeeded);\n            const updatedResponses = [...currentResponses, ...additionalResponses];\n            \n            console.log('Updated response array:', updatedResponses);\n            this.playerResponsesSubject.next(updatedResponses);\n          } else {\n            // We have enough responses, preserve them\n            console.log('Preserving existing responses:', currentResponses);\n          }\n        }\n      }\n    }\n  }\n\n  // Submit a response\n  async submitResponse(responseText: string, responseIndex: number): Promise<void> {\n    console.log(`===== SUBMITTING RESPONSE =====`);\n    console.log(`Original response array:`, this.playerResponsesSubject.getValue());\n    console.log(`Submitting response: \"${responseText}\" at index ${responseIndex}`);\n\n    // Get the current responses\n    const currentResponses = this.playerResponsesSubject.getValue();\n    let updatedResponses: string[];\n\n    // Check if this is a custom response (index = -1) or a card response\n    if (responseIndex === -1) {\n      // For custom responses, don't remove any cards from the player's hand\n      console.log('Custom response submitted, keeping all existing responses');\n      updatedResponses = [...currentResponses];\n    } else {\n      // For card responses, remove the selected card and replace it with a new one\n      // Create a new array without the selected response\n      console.log(`Removing response at index ${responseIndex}: \"${currentResponses[responseIndex]}\"`);\n      updatedResponses = currentResponses.filter((_, index) => index !== responseIndex);\n      console.log(`Updated response array after removal:`, updatedResponses);\n      \n      // Get a new random response to replace the used one\n      const newResponse = this.responseService.getRandomResponses(1)[0];\n      if (newResponse) {\n        // Make sure the new response doesn't already exist in our hand\n        while (updatedResponses.includes(newResponse)) {\n          console.log(`Response \"${newResponse}\" already in hand, getting a different one`);\n          const anotherResponse = this.responseService.getRandomResponses(1)[0];\n          if (anotherResponse && !updatedResponses.includes(anotherResponse)) {\n            console.log(`Using alternative response: \"${anotherResponse}\"`);\n            updatedResponses.push(anotherResponse);\n            console.log(`Added new response: \"${anotherResponse}\"`);\n            break;\n          }\n        }\n        \n        // If we didn't add a response yet, add the original one\n        if (updatedResponses.length < currentResponses.length) {\n          updatedResponses.push(newResponse);\n          console.log(`Added new response: \"${newResponse}\"`);\n        }\n      } else {\n        console.warn('Failed to get a new random response!');\n      }\n      \n      // Log all submissions and updates to track what's happening\n      console.log(\"DEBUG - Card submission flow:\");\n      console.log(\"1. Original responses:\", [...currentResponses]);\n      console.log(\"2. Card being submitted:\", responseText);\n      console.log(\"3. Index of submitted card:\", responseIndex);\n      console.log(\"4. Updated responses after removal:\", [...updatedResponses]);\n    }\n\n    // Update the subject with the new array - THIS IS CRITICAL\n    // The playerResponsesSubject needs to be updated immediately\n    console.log(`Updating playerResponsesSubject with new array of length ${updatedResponses.length}`);\n    this.playerResponsesSubject.next(updatedResponses);\n    console.log(`Final updated response array:`, updatedResponses);\n\n    try {\n      // Submit to Firebase and wait for the operation to complete\n      await this.firebaseService.submitResponse(responseText, responseIndex);\n\n      console.log(`Response submitted to Firebase successfully`);\n\n      // After submitting, manually check if all players have submitted responses\n      // This is a backup in case the automatic check in the Firebase service fails\n      setTimeout(() => {\n        this.checkAllResponsesSubmitted();\n      }, 1000); // Wait 1 second to ensure Firebase has updated\n\n      console.log(`===== END SUBMITTING RESPONSE =====`);\n    } catch (error) {\n      // If there's an error, revert the local array\n      this.playerResponsesSubject.next(currentResponses);\n      console.error(`Error submitting response:`, error);\n      console.log(`===== END SUBMITTING RESPONSE (WITH ERROR) =====`);\n      throw error;\n    }\n  }\n\n  // Manually check if all players have submitted responses\n  private async checkAllResponsesSubmitted(): Promise<void> {\n    console.log(`===== MANUAL CHECK FOR ALL RESPONSES SUBMITTED =====`);\n\n    // Get the current game state\n    const gameState = this.gameStateSubject.getValue();\n    if (!gameState) {\n      console.log(`No game state available, aborting check`);\n      console.log(`===== END MANUAL CHECK FOR ALL RESPONSES SUBMITTED =====`);\n      return;\n    }\n\n    // Only proceed if we're in the right state\n    if (gameState.status !== 'Waiting for Player Responses') {\n      console.log(`Game status is ${gameState.status}, not checking responses`);\n      console.log(`===== END MANUAL CHECK FOR ALL RESPONSES SUBMITTED =====`);\n      return;\n    }\n\n    const players = gameState.players || [];\n    const responses = gameState.submitted_responses || [];\n\n    // Find the judge\n    const judgeIndex = gameState.current_judge_index || 0;\n    const judge = players[judgeIndex];\n\n    // Count non-judge players\n    const nonJudgePlayers = players.filter(p => p !== judge);\n\n    console.log('Manual response check:', {\n      totalPlayers: players.length,\n      nonJudgePlayers: nonJudgePlayers.length,\n      submittedResponses: responses.length,\n      judge: judge\n    });\n\n    // If there are no non-judge players, auto-advance\n    if (nonJudgePlayers.length === 0) {\n      console.log('No non-judge players, auto-advancing to Ready for Judging');\n      if (this.isLeader) {\n        await this.firebaseService.updateGameStatus('Ready for Judging');\n      }\n      console.log(`===== END MANUAL CHECK FOR ALL RESPONSES SUBMITTED =====`);\n      return;\n    }\n\n    // Check if each player has submitted a response\n    const playerResponseCount: { [key: string]: number } = {};\n\n    // Initialize counts to zero\n    nonJudgePlayers.forEach(player => {\n      playerResponseCount[player] = 0;\n    });\n\n    // Count responses for each player\n    responses.forEach(response => {\n      if (playerResponseCount[response.player_name] !== undefined) {\n        playerResponseCount[response.player_name]++;\n      }\n    });\n\n    // Check if all players have at least one submission\n    const allPlayersHaveAtLeastOneSubmission = Object.values(playerResponseCount).every(count => count > 0);\n\n    console.log('Player response counts:', playerResponseCount);\n    console.log('All players have at least one submission:', allPlayersHaveAtLeastOneSubmission);\n\n    // If all players have submitted at least one response and we're the leader, update the game status\n    if (allPlayersHaveAtLeastOneSubmission && this.isLeader) {\n      console.log('All players have submitted at least one response, changing status to Ready for Judging');\n      await this.firebaseService.updateGameStatus('Ready for Judging');\n    } else if (allPlayersHaveAtLeastOneSubmission) {\n      console.log('All players have submitted at least one response, but not the leader');\n    } else {\n      console.log('Waiting for more players to submit responses');\n    }\n\n    console.log(`===== END MANUAL CHECK FOR ALL RESPONSES SUBMITTED =====`);\n  }\n\n  // Judge selects a winner\n  selectWinner(responseIndex: number): Promise<void> {\n    return this.firebaseService.selectWinner(responseIndex);\n  }\n\n  // Skip judging (can be called by any player)\n  skipJudging(): Promise<void> {\n    return this.firebaseService.skipJudging();\n  }\n\n  // Leader starts a new game\n  startNewGame(): Promise<void> {\n    // Get the current responses but DO NOT preserve all of them\n    // We only want to preserve the responses that weren't submitted\n    const currentResponses = this.playerResponsesSubject.getValue();\n    const hasResponses = currentResponses.length > 0;\n    \n    // Don't automatically restore all responses - instead, we'll let the \n    // normal response handling in updatePlayerRole handle this\n    if (hasResponses) {\n      console.log('Current responses before new round:', currentResponses);\n      console.log('These will NOT be automatically restored - only non-submitted responses should persist');\n    }\n    \n    return this.firebaseService.startNewGame();\n  }\n\n  // Leader resets the game\n  resetGame(): Promise<void> {\n    // We intentionally don't preserve responses when resetting the game\n    // This gives players a fresh hand of cards when starting a new game\n    console.log('Resetting game, player will receive new responses');\n    \n    return this.firebaseService.resetGame();\n  }\n\n  // Leader closes the game\n  closeGame(): Promise<void> {\n    // Clear any periodic check interval\n    if (this.periodicCheckInterval) {\n      clearInterval(this.periodicCheckInterval);\n      this.periodicCheckInterval = null;\n    }\n\n    return this.firebaseService.closeGame();\n  }\n\n  // Player leaves the game\n  leaveGame(): Promise<void> {\n    // Clear any periodic check interval\n    if (this.periodicCheckInterval) {\n      clearInterval(this.periodicCheckInterval);\n      this.periodicCheckInterval = null;\n    }\n\n    return this.firebaseService.removePlayerFromGame();\n  }\n\n  // Update game status\n  updateGameStatus(status: string): Promise<void> {\n    console.log(`Updating game status to: ${status}`);\n    return this.firebaseService.updateGameStatus(status);\n  }\n\n  // Update current prompt to \"Incoming Prompt!\"\n  updateCurrentPromptToIncoming(): Promise<void> {\n    console.log('Updating current prompt to \"Incoming Prompt!\"');\n    return this.firebaseService.updateCurrentPromptToIncoming();\n  }\n\n  // Check if current player is the leader\n  get isLeader(): boolean {\n    return this.firebaseService.isGameLeader;\n  }\n\n  // Get current player name\n  get playerName(): string | null {\n    return this.firebaseService.currentPlayerName;\n  }\n\n  // Get current player responses (volatile memory only)\n  getCurrentResponses(): string[] {\n    return this.playerResponsesSubject.getValue();\n  }\n\n  // Helper method to compare arrays\n  private areArraysEqual(arr1: any[], arr2: any[]): boolean {\n    if (arr1.length !== arr2.length) return false;\n    for (let i = 0; i < arr1.length; i++) {\n      if (arr1[i] !== arr2[i]) return false;\n    }\n    return true;\n  }\n}\n\n\n\n\n\n\n"], "mappings": ";;AAGA;AACA;AACA,SAASA,eAAe,QAAQ,MAAM;;;;;AA2BtC,OAAM,MAAOC,WAAW;EAetBC,YACUC,eAAgC,EAChCC,eAAgC;EACxC;EACA;EACQC,MAAc;IAJd,KAAAF,eAAe,GAAfA,eAAe;IACf,KAAAC,eAAe,GAAfA,eAAe;IAGf,KAAAC,MAAM,GAANA,MAAM;IAnBR,KAAAC,gBAAgB,GAAG,IAAIN,eAAe,CAAmB,IAAI,CAAC;IAC/D,KAAAO,UAAU,GAAG,IAAI,CAACD,gBAAgB,CAACE,YAAY,EAAE;IAEhD,KAAAC,sBAAsB,GAAG,IAAIT,eAAe,CAAW,EAAE,CAAC;IAC3D,KAAAU,gBAAgB,GAAG,IAAI,CAACD,sBAAsB,CAACD,YAAY,EAAE;IAE5D,KAAAG,cAAc,GAAG,IAAIX,eAAe,CAAU,KAAK,CAAC;IACrD,KAAAY,QAAQ,GAAG,IAAI,CAACD,cAAc,CAACH,YAAY,EAAE;IAIpD;IACQ,KAAAK,oBAAoB,GAAgC,EAAE;IAS5DC,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;IAE7C;IACA,IAAI,CAACZ,eAAe,CAACI,UAAU,CAACS,SAAS,CAACC,SAAS,IAAG;MACpDH,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEE,SAAS,GAAG,WAAWA,SAAS,CAACC,MAAM,EAAE,GAAG,MAAM,CAAC;MAE1G,IAAID,SAAS,EAAE;QACbH,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;QACjD,IAAI,CAACT,gBAAgB,CAACa,IAAI,CAACF,SAAS,CAAC;QACrC,IAAI,CAACG,gBAAgB,CAACH,SAAS,CAAC;QAChC,IAAI,CAACI,qBAAqB,CAACJ,SAAS,CAAC;QAErC;QACA,IAAI,CAACK,kBAAkB,CAACL,SAAS,CAAC;MACpC,CAAC,MAAM;QACLH,OAAO,CAACS,IAAI,CAAC,yCAAyC,CAAC;MACzD;IACF,CAAC,CAAC;EACJ;EAEA;EACQD,kBAAkBA,CAACL,SAAoB;IAC7C;IACA,IAAI,IAAI,CAACO,qBAAqB,EAAE;MAC9BC,aAAa,CAAC,IAAI,CAACD,qBAAqB,CAAC;MACzC,IAAI,CAACA,qBAAqB,GAAG,IAAI;IACnC;IAEA;IACA,IAAIP,SAAS,CAACC,MAAM,KAAK,8BAA8B,IAAI,IAAI,CAACQ,QAAQ,EAAE;MACxEZ,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;MAEpE;MACA,IAAI,CAACS,qBAAqB,GAAGG,WAAW,CAAC,MAAK;QAC5C,IAAI,CAACC,0BAA0B,EAAE;MACnC,CAAC,EAAE,IAAI,CAAC;IACV;EACF;EAEA;EACMC,QAAQA,CAACC,QAAgB,EAAEC,UAAkB;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACjD,MAAMC,OAAO,SAASF,KAAI,CAAC7B,eAAe,CAAC0B,QAAQ,CAACC,QAAQ,EAAEC,UAAU,CAAC;MACzE,OAAOG,OAAO;IAAC;EACjB;EAEA;EACQd,gBAAgBA,CAACH,SAAoB;IAC3C,MAAMc,UAAU,GAAG,IAAI,CAAC5B,eAAe,CAACgC,iBAAiB;IACzD,IAAI,CAACJ,UAAU,EAAE;IAEjBjB,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;IAC/CD,OAAO,CAACC,GAAG,CAAC,kCAAkCgB,UAAU,EAAE,CAAC;IAE3D;IACA,MAAMK,UAAU,GAAGnB,SAAS,CAACoB,mBAAmB,IAAI,CAAC;IACrD,MAAMC,OAAO,GAAGrB,SAAS,CAACsB,OAAO,IAAItB,SAAS,CAACsB,OAAO,CAACH,UAAU,CAAC,KAAKL,UAAU;IACjF,MAAMS,QAAQ,GAAG,IAAI,CAAC7B,cAAc,CAAC8B,QAAQ,EAAE;IAC/C,IAAI,CAAC9B,cAAc,CAACQ,IAAI,CAACmB,OAAO,CAAC;IAEjC;IACA,MAAMI,4BAA4B,GAAG,IAAI,CAACjC,sBAAsB,CAACgC,QAAQ,EAAE;IAE3E;IACA,IAAI,CAAC,IAAI,CAAC5B,oBAAoB,EAAE;MAC9B,IAAI,CAACA,oBAAoB,GAAG,EAAE;IAChC;IAEA;IACA,IAAIyB,OAAO,EAAE;MACXxB,OAAO,CAACC,GAAG,CAAC,UAAUgB,UAAU,iDAAiD,CAAC;MAElF;MACA,IAAIW,4BAA4B,CAACC,MAAM,GAAG,CAAC,EAAE;QAC3C7B,OAAO,CAACC,GAAG,CAAC,UAAU2B,4BAA4B,CAACC,MAAM,yBAAyBZ,UAAU,EAAE,CAAC;QAC/F,IAAI,CAAClB,oBAAoB,CAACkB,UAAU,CAAC,GAAG,CAAC,GAAGW,4BAA4B,CAAC;MAC3E;MAEA;MACA,IAAI,CAACjC,sBAAsB,CAACU,IAAI,CAAC,EAAE,CAAC;MACpCL,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;MAC3D;IACF;IAEA;IACA,IAAIyB,QAAQ,IAAI,CAACF,OAAO,IAAI,IAAI,CAACzB,oBAAoB,IAAI,IAAI,CAACA,oBAAoB,CAACkB,UAAU,CAAC,EAAE;MAC9F,MAAMa,cAAc,GAAG,IAAI,CAAC/B,oBAAoB,CAACkB,UAAU,CAAC;MAC5D,IAAIa,cAAc,CAACD,MAAM,GAAG,CAAC,EAAE;QAC7B7B,OAAO,CAACC,GAAG,CAAC,UAAUgB,UAAU,2DAA2D,EAAEa,cAAc,CAAC;QAC5G,IAAI,CAACnC,sBAAsB,CAACU,IAAI,CAAC,CAAC,GAAGyB,cAAc,CAAC,CAAC;QACrD9B,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;QAC9D;MACF,CAAC,MAAM;QACLD,OAAO,CAACC,GAAG,CAAC,UAAUgB,UAAU,iGAAiG,CAAC;MACpI;IACF,CAAC,MAAM,IAAIS,QAAQ,IAAI,CAACF,OAAO,EAAE;MAC/BxB,OAAO,CAACC,GAAG,CAAC,UAAUgB,UAAU,iGAAiG,CAAC;IACpI;IAEA;IACA,MAAMc,gBAAgB,GAAG,IAAI,CAACpC,sBAAsB,CAACgC,QAAQ,EAAE;IAE/D;IACA,IAAII,gBAAgB,CAACF,MAAM,KAAK,CAAC,KAAK1B,SAAS,CAACC,MAAM,KAAK,OAAO,IAAID,SAAS,CAACC,MAAM,KAAK,8BAA8B,CAAC,EAAE;MAC1H;MACAJ,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;MACpD,MAAM+B,YAAY,GAAG,IAAI,CAAC1C,eAAe,CAAC2C,kBAAkB,CAAC,CAAC,CAAC;MAC/D,IAAI,CAACtC,sBAAsB,CAACU,IAAI,CAAC2B,YAAY,CAAC;MAC9ChC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE+B,YAAY,CAAC;MAExD;MACA;MACAhC,OAAO,CAACC,GAAG,CAAC,oEAAoE,CAAC;MAEjFD,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;MACnD;IACF;IAEA;IAEA;IACA;IACA,IAAIE,SAAS,CAAC+B,gBAAgB,IAAI/B,SAAS,CAAC+B,gBAAgB,CAACjB,UAAU,CAAC,EAAE;MACxE,MAAMkB,aAAa,GAAGhC,SAAS,CAAC+B,gBAAgB,CAACjB,UAAU,CAAC;MAC5DjB,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEkC,aAAa,CAAC;MAE5D;MACA,MAAMC,eAAe,GAAG,CAAC,GAAG,IAAIC,GAAG,CAACF,aAAa,CAAC,CAAC;MACnD,IAAIC,eAAe,CAACP,MAAM,KAAKM,aAAa,CAACN,MAAM,EAAE;QACnD7B,OAAO,CAACS,IAAI,CAAC,wDAAwD,CAAC;QACtE,IAAI,CAACd,sBAAsB,CAACU,IAAI,CAAC+B,eAAe,CAAC;MACnD,CAAC,MAAM;QACL,IAAI,CAACzC,sBAAsB,CAACU,IAAI,CAAC8B,aAAa,CAAC;MACjD;MACAnC,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;MACnD;IACF;IAEA;IACA,IAAIE,SAAS,CAACmC,qBAAqB,IAAInC,SAAS,CAACmC,qBAAqB,CAACrB,UAAU,CAAC,EAAE;MAClF,MAAMkB,aAAa,GAAGhC,SAAS,CAACmC,qBAAqB,CAACrB,UAAU,CAAC;MACjEjB,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEkC,aAAa,CAAC;MAC1D,IAAI,CAACxC,sBAAsB,CAACU,IAAI,CAAC8B,aAAa,CAAC;MAC/CnC,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;MACnD;IACF;IAEA;IACA,IAAI8B,gBAAgB,CAACF,MAAM,GAAG,CAAC,KAAK1B,SAAS,CAACC,MAAM,KAAK,OAAO,IAAID,SAAS,CAACC,MAAM,KAAK,8BAA8B,CAAC,EAAE;MACxHJ,OAAO,CAACC,GAAG,CAAC,cAAc8B,gBAAgB,CAACF,MAAM,2BAA2B,CAAC,GAAGE,gBAAgB,CAACF,MAAM,OAAO,CAAC;MAE/G;MACA,IAAIE,gBAAgB,CAACF,MAAM,GAAG,CAAC,EAAE;QAC/B;QACA;QACA,MAAMU,aAAa,GAAGpC,SAAS,CAACqC,aAAa,GAAGrC,SAAS,CAACqC,aAAa,GAAG,CAAC,GAAG,CAAC;QAC/E,MAAMC,YAAY,GAAGtC,SAAS,CAACqC,aAAa,IAAI,CAAC;QACjD,MAAME,YAAY,GAAGH,aAAa,GAAG,CAAC,IAAIA,aAAa,KAAKE,YAAY;QAExE,IAAIC,YAAY,EAAE;UAChB1C,OAAO,CAACC,GAAG,CAAC,sBAAsBsC,aAAa,OAAOE,YAAY,EAAE,CAAC;UACrEzC,OAAO,CAACC,GAAG,CAAC,cAAc8B,gBAAgB,CAACF,MAAM,mCAAmC,CAAC;QACvF;QAEA;QACA,MAAMc,gBAAgB,GAAG,CAAC,GAAGZ,gBAAgB,CAACF,MAAM;QACpD7B,OAAO,CAACC,GAAG,CAAC,UAAU0C,gBAAgB,iCAAiC,CAAC;QAExE,MAAMC,mBAAmB,GAAG,IAAI,CAACtD,eAAe,CAAC2C,kBAAkB,CAACU,gBAAgB,CAAC;QACrF,MAAME,gBAAgB,GAAG,CAAC,GAAGd,gBAAgB,EAAE,GAAGa,mBAAmB,CAAC;QAEtE5C,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE4C,gBAAgB,CAAC;QACxD,IAAI,CAAClD,sBAAsB,CAACU,IAAI,CAACwC,gBAAgB,CAAC;MACpD,CAAC,MAAM;QACL;QACA7C,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;QAC1D,MAAM+B,YAAY,GAAG,IAAI,CAAC1C,eAAe,CAAC2C,kBAAkB,CAAC,CAAC,CAAC;QAC/D,IAAI,CAACtC,sBAAsB,CAACU,IAAI,CAAC2B,YAAY,CAAC;QAC9ChC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE+B,YAAY,CAAC;MAC1D;MAEA;MACA;MACAhC,OAAO,CAACC,GAAG,CAAC,oEAAoE,CAAC;IACnF;IAEA;IACA,IAAI8B,gBAAgB,CAACF,MAAM,GAAG,CAAC,IAAI,CAACL,OAAO,IACvCrB,SAAS,CAACC,MAAM,KAAK,OAAO,IAAID,SAAS,CAACC,MAAM,KAAK,8BAA8B,EAAE;MACvFJ,OAAO,CAACC,GAAG,CAAC,mBAAmB8B,gBAAgB,CAACF,MAAM,kDAAkD1B,SAAS,CAACC,MAAM,EAAE,CAAC;IAC7H;IAEAJ,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;EACrD;EAEA;EACQM,qBAAqBA,CAACJ,SAAoB;IAChDH,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEE,SAAS,CAACC,MAAM,CAAC;IAEpD;IACA,MAAM2B,gBAAgB,GAAG,IAAI,CAACpC,sBAAsB,CAACgC,QAAQ,EAAE;IAE/D;IACA,IAAIxB,SAAS,CAAC2C,cAAc,EAAE;MAC5B9C,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE;QACtD8C,YAAY,EAAE5C,SAAS,CAAC2C,cAAc;QACtCE,UAAU,EAAE7C,SAAS,CAAC2C,cAAc,CAACG,MAAM;QAC3CC,iBAAiB,EAAE,QAAQ,IAAI/C,SAAS,CAAC2C,cAAc;QACvDK,iBAAiB,EAAEC,IAAI,CAACC,SAAS,CAAClD,SAAS,CAAC2C,cAAc;OAC3D,CAAC;IACJ,CAAC,MAAM;MACL9C,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;IACjD;IAEA;IACA,IAAIE,SAAS,CAACC,MAAM,KAAK,OAAO,IAAID,SAAS,CAACC,MAAM,KAAK,8BAA8B,IAAID,SAAS,CAACC,MAAM,KAAK,oBAAoB,EAAE;MACpIJ,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;QACjCG,MAAM,EAAED,SAAS,CAACC,MAAM;QACxB6C,MAAM,EAAE9C,SAAS,CAAC2C,cAAc;QAChCQ,eAAe,EAAEnD,SAAS,CAACoD,gBAAgB;QAC3CC,YAAY,EAAErD,SAAS,CAACsD,aAAa;QACrCC,SAAS,EAAEvD,SAAS,CAACuD,SAAS;QAC9BpB,qBAAqB,EAAEnC,SAAS,CAACmC,qBAAqB;QACtDJ,gBAAgB,EAAE/B,SAAS,CAAC+B,gBAAgB;QAC5CM,aAAa,EAAErC,SAAS,CAACqC;OAC1B,CAAC;MAEF;MACA,MAAMvB,UAAU,GAAG,IAAI,CAAC5B,eAAe,CAACgC,iBAAiB;MACzD,IAAIJ,UAAU,EAAE;QAAA,IAAA0C,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA;QACd7D,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE;UAC3CqC,qBAAqB,GAAAqB,qBAAA,GAAExD,SAAS,CAACmC,qBAAqB,cAAAqB,qBAAA,uBAA/BA,qBAAA,CAAkC1C,UAAU,CAAC;UACpEiB,gBAAgB,GAAA0B,sBAAA,GAAEzD,SAAS,CAAC+B,gBAAgB,cAAA0B,sBAAA,uBAA1BA,sBAAA,CAA6B3C,UAAU,CAAC;UAC1DsC,gBAAgB,GAAAM,qBAAA,GAAE1D,SAAS,CAACoD,gBAAgB,cAAAM,qBAAA,uBAA1BA,qBAAA,CAA6B5C,UAAU;SAC1D,CAAC;MACJ;IACF;IAEA,QAAQd,SAAS,CAACC,MAAM;MACtB,KAAK,mBAAmB;QACtB;QACA,MAAMa,UAAU,GAAG,IAAI,CAAC5B,eAAe,CAACgC,iBAAiB;QACzD,IAAIJ,UAAU,EAAE;UACd;UACA,MAAMK,UAAU,GAAGnB,SAAS,CAACoB,mBAAmB,IAAI,CAAC;UACrD,MAAMC,OAAO,GAAGrB,SAAS,CAACsB,OAAO,IAAItB,SAAS,CAACsB,OAAO,CAACH,UAAU,CAAC,KAAKL,UAAU;UAEjF;UACA,IAAI,CAACO,OAAO,EAAE;YACZ;YACA,MAAMO,gBAAgB,GAAG,IAAI,CAACpC,sBAAsB,CAACgC,QAAQ,EAAE;YAC/D3B,OAAO,CAACC,GAAG,CAAC,cAAc8B,gBAAgB,CAACF,MAAM,uCAAuC,CAAC;YACzF7B,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;UACxD,CAAC,MAAM;YACLD,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;UACpC;QACF;QACA;MAEF,KAAK,oBAAoB;QACvB;QACAD,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;QAErE;QACA,IAAI,IAAI,CAACW,QAAQ,EAAE;UACjBZ,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;UACnE,IAAI,CAAC6D,6BAA6B,EAAE,CAACC,IAAI,CAAC,MAAK;YAC7C/D,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;UACvE,CAAC,CAAC;QACJ;QAEA,IAAIE,SAAS,CAAC2C,cAAc,IAAI3C,SAAS,CAAC2C,cAAc,CAACG,MAAM,EAAE;UAC/D;UACAjD,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAEE,SAAS,CAAC2C,cAAc,CAACG,MAAM,CAAC;UAE/F;UACA;UACAjD,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;QACtE,CAAC,MAAM;UACLD,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;UAE1D;UACA;QACF;QACA;MAEF,KAAK,OAAO;QACV;QACAD,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;QAExD,IAAI,CAACE,SAAS,CAAC2C,cAAc,IAAI,CAAC3C,SAAS,CAAC2C,cAAc,CAACG,MAAM,EAAE;UACjEjD,OAAO,CAACC,GAAG,CAAC,6EAA6E,CAAC;UAE1F;UACA,IAAI,IAAI,CAACW,QAAQ,EAAE;YACjBZ,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;YAC/D+D,UAAU,CAAC,MAAK;cACd,IAAI,CAAC3E,eAAe,CAAC4E,cAAc,EAAE;YACvC,CAAC,EAAE,IAAI,CAAC;UACV;QACF,CAAC,MAAM;UACLjE,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEE,SAAS,CAAC2C,cAAc,CAACG,MAAM,CAAC;QAChF;QAEA;QACA,IAAI,IAAI,CAACrC,QAAQ,EAAE;UACjBZ,OAAO,CAACC,GAAG,CAAC,gEAAgE,CAAC;UAC7E+D,UAAU,CAAC,MAAK;YACd,IAAI,CAAC3E,eAAe,CAAC6E,gBAAgB,CAAC,8BAA8B,CAAC;UACvE,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;QACZ;QACA;MAEF,KAAK,eAAe;QAClB;QACA,IAAI,IAAI,CAACtD,QAAQ,EAAE;UACjBZ,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;UAE5D;UACA,MAAMkE,gBAAgB,GAAGC,IAAI,CAACC,GAAG,EAAE;UACnCC,YAAY,CAACC,OAAO,CAAC,kBAAkB,EAAEJ,gBAAgB,CAACK,QAAQ,EAAE,CAAC;UAErE;UACA;UACAxE,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;QAChE;QAEA;QACA,IAAI,CAAC,IAAI,CAACJ,cAAc,CAAC8B,QAAQ,EAAE,EAAE;UACnC,MAAMI,gBAAgB,GAAG,IAAI,CAACpC,sBAAsB,CAACgC,QAAQ,EAAE;UAC/D,IAAII,gBAAgB,CAACF,MAAM,GAAG,CAAC,EAAE;YAC/B7B,OAAO,CAACC,GAAG,CAAC,qDAAqD,EAAE8B,gBAAgB,CAAC;YAEpF;YACA;YACA,MAAMd,UAAU,GAAG,IAAI,CAAC5B,eAAe,CAACgC,iBAAiB;YACzD,IAAIJ,UAAU,EAAE;cACd,IAAI,CAAClB,oBAAoB,CAACkB,UAAU,CAAC,GAAG,CAAC,GAAGc,gBAAgB,CAAC;cAC7D/B,OAAO,CAACC,GAAG,CAAC,aAAa8B,gBAAgB,CAACF,MAAM,yBAAyBZ,UAAU,yBAAyB,CAAC;YAC/G;UACF;QACF;QACA;MAEF,KAAK,WAAW;QACd;QACAjB,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEE,SAAS,CAACsE,WAAW,CAAC;QACxD;QACA;MAEF,KAAK,QAAQ;QACX;QACA,IAAI,CAACpF,eAAe,CAACqF,eAAe,EAAE;QACtC,IAAI,CAACnF,MAAM,CAACoF,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;QAC/B;IACJ;IAEA;IACA,IAAIxE,SAAS,CAACC,MAAM,KAAK,8BAA8B,EAAE;MACvDJ,OAAO,CAACC,GAAG,CAAC,mFAAmF,CAAC;MAEhG,MAAMgB,UAAU,GAAG,IAAI,CAAC5B,eAAe,CAACgC,iBAAiB;MACzD,IAAIJ,UAAU,EAAE;QAAA,IAAA2D,kBAAA;QACd5E,OAAO,CAACC,GAAG,CAAC,mBAAmBgB,UAAU,EAAE,CAAC;QAE5C;QACA,MAAMK,UAAU,GAAGnB,SAAS,CAACoB,mBAAmB,IAAI,CAAC;QACrD,MAAMC,OAAO,GAAGrB,SAAS,CAACsB,OAAO,IAAItB,SAAS,CAACsB,OAAO,CAACH,UAAU,CAAC,KAAKL,UAAU;QACjFjB,OAAO,CAACC,GAAG,CAAC,wBAAwBuB,OAAO,kBAAkBF,UAAU,aAAAsD,kBAAA,GAAYzE,SAAS,CAACsB,OAAO,cAAAmD,kBAAA,uBAAjBA,kBAAA,CAAoBtD,UAAU,CAAC,GAAG,CAAC;QAEtH;QACA,MAAMS,gBAAgB,GAAG,IAAI,CAACpC,sBAAsB,CAACgC,QAAQ,EAAE;QAC/D3B,OAAO,CAACC,GAAG,CAAC,4BAA4B8B,gBAAgB,CAACF,MAAM,GAAG,CAAC,GAAGE,gBAAgB,CAACF,MAAM,GAAG,MAAM,EAAE,CAAC;QACzG,IAAIE,gBAAgB,CAACF,MAAM,GAAG,CAAC,EAAE;UAC/B7B,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE8B,gBAAgB,CAAC8C,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAChE;QAEA;QACA,IAAI,CAACrD,OAAO,EAAE;UAAA,IAAAsD,sBAAA,EAAAC,sBAAA;UACZ;UACA,MAAMC,mBAAmB,GAAG,EAAAF,sBAAA,GAAA3E,SAAS,CAAC+B,gBAAgB,cAAA4C,sBAAA,uBAA1BA,sBAAA,CAA6B7D,UAAU,CAAC,OAAA8D,sBAAA,GAAI5E,SAAS,CAACmC,qBAAqB,cAAAyC,sBAAA,uBAA/BA,sBAAA,CAAkC9D,UAAU,CAAC,KAAI,EAAE;UAC3H,MAAMc,gBAAgB,GAAG,IAAI,CAACpC,sBAAsB,CAACgC,QAAQ,EAAE;UAE/D;UACA;UACA;UACA;UACA;UACA,IAAIqD,mBAAmB,CAACnD,MAAM,GAAG,CAAC,IAAIE,gBAAgB,CAACF,MAAM,KAAK,CAAC,EAAE;YACnE7B,OAAO,CAACC,GAAG,CAAC,oDAAoD,EAAE+E,mBAAmB,CAAC;YACtF,IAAI,CAACrF,sBAAsB,CAACU,IAAI,CAAC2E,mBAAmB,CAAC;UACvD,CAAC,MAAM,IAAIjD,gBAAgB,CAACF,MAAM,KAAK,CAAC,EAAE;YACxC;YACA;YACA7B,OAAO,CAACC,GAAG,CAAC,mEAAmE,CAAC;YAChF,MAAM+B,YAAY,GAAG,IAAI,CAAC1C,eAAe,CAAC2C,kBAAkB,CAAC,CAAC,CAAC;YAC/D,IAAI,CAACtC,sBAAsB,CAACU,IAAI,CAAC2B,YAAY,CAAC;YAC9ChC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE+B,YAAY,CAAC;YAExD;UACF,CAAC,MAAM,IAAID,gBAAgB,CAACF,MAAM,GAAG,CAAC,EAAE;YACtC;YACA7B,OAAO,CAACC,GAAG,CAAC,aAAa8B,gBAAgB,CAACF,MAAM,6BAA6B,CAAC;YAC9E,MAAMc,gBAAgB,GAAG,CAAC,GAAGZ,gBAAgB,CAACF,MAAM;YACpD,MAAMe,mBAAmB,GAAG,IAAI,CAACtD,eAAe,CAAC2C,kBAAkB,CAACU,gBAAgB,CAAC;YACrF,MAAME,gBAAgB,GAAG,CAAC,GAAGd,gBAAgB,EAAE,GAAGa,mBAAmB,CAAC;YAEtE5C,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE4C,gBAAgB,CAAC;YACxD,IAAI,CAAClD,sBAAsB,CAACU,IAAI,CAACwC,gBAAgB,CAAC;UACpD,CAAC,MAAM;YACL;YACA7C,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE8B,gBAAgB,CAAC;UACjE;QACF;MACF;IACF;EACF;EAEA;EACMkD,cAAcA,CAACC,YAAoB,EAAEC,aAAqB;IAAA,IAAAC,MAAA;IAAA,OAAAjE,iBAAA;MAC9DnB,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;MAC9CD,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEmF,MAAI,CAACzF,sBAAsB,CAACgC,QAAQ,EAAE,CAAC;MAC/E3B,OAAO,CAACC,GAAG,CAAC,yBAAyBiF,YAAY,cAAcC,aAAa,EAAE,CAAC;MAE/E;MACA,MAAMpD,gBAAgB,GAAGqD,MAAI,CAACzF,sBAAsB,CAACgC,QAAQ,EAAE;MAC/D,IAAIkB,gBAA0B;MAE9B;MACA,IAAIsC,aAAa,KAAK,CAAC,CAAC,EAAE;QACxB;QACAnF,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;QACxE4C,gBAAgB,GAAG,CAAC,GAAGd,gBAAgB,CAAC;MAC1C,CAAC,MAAM;QACL;QACA;QACA/B,OAAO,CAACC,GAAG,CAAC,8BAA8BkF,aAAa,MAAMpD,gBAAgB,CAACoD,aAAa,CAAC,GAAG,CAAC;QAChGtC,gBAAgB,GAAGd,gBAAgB,CAACsD,MAAM,CAAC,CAACC,CAAC,EAAEC,KAAK,KAAKA,KAAK,KAAKJ,aAAa,CAAC;QACjFnF,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE4C,gBAAgB,CAAC;QAEtE;QACA,MAAM2C,WAAW,GAAGJ,MAAI,CAAC9F,eAAe,CAAC2C,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjE,IAAIuD,WAAW,EAAE;UACf;UACA,OAAO3C,gBAAgB,CAAC4C,QAAQ,CAACD,WAAW,CAAC,EAAE;YAC7CxF,OAAO,CAACC,GAAG,CAAC,aAAauF,WAAW,4CAA4C,CAAC;YACjF,MAAME,eAAe,GAAGN,MAAI,CAAC9F,eAAe,CAAC2C,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrE,IAAIyD,eAAe,IAAI,CAAC7C,gBAAgB,CAAC4C,QAAQ,CAACC,eAAe,CAAC,EAAE;cAClE1F,OAAO,CAACC,GAAG,CAAC,gCAAgCyF,eAAe,GAAG,CAAC;cAC/D7C,gBAAgB,CAAC8C,IAAI,CAACD,eAAe,CAAC;cACtC1F,OAAO,CAACC,GAAG,CAAC,wBAAwByF,eAAe,GAAG,CAAC;cACvD;YACF;UACF;UAEA;UACA,IAAI7C,gBAAgB,CAAChB,MAAM,GAAGE,gBAAgB,CAACF,MAAM,EAAE;YACrDgB,gBAAgB,CAAC8C,IAAI,CAACH,WAAW,CAAC;YAClCxF,OAAO,CAACC,GAAG,CAAC,wBAAwBuF,WAAW,GAAG,CAAC;UACrD;QACF,CAAC,MAAM;UACLxF,OAAO,CAACS,IAAI,CAAC,sCAAsC,CAAC;QACtD;QAEA;QACAT,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;QAC5CD,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE,CAAC,GAAG8B,gBAAgB,CAAC,CAAC;QAC5D/B,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEiF,YAAY,CAAC;QACrDlF,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEkF,aAAa,CAAC;QACzDnF,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE,CAAC,GAAG4C,gBAAgB,CAAC,CAAC;MAC3E;MAEA;MACA;MACA7C,OAAO,CAACC,GAAG,CAAC,4DAA4D4C,gBAAgB,CAAChB,MAAM,EAAE,CAAC;MAClGuD,MAAI,CAACzF,sBAAsB,CAACU,IAAI,CAACwC,gBAAgB,CAAC;MAClD7C,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE4C,gBAAgB,CAAC;MAE9D,IAAI;QACF;QACA,MAAMuC,MAAI,CAAC/F,eAAe,CAAC4F,cAAc,CAACC,YAAY,EAAEC,aAAa,CAAC;QAEtEnF,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;QAE1D;QACA;QACA+D,UAAU,CAAC,MAAK;UACdoB,MAAI,CAACtE,0BAA0B,EAAE;QACnC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;QAEVd,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;MACpD,CAAC,CAAC,OAAO2F,KAAK,EAAE;QACd;QACAR,MAAI,CAACzF,sBAAsB,CAACU,IAAI,CAAC0B,gBAAgB,CAAC;QAClD/B,OAAO,CAAC4F,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClD5F,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;QAC/D,MAAM2F,KAAK;MACb;IAAC;EACH;EAEA;EACc9E,0BAA0BA,CAAA;IAAA,IAAA+E,MAAA;IAAA,OAAA1E,iBAAA;MACtCnB,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;MAEnE;MACA,MAAME,SAAS,GAAG0F,MAAI,CAACrG,gBAAgB,CAACmC,QAAQ,EAAE;MAClD,IAAI,CAACxB,SAAS,EAAE;QACdH,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;QACtDD,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;QACvE;MACF;MAEA;MACA,IAAIE,SAAS,CAACC,MAAM,KAAK,8BAA8B,EAAE;QACvDJ,OAAO,CAACC,GAAG,CAAC,kBAAkBE,SAAS,CAACC,MAAM,0BAA0B,CAAC;QACzEJ,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;QACvE;MACF;MAEA,MAAMwB,OAAO,GAAGtB,SAAS,CAACsB,OAAO,IAAI,EAAE;MACvC,MAAMiC,SAAS,GAAGvD,SAAS,CAAC2F,mBAAmB,IAAI,EAAE;MAErD;MACA,MAAMxE,UAAU,GAAGnB,SAAS,CAACoB,mBAAmB,IAAI,CAAC;MACrD,MAAMwE,KAAK,GAAGtE,OAAO,CAACH,UAAU,CAAC;MAEjC;MACA,MAAM0E,eAAe,GAAGvE,OAAO,CAAC4D,MAAM,CAACY,CAAC,IAAIA,CAAC,KAAKF,KAAK,CAAC;MAExD/F,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE;QACpCiG,YAAY,EAAEzE,OAAO,CAACI,MAAM;QAC5BmE,eAAe,EAAEA,eAAe,CAACnE,MAAM;QACvCsE,kBAAkB,EAAEzC,SAAS,CAAC7B,MAAM;QACpCkE,KAAK,EAAEA;OACR,CAAC;MAEF;MACA,IAAIC,eAAe,CAACnE,MAAM,KAAK,CAAC,EAAE;QAChC7B,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;QACxE,IAAI4F,MAAI,CAACjF,QAAQ,EAAE;UACjB,MAAMiF,MAAI,CAACxG,eAAe,CAAC6E,gBAAgB,CAAC,mBAAmB,CAAC;QAClE;QACAlE,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;QACvE;MACF;MAEA;MACA,MAAMmG,mBAAmB,GAA8B,EAAE;MAEzD;MACAJ,eAAe,CAACK,OAAO,CAACC,MAAM,IAAG;QAC/BF,mBAAmB,CAACE,MAAM,CAAC,GAAG,CAAC;MACjC,CAAC,CAAC;MAEF;MACA5C,SAAS,CAAC2C,OAAO,CAACE,QAAQ,IAAG;QAC3B,IAAIH,mBAAmB,CAACG,QAAQ,CAACC,WAAW,CAAC,KAAKC,SAAS,EAAE;UAC3DL,mBAAmB,CAACG,QAAQ,CAACC,WAAW,CAAC,EAAE;QAC7C;MACF,CAAC,CAAC;MAEF;MACA,MAAME,kCAAkC,GAAGC,MAAM,CAACC,MAAM,CAACR,mBAAmB,CAAC,CAACS,KAAK,CAACC,KAAK,IAAIA,KAAK,GAAG,CAAC,CAAC;MAEvG9G,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEmG,mBAAmB,CAAC;MAC3DpG,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAEyG,kCAAkC,CAAC;MAE5F;MACA,IAAIA,kCAAkC,IAAIb,MAAI,CAACjF,QAAQ,EAAE;QACvDZ,OAAO,CAACC,GAAG,CAAC,wFAAwF,CAAC;QACrG,MAAM4F,MAAI,CAACxG,eAAe,CAAC6E,gBAAgB,CAAC,mBAAmB,CAAC;MAClE,CAAC,MAAM,IAAIwC,kCAAkC,EAAE;QAC7C1G,OAAO,CAACC,GAAG,CAAC,sEAAsE,CAAC;MACrF,CAAC,MAAM;QACLD,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;MAC7D;MAEAD,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;IAAC;EAC1E;EAEA;EACA8G,YAAYA,CAAC5B,aAAqB;IAChC,OAAO,IAAI,CAAC9F,eAAe,CAAC0H,YAAY,CAAC5B,aAAa,CAAC;EACzD;EAEA;EACA6B,WAAWA,CAAA;IACT,OAAO,IAAI,CAAC3H,eAAe,CAAC2H,WAAW,EAAE;EAC3C;EAEA;EACAC,YAAYA,CAAA;IACV;IACA;IACA,MAAMlF,gBAAgB,GAAG,IAAI,CAACpC,sBAAsB,CAACgC,QAAQ,EAAE;IAC/D,MAAMuF,YAAY,GAAGnF,gBAAgB,CAACF,MAAM,GAAG,CAAC;IAEhD;IACA;IACA,IAAIqF,YAAY,EAAE;MAChBlH,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE8B,gBAAgB,CAAC;MACpE/B,OAAO,CAACC,GAAG,CAAC,wFAAwF,CAAC;IACvG;IAEA,OAAO,IAAI,CAACZ,eAAe,CAAC4H,YAAY,EAAE;EAC5C;EAEA;EACAE,SAASA,CAAA;IACP;IACA;IACAnH,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;IAEhE,OAAO,IAAI,CAACZ,eAAe,CAAC8H,SAAS,EAAE;EACzC;EAEA;EACAC,SAASA,CAAA;IACP;IACA,IAAI,IAAI,CAAC1G,qBAAqB,EAAE;MAC9BC,aAAa,CAAC,IAAI,CAACD,qBAAqB,CAAC;MACzC,IAAI,CAACA,qBAAqB,GAAG,IAAI;IACnC;IAEA,OAAO,IAAI,CAACrB,eAAe,CAAC+H,SAAS,EAAE;EACzC;EAEA;EACAC,SAASA,CAAA;IACP;IACA,IAAI,IAAI,CAAC3G,qBAAqB,EAAE;MAC9BC,aAAa,CAAC,IAAI,CAACD,qBAAqB,CAAC;MACzC,IAAI,CAACA,qBAAqB,GAAG,IAAI;IACnC;IAEA,OAAO,IAAI,CAACrB,eAAe,CAACiI,oBAAoB,EAAE;EACpD;EAEA;EACApD,gBAAgBA,CAAC9D,MAAc;IAC7BJ,OAAO,CAACC,GAAG,CAAC,4BAA4BG,MAAM,EAAE,CAAC;IACjD,OAAO,IAAI,CAACf,eAAe,CAAC6E,gBAAgB,CAAC9D,MAAM,CAAC;EACtD;EAEA;EACA0D,6BAA6BA,CAAA;IAC3B9D,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;IAC5D,OAAO,IAAI,CAACZ,eAAe,CAACyE,6BAA6B,EAAE;EAC7D;EAEA;EACA,IAAIlD,QAAQA,CAAA;IACV,OAAO,IAAI,CAACvB,eAAe,CAACkI,YAAY;EAC1C;EAEA;EACA,IAAItG,UAAUA,CAAA;IACZ,OAAO,IAAI,CAAC5B,eAAe,CAACgC,iBAAiB;EAC/C;EAEA;EACAmG,mBAAmBA,CAAA;IACjB,OAAO,IAAI,CAAC7H,sBAAsB,CAACgC,QAAQ,EAAE;EAC/C;EAEA;EACQ8F,cAAcA,CAACC,IAAW,EAAEC,IAAW;IAC7C,IAAID,IAAI,CAAC7F,MAAM,KAAK8F,IAAI,CAAC9F,MAAM,EAAE,OAAO,KAAK;IAC7C,KAAK,IAAI+F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,IAAI,CAAC7F,MAAM,EAAE+F,CAAC,EAAE,EAAE;MACpC,IAAIF,IAAI,CAACE,CAAC,CAAC,KAAKD,IAAI,CAACC,CAAC,CAAC,EAAE,OAAO,KAAK;IACvC;IACA,OAAO,IAAI;EACb;;eArrBWzI,WAAW;;mCAAXA,YAAW,EAAA0I,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,eAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,MAAA;AAAA;;SAAXjJ,YAAW;EAAAkJ,OAAA,EAAXlJ,YAAW,CAAAmJ,IAAA;EAAAC,UAAA,EAFV;AAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}