const functions = require('firebase-functions');
const admin = require('firebase-admin');
admin.initializeApp();

exports.deleteOldRooms = functions.pubsub.schedule('every 24 hours').onRun(async (context) => {
    const cutoff = Date.now() - (24 * 60 * 60 * 1000); // 24 hours ago
    
    const ref = admin.database().ref('rooms');
    const snapshot = await ref.orderByChild('timestamp').endAt(cutoff).once('value');
    
    const updates = {};
    snapshot.forEach(child => {
        updates[child.key] = null;
    });
    
    return ref.update(updates);
});