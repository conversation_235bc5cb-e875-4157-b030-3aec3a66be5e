import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { Router } from '@angular/router';
import { AlertController } from '@ionic/angular';
import { GameService } from '../services/game.service';
import { Subscription } from 'rxjs';
import { GameState } from '../services/game.service';

@Component({
  selector: 'app-game',
  templateUrl: './game.page.html',
  styleUrls: ['./game.page.scss'],
  standalone: false,
})
export class GamePage implements OnInit, OnDestroy {
  gameState: GameState | null = null;
  playerResponses: string[] = [];
  isJudge: boolean = false;
  selectedResponse: string | null = null;
  selectedResponseIndex: number = -1; // Track the index of the selected response
  selectedWinnerIndex: number | null = null;
  hasSubmitted: boolean = false;


  // New variables for custom response feature
  responseMode: string = 'cards'; // 'cards' or 'custom'
  customResponse: string = '';

  // Add a new property to track if prompt should be hidden
  hidePrompt: boolean = false;

  // Add a new property to track the last selected response index
  private lastSelectedResponseIndex: number = -1;

  // Add these properties to track selection state more reliably
  private selectedResponseText: string | null = null;
  private isProcessingGameStateUpdate: boolean = false;

  // Add properties to track custom response state
  private lastCustomResponse: string = '';
  private lastResponseMode: string = 'cards';

  private gameStateSubscription: Subscription | null = null;
  private playerResponsesSubscription: Subscription | null = null;
  private isJudgeSubscription: Subscription | null = null;
  private previousGameState: string | null = null;

  constructor(
    private gameService: GameService,
    private router: Router,
    private alertController: AlertController
  ) {}



  ngOnInit() {
    console.log('GamePage ngOnInit called');

    // Subscribe to game state changes
    this.gameStateSubscription = this.gameService.gameState$.subscribe(state => {
      console.log('GamePage received game state update:', state ? `Status: ${state.status}` : 'null');

      this.isProcessingGameStateUpdate = true;

      // Update the gameState property
      this.gameState = state;

      this.previousGameState = state?.status || null;

      // Hide prompt when transitioning to Winner Chosen
      if (state && state.status === 'Winner Chosen') {
        this.hidePrompt = true;
        console.log('Hiding prompt because status is Winner Chosen');
      }

      // Show prompt when we have a new prompt in any state that should display it
      if (state && state.current_prompt && state.current_prompt.prompt) {
        // Show prompt for these states
        if (state.status === 'Waiting for Prompt' ||
            state.status === 'Ready' ||
            state.status === 'Waiting for Player Responses' ||
            state.status === 'Ready for Judging') {
          this.hidePrompt = false;
          console.log('Showing prompt in', state.status, 'state with prompt:', state.current_prompt.prompt);
        }
      }

      // Hide prompt when waiting for a new prompt (but no prompt available yet)
      if (state && state.status === 'Waiting for Prompt' && (!state.current_prompt || !state.current_prompt.prompt)) {
        this.hidePrompt = true;
        console.log('Hiding prompt because status is Waiting for Prompt and no prompt available');
      }

      // Reset submission state when game status changes
      if (state && state.status === 'Waiting for Player Responses') {
        // Check if the player has already submitted a response
        const playerName = this.playerName;
        const submittedResponses = state.submitted_responses || [];

        // Check if this player has already submitted a response
        const hasAlreadySubmitted = submittedResponses.some(
          response => response.player_name === playerName
        );

        // IMPORTANT: Update hasSubmitted based on the game state
        // This ensures it correctly reflects whether the player has submitted
        // regardless of local state
        this.hasSubmitted = hasAlreadySubmitted;
        console.log(`Setting hasSubmitted=${hasAlreadySubmitted} based on game state`);

        // Track the previous round to detect round changes
        const previousRound = this.gameState?.current_round || 0;
        const currentRound = state.current_round || 0;
        
        // If we're in a new round, make sure hasSubmitted is reset and log current responses
        if (previousRound !== currentRound && previousRound > 0) {
          console.log(`Round changed from ${previousRound} to ${currentRound}, resetting hasSubmitted`);
          console.log(`Current responses at round change:`, [...this.playerResponses]);
          this.hasSubmitted = false;
        }

        if (!hasAlreadySubmitted) {
          // Only reset selection if the player hasn't made a selection yet
          // or if the responses have changed
          if (this.lastSelectedResponseIndex === -1 ||
              (this.lastSelectedResponseIndex >= 0 &&
               this.lastSelectedResponseIndex < this.playerResponses.length &&
               this.playerResponses[this.lastSelectedResponseIndex] !== this.selectedResponse)) {
            this.selectedResponse = null;
            this.selectedResponseIndex = -1;
          } else {
            // Restore the last selected index if it's still valid
            this.selectedResponseIndex = this.lastSelectedResponseIndex;
            if (this.selectedResponseIndex >= 0 && this.selectedResponseIndex < this.playerResponses.length) {
              this.selectedResponse = this.playerResponses[this.selectedResponseIndex];
            }
          }
          // Don't reset custom response or response mode here
          // This was causing the issue where custom responses were lost when other players submitted
        }
      }

      // Reset winner selection when game status changes
      if (state && state.status === 'Ready for Judging') {
        this.selectedWinnerIndex = null;
      }

      // Handle game closed state
      if (state && state.status === 'Closed') {
        this.router.navigate(['/home']);
      }

      // Remove web client countdown - let Godot client handle the countdown and new round logic
      // The Godot client will now handle the countdown and automatically start new rounds

      // If in response phase, handle selection state based on submission status
      if (state && state.status === 'Waiting for Player Responses') {
        // Check if the player has already submitted a response
        const playerName = this.playerName;
        const submittedResponses = state.submitted_responses || [];          // Check if this player has already submitted a response
          const hasAlreadySubmitted = submittedResponses.some(
            response => response.player_name === playerName
          );

          // Log the current selection state before updating
          console.log('Current selection state before updating hasSubmitted:', {
            currentHasSubmitted: this.hasSubmitted,
            newHasSubmitted: hasAlreadySubmitted,
            selectedResponseIndex: this.selectedResponseIndex,
            selectedResponse: this.selectedResponse,
            selectedResponseText: this.selectedResponseText,
            responseCount: this.playerResponses.length
          });

        // Update submission status
        this.hasSubmitted = hasAlreadySubmitted;

        // Only reset if player has submitted
        if (hasAlreadySubmitted) {
          console.log('Player has submitted, resetting selection');
          this.selectedResponseIndex = -1;
          this.selectedResponse = null;
          this.selectedResponseText = null;
          this.lastSelectedResponseIndex = -1;
        } else {
          // If player hasn't submitted, preserve the current selection
          console.log('Player has not submitted, preserving selection');
          // No need to reset anything here, keep the current selection
        }
      }

      this.isProcessingGameStateUpdate = false;
    });

    // Subscribe to player responses
    this.playerResponsesSubscription = this.gameService.playerResponses$.subscribe(responses => {
      console.log('Received player responses update:', responses);

      // Store current response mode and custom response
      const currentResponseMode = this.responseMode;
      const currentCustomResponse = this.customResponse;

      // FIXED: Always process response updates to ensure cards are properly removed
      // Only skip in the middle of a game state update, but NOT when the player has submitted
      // This ensures the array gets updated after submission
      if (!this.isProcessingGameStateUpdate) {
        console.log('Processing player responses update (not in game state update)');
        
        // Save the current submission state
        const wasSubmitted = this.hasSubmitted;
        
        // Process the responses
        this.handlePlayerResponsesUpdate(responses);

        // Restore the submission state (in case handlePlayerResponsesUpdate changed it)
        this.hasSubmitted = wasSubmitted;

        // Restore response mode and custom response after update
        if (currentResponseMode === 'custom') {
          console.log('Restoring custom response mode and text:', currentCustomResponse);
          this.responseMode = 'custom';
          this.customResponse = currentCustomResponse;
        }
      } else {
        console.log('Skipping player responses update due to game state update in progress');
      }
    });

    // Subscribe to judge status
    this.isJudgeSubscription = this.gameService.isJudge$.subscribe(isJudge => {
      this.isJudge = isJudge;
    });
  }

  ngOnDestroy() {
    // Unsubscribe to prevent memory leaks
    if (this.gameStateSubscription) {
      this.gameStateSubscription.unsubscribe();
    }
    if (this.playerResponsesSubscription) {
      this.playerResponsesSubscription.unsubscribe();
    }
    if (this.isJudgeSubscription) {
      this.isJudgeSubscription.unsubscribe();
    }

    // Countdown functionality removed - handled by Godot client
  }

  // Handle response type change (cards/custom)
  responseTypeChanged() {
    console.log(`Response mode changed to: ${this.responseMode}`);

    // Store the current response mode
    this.lastResponseMode = this.responseMode;

    // Reset selections when switching modes
    if (this.responseMode === 'cards') {
      // Store the custom response before clearing it
      this.lastCustomResponse = this.customResponse;
      this.customResponse = '';
      // Don't reset selectedResponseIndex here
    } else {
      // Store the current selection before switching to custom mode
      this.lastSelectedResponseIndex = this.selectedResponseIndex;
      this.selectedResponseIndex = -1;
    }

    console.log(`Response mode change complete. Mode: ${this.responseMode}, Custom: ${this.customResponse}`);
  }

  // Countdown functionality removed - Godot client now handles countdown and new round logic

  // Start the game (leader only)
  startGame() {
    // Reset hidePrompt flag when starting a new game
    this.hidePrompt = false;
    console.log('Resetting hidePrompt flag when starting a new game');

    this.gameService.startNewGame();
  }

  // Submit a response
  submitResponse() {
    try {
      if (this.responseMode === 'cards') {
        // Submit pre-defined card response
        if (this.selectedResponseIndex < 0) {
          console.warn('Cannot submit: No response selected');
          return;
        }

        // Make sure we have the selected response text
        const responseText = this.playerResponses[this.selectedResponseIndex];
        if (!responseText) {
          console.warn('Cannot submit: Selected response text is empty');
          return;
        }

        console.log(`Submitting card response: "${responseText}" with index ${this.selectedResponseIndex}`);

        // Mark as submitted BEFORE sending to avoid race conditions
        this.hasSubmitted = true;

        // Send both the response text and the index
        this.gameService.submitResponse(responseText, this.selectedResponseIndex)
          .then(() => {
            // Track that this was the last submitted response
            console.log('Card response submitted successfully');
            
            // Force update player responses from the service
            const updatedResponses = this.gameService.getCurrentResponses();
            if (updatedResponses.length > 0) {
              console.log('Updated player responses after submission:', updatedResponses);
              this.playerResponses = [...updatedResponses];
            }
          })
          .catch(error => {
            console.error('Error submitting response:', error);
            this.hasSubmitted = false; // Reset submission state on error
          });
      } else {
        // Submit custom text response
        if (!this.customResponse?.trim()) {
          console.warn('Cannot submit: Custom response is empty');
          return;
        }

        console.log(`Submitting custom response: "${this.customResponse}"`);

        // Mark as submitted BEFORE sending to avoid race conditions
        this.hasSubmitted = true;

        // Send custom response
        this.gameService.submitResponse(this.customResponse.trim(), -1)
          .then(() => {
            console.log('Custom response submitted successfully');
          })
          .catch(error => {
            console.error('Error submitting custom response:', error);
            this.hasSubmitted = false; // Reset submission state on error
          });
      }

      // Reset selection after submission is initiated
      console.log('Response submission initiated, resetting selection state');
      this.selectedResponseIndex = -1;
      this.selectedResponse = null;
      this.selectedResponseText = null;
      this.lastSelectedResponseIndex = -1;
    } catch (error) {
      // If there was an error, revert the submitted state
      console.error('Error submitting response:', error);
      this.hasSubmitted = false;
    }
  }

  // Select a response (for player)
  selectResponse(index: number) {
    if (index < 0 || index >= this.playerResponses.length) {
      console.warn(`Invalid response index: ${index}, max index: ${this.playerResponses.length - 1}`);
      return;
    }

    this.selectedResponseIndex = index;
    this.selectedResponse = this.playerResponses[index];
    this.selectedResponseText = this.playerResponses[index];

    // Store the last selected index for restoration after updates
    this.lastSelectedResponseIndex = index;

    console.log(`Selected response at index ${index}: "${this.selectedResponseText}"`);
  }

  // Select a winner response (for judge)
  selectWinnerResponse(index: number) {
    this.selectedWinnerIndex = index;
    console.log(`Selected winner response at index ${index}: ${this.getSubmittedResponses()[index].text}`);
  }

  // Select a winner (judge only)
  selectWinner() {
    if (this.selectedWinnerIndex === null) return;

    this.gameService.selectWinner(this.selectedWinnerIndex);
  }

  // Skip judging (no winner)
  skipJudging() {
    this.gameService.skipJudging();
  }

  // Start next round (leader only)
  nextRound() {
    // Reset hidePrompt flag when starting a new round
    this.hidePrompt = false;
    console.log('Resetting hidePrompt flag when starting a new round');

    this.gameService.startNewGame();
  }

  // Play again with same players (leader only)
  playAgain() {
    // Reset hidePrompt flag when playing again
    this.hidePrompt = false;
    console.log('Resetting hidePrompt flag when playing again');

    this.gameService.resetGame();
  }

  // End the game (leader only)
  endGame() {
    this.gameService.closeGame();
  }

  // Leave the game (any player)
  async leaveGame() {
    const alert = await this.alertController.create({
      header: 'Leave Game',
      message: 'Are you sure you want to leave the game?',
      buttons: [
        {
          text: 'No',
          role: 'cancel',
          cssClass: 'secondary',
          handler: () => {
            console.log('Leave game cancelled');
          }
        }, {
          text: 'Yes',
          handler: () => {
            console.log('Confirming leave game');
            this.gameService.leaveGame().then(() => {
              this.router.navigate(['/home']);
            });
          }
        }
      ]
    });

    await alert.present();
  }

  // Get player score
  getPlayerScore(playerName: string): number {
    if (!this.gameState || !this.gameState.player_scores) return 0;
    return this.gameState.player_scores[playerName] || 0;
  }

  // Get the game winner
  getGameWinner(): string {
    if (!this.gameState || !this.gameState.player_scores || !this.gameState.players) {
      return 'Unknown';
    }

    let highestScore = 0;
    let winner = 'Unknown';

    for (const player of this.gameState.players) {
      const score = this.getPlayerScore(player);
      if (score > highestScore) {
        highestScore = score;
        winner = player;
      }
    }

    return winner;
  }

  // Safe access methods to prevent null errors
  getPlayers(): string[] {
    return this.gameState?.players || [];
  }

  getCurrentRound(): number {
    return this.gameState?.current_round || 1;
  }

  getPrompt(): string {
    // If hidePrompt is true, return empty string to hide the prompt
    if (this.hidePrompt) {
      console.log('getPrompt() returning empty string because hidePrompt is true');
      return '';
    }

    // If we're in Winner Chosen state, hide the prompt
    if (this.gameState?.status === 'Winner Chosen') {
      console.log('getPrompt() returning empty string because we are in Winner Chosen state');
      return '';
    }

    console.log('GameState in getPrompt():', this.gameState?.status);
    console.log('Current prompt object:', this.gameState?.current_prompt);

    const prompt = this.gameState?.current_prompt?.prompt || '';
    console.log('PROMPT TEXT FROM FIREBASE:', prompt);

    // If we have a prompt, return it (regardless of state)
    if (prompt) {
      return prompt;
    }

    // Otherwise return an empty string
    return '';
  }

  getSubmittedResponses(): any[] {
    return this.gameState?.submitted_responses || [];
  }

  // Getters
  get isLeader(): boolean {
    return this.gameService.isLeader;
  }

  get playerName(): string {
    return this.gameService.playerName || 'Unknown';
  }

  get roomCode(): string {
    return this.gameState?.room_code || '';
  }

  // Create a new method to handle player responses update
  private handlePlayerResponsesUpdate(responses: string[]) {
    console.log('Handling player responses update:', responses);
    console.log('IMPORTANT - Current state of hasSubmitted:', this.hasSubmitted);

    // Store current selection and state before updating
    const previousSelectedText = this.selectedResponseText;
    const previousSelectedIndex = this.selectedResponseIndex;
    const currentResponseMode = this.responseMode;
    const currentCustomResponse = this.customResponse;

    // FIXED: Always process response updates to ensure cards are properly removed
    // We no longer skip updates when the player has submitted

    // Log the current state before updating
    console.log('Current state before update:', {
      previousSelectedText,
      previousSelectedIndex,
      responseMode: currentResponseMode,
      customResponse: currentCustomResponse,
      hasSubmitted: this.hasSubmitted,
      currentResponses: [...this.playerResponses]
    });

    // Update the responses array
    this.playerResponses = [...responses]; // Create a new array to ensure change detection

    // If we had a previous selection, try to restore it
    if (previousSelectedText && previousSelectedIndex >= 0 && currentResponseMode === 'cards') {
      console.log('Attempting to restore previous card selection:', previousSelectedText);

      // First try to find the exact same text
      const newIndex = this.playerResponses.findIndex(text => text === previousSelectedText);

      if (newIndex >= 0) {
        console.log('Found matching text at new index:', newIndex);
        this.selectedResponseIndex = newIndex;
        this.selectedResponse = previousSelectedText;
        this.selectedResponseText = previousSelectedText; // Ensure this is also updated
      }
      // If not found but the previous index is still valid, use it
      else if (previousSelectedIndex < this.playerResponses.length) {
        console.log('Using previous index with new text:', previousSelectedIndex);
        this.selectedResponseIndex = previousSelectedIndex;
        this.selectedResponse = this.playerResponses[previousSelectedIndex];
        this.selectedResponseText = this.selectedResponse;
      }
      // Otherwise reset selection
      else {
        console.log('Could not restore selection, resetting');
        this.selectedResponseIndex = -1;
        this.selectedResponse = null;
        this.selectedResponseText = null;
      }
    }

    // Log the state after updating
    console.log('State after update:', {
      selectedResponseIndex: this.selectedResponseIndex,
      selectedResponse: this.selectedResponse,
      selectedResponseText: this.selectedResponseText,
      responseMode: this.responseMode,
      customResponse: this.customResponse,
      playerResponses: [...this.playerResponses]
    });
  }
}

