import { Injectable } from '@angular/core';
import { initializeApp } from 'firebase/app';
import {
  getAuth,
  signInAnonymously,
  onAuthStateChanged,
  Auth,
  User
} from 'firebase/auth';
import {
  getDatabase,
  ref,
  set,
  get,
  onValue,
  update,
  Database
} from 'firebase/database';
import { BehaviorSubject } from 'rxjs';
import { environment } from '../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class FirebaseService {
  private app = initializeApp(environment.firebaseConfig);
  private auth: Auth;
  private db: Database;

  private userSubject = new BehaviorSubject<User | null>(null);
  public user$ = this.userSubject.asObservable();

  private gameStateSubject = new BehaviorSubject<any>(null);
  public gameState$ = this.gameStateSubject.asObservable();

  private gameId: string | null = null;
  private playerName: string | null = null;
  private isLeader = false;

  constructor() {
    this.auth = getAuth(this.app);
    this.db = getDatabase(this.app);

    // Listen for auth state changes
    onAuthStateChanged(this.auth, (user) => {
      this.userSubject.next(user);

      // If we have a game ID and user, set up game state listener
      if (this.gameId && user) {
        this.setupGameListener();
      }
    });

    // Check for stored player info
    this.loadPlayerInfo();
  }

  // Sign in anonymously
  async signIn(): Promise<User> {
    try {
      const result = await signInAnonymously(this.auth);
      return result.user;
    } catch (error) {
      console.error('Error signing in:', error);
      throw error;
    }
  }

  // Join a game with room code
  async joinGame(roomCode: string, playerName: string): Promise<boolean> {
    try {
      console.log(`Attempting to join game with room code: ${roomCode}, player name: ${playerName}`);

      // Make sure user is signed in
      if (!this.auth.currentUser) {
        console.log('User not signed in, signing in anonymously');
        const user = await this.signIn();
        console.log('Anonymous sign-in successful, user:', user.uid);
      } else {
        console.log('User already signed in:', this.auth.currentUser.uid);
      }

      // Convert room code to uppercase
      roomCode = roomCode.toUpperCase();
      console.log(`Looking for game with room code: ${roomCode}`);

      // Check if game exists
      const gamesRef = ref(this.db, '/');
      const gamesSnapshot = await get(gamesRef);

      if (!gamesSnapshot.exists()) {
        console.error('No games found in database');
        return false;
      }

      console.log('Games found in database, searching for matching room code');

      // Find game with matching room code
      let gameId = null;

      gamesSnapshot.forEach((childSnapshot) => {
        const gameData = childSnapshot.val();
        console.log(`Checking game ${childSnapshot.key}, room code: ${gameData.room_code}`);

        if (gameData.room_code === roomCode) {
          gameId = childSnapshot.key;
          console.log(`Found matching game with ID: ${gameId}`);
          return true; // Break the forEach loop
        }
        return false;
      });

      if (!gameId) {
        console.error(`No game found with room code: ${roomCode}`);
        return false;
      }

      // Store game ID and player name
      this.gameId = gameId;
      this.playerName = playerName;
      console.log(`Storing game ID: ${gameId} and player name: ${playerName}`);
      this.savePlayerInfo();

      // Add player to game
      console.log(`Adding player ${playerName} to game ${gameId}`);
      await this.addPlayerToGame(playerName);

      // Set up game state listener
      console.log('Setting up game state listener');
      this.setupGameListener();

      // Manually fetch the initial game state to ensure we have it
      console.log('Manually fetching initial game state');
      const gameRef = ref(this.db, `${gameId}`);
      const gameSnapshot = await get(gameRef);

      if (gameSnapshot.exists()) {
        const gameData = gameSnapshot.val();
        console.log('Initial game state fetched:', gameData);
        this.gameStateSubject.next(gameData);
      } else {
        console.error('Failed to fetch initial game state');
      }

      console.log('Join game process completed successfully');
      return true;
    } catch (error) {
      console.error('Error joining game:', error);
      return false;
    }
  }

  // Add player to game
  private async addPlayerToGame(playerName: string): Promise<void> {
    if (!this.gameId) {
      console.error('Cannot add player to game: gameId is null');
      return;
    }

    try {
      console.log(`Adding player ${playerName} to game ${this.gameId}`);

      // Get current players
      const playersRef = ref(this.db, `${this.gameId}/players`);
      const playersSnapshot = await get(playersRef);

      let players: string[] = [];
      if (playersSnapshot.exists()) {
        players = playersSnapshot.val() || [];
        console.log('Current players:', players);
      } else {
        console.log('No players found, creating new players array');
      }

      // Check if player is already in the game
      if (!players.includes(playerName)) {
        console.log(`Player ${playerName} not in game, adding to players array`);
        players.push(playerName);
        await set(playersRef, players);
        console.log(`Player ${playerName} added to game`);
      } else {
        console.log(`Player ${playerName} already in game`);
      }

      // Check if this is the first player (leader)
      this.isLeader = players.length === 1 || players[0] === playerName;
      console.log(`Player ${playerName} is ${this.isLeader ? 'the leader' : 'not the leader'}`);

      // Initialize player score if not already set
      const playerScoreRef = ref(this.db, `${this.gameId}/player_scores/${playerName}`);
      const scoreSnapshot = await get(playerScoreRef);

      if (!scoreSnapshot.exists()) {
        console.log(`Initializing score for player ${playerName}`);
        await set(playerScoreRef, 0);
      }

    } catch (error) {
      console.error('Error adding player to game:', error);
    }
  }

  // Set up listener for game state changes
  private setupGameListener(): void {
    if (!this.gameId) {
      console.error('Cannot setup game listener: gameId is null');
      return;
    }

    console.log(`Setting up game listener for gameId: ${this.gameId}`);

    const gameRef = ref(this.db, `${this.gameId}`);
    onValue(gameRef, (snapshot) => {
      console.log('Game data received from Firebase:', snapshot.exists() ? 'Data exists' : 'No data');

      const gameData = snapshot.val();
      if (gameData) {
        console.log('Game status:', gameData.status);
        console.log('Game data:', gameData);
        this.gameStateSubject.next(gameData);
      } else {
        console.error('Game data is null or undefined');
      }
    }, (error) => {
      console.error('Error in game listener:', error);
    });

    console.log('Game listener setup complete');
  }

  // Update game status
  async updateGameStatus(status: string): Promise<void> {
    if (!this.gameId) return;

    try {
      console.log(`===== UPDATING GAME STATUS TO: ${status} =====`);

      // Get current game state to log for debugging
      const gameRef = ref(this.db, `${this.gameId}`);
      const gameSnapshot = await get(gameRef);

      if (gameSnapshot.exists()) {
        const gameData = gameSnapshot.val();
        console.log(`Current game state before status update:`, {
          currentStatus: gameData.status,
          currentJudgeIndex: gameData.current_judge_index || 0,
          players: gameData.players || [],
          currentRound: gameData.current_round || 1
        });
      }

      // Update the status
      const statusRef = ref(this.db, `${this.gameId}/status`);
      await set(statusRef, status);
      console.log(`Game status updated to: ${status}`);

      console.log(`===== END UPDATING GAME STATUS =====`);
    } catch (error) {
      console.error('Error updating game status:', error);
      console.log(`===== END UPDATING GAME STATUS (WITH ERROR) =====`);
    }
  }

  // Update prompt - DISABLED: Only Godot client should update prompts
  // This function is kept for reference but should not be used
  async updatePrompt(_prompt: string): Promise<void> {
    console.warn('WARNING: Web client should not update prompts. This is handled by the Godot client.');
    return;
  }

  // Update current prompt to "Incoming Prompt!"
  // This is used when transitioning to "Waiting for Prompt" state
  async updateCurrentPromptToIncoming(): Promise<void> {
    if (!this.gameId) return;

    try {
      console.log(`===== UPDATING CURRENT PROMPT TO "Incoming Prompt!" =====`);

      // Set the current_prompt to an object with "Incoming Prompt!" as the prompt
      const promptRef = ref(this.db, `${this.gameId}/current_prompt`);
      await set(promptRef, { prompt: "Incoming Prompt!" });

      console.log(`Current prompt updated to "Incoming Prompt!"`);
      console.log(`===== END UPDATING CURRENT PROMPT =====`);
    } catch (error) {
      console.error('Error updating current prompt:', error);
      console.log(`===== END UPDATING CURRENT PROMPT (WITH ERROR) =====`);
    }
  }

  // Submit player response
  async submitResponse(responseText: string, responseIndex: number): Promise<void> {
    if (!this.gameId || !this.playerName) return;

    try {
      console.log(`==== SUBMITTING RESPONSE IN FIREBASE SERVICE ====`);
      console.log(`Player ${this.playerName} is submitting response index ${responseIndex}: "${responseText}"`);

      // 1. Add to submitted responses
      const responsesRef = ref(this.db, `${this.gameId}/submitted_responses`);
      const responsesSnapshot = await get(responsesRef);

      let responses: any[] = [];
      if (responsesSnapshot.exists()) {
        responses = responsesSnapshot.val() || [];
      }

      // Check if this player has already submitted a response
      const existingResponseIndex = responses.findIndex(r => r.player_name === this.playerName);

      if (existingResponseIndex >= 0) {
        // Replace the existing response
        console.log(`Player ${this.playerName} already submitted a response, replacing it`);
        responses[existingResponseIndex] = {
          text: responseText,
          player_name: this.playerName
        };
      } else {
        // Add a new response
        console.log(`Adding new response for player ${this.playerName}`);
        responses.push({
          text: responseText,
          player_name: this.playerName
        });
      }

      await set(responsesRef, responses);
      console.log(`Updated submitted_responses:`, responses);

      // We no longer need to update player_response_texts, player_responses, or response_indices
      // as the web client is now managing responses locally

      console.log(`==== END SUBMITTING RESPONSE IN FIREBASE SERVICE ====`);

      // Check if all players have submitted responses after a short delay
      // This ensures Firebase has time to update before we check
      setTimeout(() => {
        console.log('Checking if all players have submitted responses after a short delay');
        this.checkAllResponsesSubmitted();
      }, 500);
    } catch (error) {
      console.error('Error submitting response:', error);
      throw error;
    }
  }

  // Check if all players have submitted responses
  private async checkAllResponsesSubmitted(): Promise<void> {
    if (!this.gameId) return;

    try {
      console.log(`===== CHECKING ALL RESPONSES SUBMITTED =====`);

      // Get current game state
      const gameRef = ref(this.db, `${this.gameId}`);
      const gameSnapshot = await get(gameRef);

      if (!gameSnapshot.exists()) {
        console.log(`Game data doesn't exist, aborting check`);
        console.log(`===== END CHECKING ALL RESPONSES SUBMITTED =====`);
        return;
      }

      const gameData = gameSnapshot.val();

      // Only proceed if we're in the right state
      if (gameData.status !== 'Waiting for Player Responses') {
        console.log(`Game status is ${gameData.status}, not checking responses`);
        console.log(`===== END CHECKING ALL RESPONSES SUBMITTED =====`);
        return;
      }

      const players = gameData.players || [];
      const responses = gameData.submitted_responses || [];

      // Find the judge
      const judgeIndex = gameData.current_judge_index || 0;
      const judge = players[judgeIndex];

      // Count non-judge players
      const nonJudgePlayers = players.filter((p: string) => p !== judge);

      console.log('Response check:', {
        totalPlayers: players.length,
        nonJudgePlayers: nonJudgePlayers.length,
        submittedResponses: responses.length,
        judge: judge
      });

      // Special case: if there's only one player (plus judge), auto-advance
      if (nonJudgePlayers.length === 0) {
        console.log('No non-judge players, auto-advancing to Ready for Judging');
        await this.updateGameStatus('Ready for Judging');
        console.log(`===== END CHECKING ALL RESPONSES SUBMITTED =====`);
        return;
      }

      // Special case: if all players have submitted (equal number of responses and non-judge players)
      // This is a simpler check that should work in most cases
      if (responses.length >= nonJudgePlayers.length) {
        console.log('All players appear to have submitted responses, changing status to Ready for Judging');
        await this.updateGameStatus('Ready for Judging');
        console.log(`===== END CHECKING ALL RESPONSES SUBMITTED =====`);
        return;
      }

      // More detailed check if the simple check didn't pass
      // Check if each player has submitted exactly one response
      const playerResponseCount: { [key: string]: number } = {};

      // Initialize counts to zero
      nonJudgePlayers.forEach((player: string) => {
        playerResponseCount[player] = 0;
      });

      // Count responses for each player
      responses.forEach((response: { player_name: string; text: string }) => {
        if (playerResponseCount[response.player_name] !== undefined) {
          playerResponseCount[response.player_name]++;
        }
      });

      // Check if all non-judge players have submitted at least one response
      const allPlayersHaveAtLeastOneSubmission = Object.values(playerResponseCount).every(count => count > 0);

      console.log('Player response counts:', playerResponseCount);
      console.log('All players have at least one submission:', allPlayersHaveAtLeastOneSubmission);

      // Update status if all non-judge players have submitted at least one response
      if (allPlayersHaveAtLeastOneSubmission) {
        console.log('All players have submitted at least one response, changing status to Ready for Judging');
        await this.updateGameStatus('Ready for Judging');
      } else {
        console.log('Waiting for more players to submit responses');
      }

      console.log(`===== END CHECKING ALL RESPONSES SUBMITTED =====`);
    } catch (error) {
      console.error('Error checking responses:', error);
      console.log(`===== END CHECKING ALL RESPONSES SUBMITTED (WITH ERROR) =====`);
    }
  }

  // Judge selects a winning response
  async selectWinner(responseIndex: number): Promise<void> {
    if (!this.gameId) return;

    try {
      console.log(`===== SELECT WINNER CALLED FOR RESPONSE INDEX ${responseIndex} =====`);

      // Get the response at the given index
      const responsesRef = ref(this.db, `${this.gameId}/submitted_responses/${responseIndex}`);
      const responseSnapshot = await get(responsesRef);

      if (!responseSnapshot.exists()) {
        console.log(`Response at index ${responseIndex} does not exist, aborting selectWinner`);
        console.log(`===== END SELECT WINNER (ABORTED) =====`);
        return;
      }

      const response = responseSnapshot.val();
      console.log(`Selected winning response:`, response);

      // Update player score
      const playerScoreRef = ref(this.db, `${this.gameId}/player_scores/${response.player_name}`);
      const scoreSnapshot = await get(playerScoreRef);

      let currentScore = 0;
      if (scoreSnapshot.exists()) {
        currentScore = scoreSnapshot.val() || 0;
      }

      // Calculate new score
      const newScore = currentScore + 1;
      console.log(`Updating score for ${response.player_name} from ${currentScore} to ${newScore}`);

      // Check if player has reached 5 points (game over)
      const gameOver = newScore >= 5;
      console.log(`Game over check: ${gameOver} (${newScore} >= 5)`);

      // Update winner in the game
      const updates = {
        [`${this.gameId}/winner`]: response.player_name,
        [`${this.gameId}/winning_response`]: response.text,
        [`${this.gameId}/player_scores/${response.player_name}`]: newScore,
        [`${this.gameId}/status`]: gameOver ? 'Game Over' : 'Winner Chosen'
      };

      // If game is over, also set the game winner
      if (gameOver) {
        updates[`${this.gameId}/game_winner`] = response.player_name;
        console.log(`Game over! ${response.player_name} has won with ${newScore} points!`);
      }

      console.log(`Updating Firebase with winner data:`, updates);
      await update(ref(this.db), updates);
      console.log(`Firebase update completed`);

      // Godot client now handles the countdown and new round logic
      // No need for web client timeout

      console.log(`===== END SELECT WINNER =====`);
    } catch (error) {
      console.error('Error selecting winner:', error);
      console.log(`===== END SELECT WINNER (WITH ERROR) =====`);
    }
  }

  // Skip judging (can be called by any player)
  async skipJudging(): Promise<void> {
    if (!this.gameId) return;

    try {
      console.log(`===== SKIP JUDGING CALLED =====`);

      // Update the game status to "Winner Chosen" with no winner
      const updates = {
        [`${this.gameId}/winner`]: "No Winner",
        [`${this.gameId}/winning_response`]: "Judging was skipped",
        [`${this.gameId}/status`]: 'Winner Chosen'
      };

      console.log(`Updating Firebase with skip data:`, updates);
      await update(ref(this.db), updates);
      console.log(`Firebase update completed`);

      console.log('Judging skipped, no winner selected');

      // Godot client now handles the countdown and new round logic
      // No need for web client timeout

      console.log(`===== END SKIP JUDGING =====`);
    } catch (error) {
      console.error('Error skipping judging:', error);
      console.log(`===== END SKIP JUDGING (WITH ERROR) =====`);
    }
  }

  // Helper method to check game status before starting a new round
  // This helps prevent race conditions where multiple calls to startNewGame might occur
  private async checkGameStatusBeforeNewRound(): Promise<void> {
    if (!this.gameId) return;

    try {
      console.log(`===== CHECKING GAME STATUS BEFORE NEW ROUND =====`);

      // Get current game state
      const gameRef = ref(this.db, `${this.gameId}`);
      const gameSnapshot = await get(gameRef);

      if (!gameSnapshot.exists()) {
        console.log('Game data does not exist, aborting check');
        console.log(`===== END CHECKING GAME STATUS (ABORTED) =====`);
        return;
      }

      const gameData = gameSnapshot.val();
      console.log(`Current game status: ${gameData.status}`);

      // Only proceed if we're still in Winner Chosen state
      // This prevents multiple calls to startNewGame
      if (gameData.status === 'Winner Chosen') {
        console.log('Game is still in Winner Chosen state, starting new round');

        // Update the current prompt to "Incoming Prompt!"
        await this.updateCurrentPromptToIncoming();
        console.log('Current prompt updated to "Incoming Prompt!" before starting new game');

        await this.startNewGame();
      } else {
        console.log(`Game is in ${gameData.status} state, not starting new round`);
      }

      console.log(`===== END CHECKING GAME STATUS =====`);
    } catch (error) {
      console.error('Error checking game status:', error);
      console.log(`===== END CHECKING GAME STATUS (WITH ERROR) =====`);
    }
  }

  // Leader starts a new game
  async startNewGame(): Promise<void> {
    if (!this.gameId || !this.isLeader) return;

    try {
      console.log(`===== START NEW GAME CALLED =====`);

      // Get current game state
      const gameRef = ref(this.db, `${this.gameId}`);
      const gameSnapshot = await get(gameRef);

      if (!gameSnapshot.exists()) {
        console.log('Game data does not exist, aborting startNewGame');
        console.log(`===== END START NEW GAME (ABORTED) =====`);
        return;
      }

      const gameData = gameSnapshot.val();

      // Get current judge index and players array
      const currentJudgeIndex = gameData.current_judge_index || 0;
      const players = gameData.players || [];

      // Log detailed information about the current state
      console.log('Current game state:', {
        currentJudgeIndex,
        players,
        currentRound: gameData.current_round || 1,
        status: gameData.status
      });

      // Validate players array
      if (!players || players.length < 2) {
        console.error('Invalid players array:', players);
        console.log(`===== END START NEW GAME (INVALID PLAYERS) =====`);
        return;
      }

      // Calculate the next judge index (round-robin style)
      let nextJudgeIndex = currentJudgeIndex + 1;
      if (nextJudgeIndex >= players.length) {
        nextJudgeIndex = 0;
      }

      console.log(`Updating judge from ${players[currentJudgeIndex]} (index ${currentJudgeIndex}) to ${players[nextJudgeIndex]} (index ${nextJudgeIndex})`);
      console.log(`Full players array: ${JSON.stringify(players)}`);

      // Update the judge index, clear submitted responses, and increment round
      const updates = {
        [`${this.gameId}/current_judge_index`]: nextJudgeIndex,
        [`${this.gameId}/submitted_responses`]: [],
        [`${this.gameId}/current_round`]: (gameData.current_round || 0) + 1
      };

      // Clear any response indices, player_response_texts, and player_responses
      // This ensures we don't have any leftover data from the old approach
      updates[`${this.gameId}/response_indices`] = null;
      updates[`${this.gameId}/player_response_texts`] = null;
      updates[`${this.gameId}/player_responses`] = null;
      updates[`${this.gameId}/used_responses`] = null;

      await update(ref(this.db), updates);

      console.log('Updated judge index, cleared responses, and incremented round');

      // Update the current prompt to "Incoming Prompt!"
      await this.updateCurrentPromptToIncoming();
      console.log('Current prompt updated to "Incoming Prompt!" before changing game status');

      // Set game status to "Waiting for Prompt" to let Godot client know to choose a prompt
      await this.updateGameStatus('Waiting for Prompt');

      console.log(`===== END START NEW GAME =====`);
    } catch (error) {
      console.error('Error starting new game:', error);
      console.log(`===== END START NEW GAME (WITH ERROR) =====`);
    }
  }

  // Leader resets the game
  async resetGame(): Promise<void> {
    if (!this.gameId || !this.isLeader) return;

    try {
      // Reset player scores
      const playerScoresRef = ref(this.db, `${this.gameId}/player_scores`);
      await set(playerScoresRef, {});

      // Reset other game state
      const updates = {
        [`${this.gameId}/current_round`]: 1,
        [`${this.gameId}/current_judge_index`]: 0, // Reset judge index to 0
        [`${this.gameId}/submitted_responses`]: [],
        [`${this.gameId}/winner`]: null,
        [`${this.gameId}/winning_response`]: null,
        [`${this.gameId}/game_winner`]: null, // Clear the game winner
        // IMPORTANT: Set status to "Waiting for Prompt" instead of "Ready"
        // This ensures the Godot client will select a new prompt
        [`${this.gameId}/status`]: 'Waiting for Prompt'
      };

      // Clear any response indices, player_response_texts, and player_responses
      // This ensures we don't have any leftover data from the old approach
      updates[`${this.gameId}/response_indices`] = null;
      updates[`${this.gameId}/player_response_texts`] = null;
      updates[`${this.gameId}/player_responses`] = null;
      updates[`${this.gameId}/used_responses`] = null;

      await update(ref(this.db), updates);

      // Update the current prompt to "Incoming Prompt!"
      await this.updateCurrentPromptToIncoming();
      console.log('Current prompt updated to "Incoming Prompt!"');

      console.log('Game reset: judge index set to 0, round set to 1, scores reset, waiting for new prompt');
    } catch (error) {
      console.error('Error resetting game:', error);
    }
  }

  // Leader closes the game
  async closeGame(): Promise<void> {
    if (!this.gameId || !this.isLeader) return;

    try {
      await this.updateGameStatus('Closed');
    } catch (error) {
      console.error('Error closing game:', error);
    }
  }

  // Save player info to local storage
  private savePlayerInfo(): void {
    if (this.gameId && this.playerName) {
      localStorage.setItem('gameId', this.gameId);
      localStorage.setItem('playerName', this.playerName);
      localStorage.setItem('isLeader', String(this.isLeader));
    }
  }

  // Load player info from local storage
  private loadPlayerInfo(): void {
    console.log('Loading player info from localStorage');

    this.gameId = localStorage.getItem('gameId');
    this.playerName = localStorage.getItem('playerName');
    this.isLeader = localStorage.getItem('isLeader') === 'true';

    console.log(`Loaded gameId: ${this.gameId || 'null'}`);
    console.log(`Loaded playerName: ${this.playerName || 'null'}`);
    console.log(`Loaded isLeader: ${this.isLeader}`);

    // If we have a gameId but no game state listener, set it up
    if (this.gameId && this.auth.currentUser) {
      console.log('Found existing game session, setting up game listener');
      this.setupGameListener();

      // Manually fetch the initial game state
      this.verifyGameData().then(() => {
        console.log('Initial game state verified after loading player info');
      });
    }
  }

  // Clear player info from local storage
  clearPlayerInfo(): void {
    localStorage.removeItem('gameId');
    localStorage.removeItem('playerName');
    localStorage.removeItem('isLeader');
    this.gameId = null;
    this.playerName = null;
    this.isLeader = false;
  }

  // Getters
  get currentGameId(): string | null {
    return this.gameId;
  }

  get currentPlayerName(): string | null {
    return this.playerName;
  }

  get isGameLeader(): boolean {
    return this.isLeader;
  }

  // Verify and fetch the latest game data directly from Firebase
  async verifyGameData(): Promise<void> {
    if (!this.gameId) return;

    try {
      console.log(`===== VERIFYING GAME DATA =====`);

      // Get the latest game data directly from Firebase
      const gameRef = ref(this.db, `${this.gameId}`);
      const snapshot = await get(gameRef);

      if (!snapshot.exists()) {
        console.log(`Game data doesn't exist, aborting verification`);
        console.log(`===== END VERIFYING GAME DATA =====`);
        return;
      }

      // Get the latest game data
      const gameData = snapshot.val();

      // Log the current prompt data for debugging
      console.log('Current prompt in Firebase:', gameData.current_prompt);

      // Manually trigger the game state update to ensure our local state is in sync
      this.gameStateSubject.next(gameData);

      // If no prompt is found and we're in Ready or Waiting for Player Responses state, log a warning
      if ((!gameData.current_prompt || !gameData.current_prompt.prompt) &&
          (gameData.status === 'Ready' || gameData.status === 'Waiting for Player Responses')) {
        console.warn('No prompt found in Firebase during verification, game may need to restart prompt selection');

        // If we're the leader, we could potentially trigger a new prompt selection
        if (this.isLeader && (gameData.status === 'Ready' || gameData.status === 'Waiting for Player Responses')) {
          console.log('Leader detected missing prompt, falling back to Waiting for Prompt state');
          await this.updateGameStatus('Waiting for Prompt');
        }
      }

      console.log(`===== END VERIFYING GAME DATA =====`);
    } catch (error) {
      console.error('Error verifying game data:', error);
    }
  }

  // Method updatePlayerResponses has been removed
  // Player responses are now managed locally by the web client
  // This reduces unnecessary database writes

  // Remove player from game
  async removePlayerFromGame(): Promise<void> {
    if (!this.gameId || !this.playerName) return;

    try {
      console.log(`===== REMOVING PLAYER FROM GAME =====`);
      console.log(`Removing player ${this.playerName} from game ${this.gameId}`);

      // Get current players
      const playersRef = ref(this.db, `${this.gameId}/players`);
      const playersSnapshot = await get(playersRef);

      if (!playersSnapshot.exists()) {
        console.log('No players found, nothing to remove');
        console.log(`===== END REMOVING PLAYER FROM GAME =====`);
        return;
      }

      let players: string[] = playersSnapshot.val() || [];
      console.log('Current players:', players);

      // Check if player is in the game
      const playerIndex = players.indexOf(this.playerName);
      if (playerIndex === -1) {
        console.log(`Player ${this.playerName} not found in game`);
        console.log(`===== END REMOVING PLAYER FROM GAME =====`);
        return;
      }

      // Remove player from array
      players.splice(playerIndex, 1);
      console.log('Updated players array:', players);

      // Update players array in Firebase
      await set(playersRef, players);
      console.log(`Player ${this.playerName} removed from game`);

      // Clear player info from local storage
      this.clearPlayerInfo();

      console.log(`===== END REMOVING PLAYER FROM GAME =====`);
    } catch (error) {
      console.error('Error removing player from game:', error);
      console.log(`===== END REMOVING PLAYER FROM GAME (WITH ERROR) =====`);
    }
  }
}



