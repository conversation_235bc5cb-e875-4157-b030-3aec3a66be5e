; Engine configuration file.
; It's best edited using the editor UI and not directly,
; since the parameters that go here are not all obvious.
;
; Format:
;   [section] ; section goes between []
;   param=value ; assign values to parameters

config_version=5

[application]

config/name="CJSC"
run/main_scene="res://menu.tscn"
config/features=PackedStringArray("4.4", "Forward Plus")
config/icon="res://icon.svg"

[autoload]

FirebaseManager="*res://cardGame/firebase_manager.gd"
MultiplayerManager="*res://cardGame/multiplayer_manager.gd"

[display]

window/size/mode=3

[editor_plugins]

enabled=PackedStringArray("res://addons/qr_code/plugin.cfg")
