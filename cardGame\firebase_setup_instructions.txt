Firebase Setup Instructions
==========================

To allow the game to connect to Firebase, you need to configure your Firebase project with the following settings:

1. Authentication:
   - Enable Anonymous Authentication in the Firebase Console
   - Go to Authentication > Sign-in method > Anonymous > Enable

2. Realtime Database Rules:
   - Set the following rules to allow read/write access:

```json
{
  "rules": {
    ".read": true,
    ".write": true,
    ".indexOn": ["room_code"]
  }
}
```

Note: These rules allow anyone to read and write to your database. For a production app, you should use more restrictive rules.

3. API Key:
   - Make sure the API key in firebase_manager.gd matches your Firebase project's API key
   - You can find your API key in the Firebase Console under Project Settings > General > Web API Key

4. Project ID and Database URL:
   - Make sure the PROJECT_ID and DATABASE_URL in firebase_manager.gd match your Firebase project
   - You can find these in the Firebase Console under Project Settings > General and Realtime Database

If you're still having issues connecting to Firebase, check the console output for detailed error messages.

