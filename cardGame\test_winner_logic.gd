extends Node

# Test script to verify winner display logic

func _ready():
	print("Testing winner display logic...")
	
	# Test case 1: Valid winner and response
	test_winner_display("<PERSON>", "This is a funny response")
	
	# Test case 2: Empty winner
	test_winner_display("", "This is a funny response")
	
	# Test case 3: Empty response
	test_winner_display("Alice", "")
	
	# Test case 4: "No winner available" message
	test_winner_display("No winner available", "This is a funny response")
	
	# Test case 5: "No winning response available" message
	test_winner_display("Alice", "No winning response available")

func test_winner_display(winner_name: String, winning_response: String):
	print("\n--- Testing winner display ---")
	print("Winner: '", winner_name, "'")
	print("Response: '", winning_response, "'")
	
	var should_display = (winner_name != "" and winner_name != "No winner available" and 
	                     winning_response != "" and winning_response != "No winning response available")
	
	print("Should display: ", should_display)
	
	if should_display:
		print("✓ Would display winner")
	else:
		print("✗ Would NOT display winner")
