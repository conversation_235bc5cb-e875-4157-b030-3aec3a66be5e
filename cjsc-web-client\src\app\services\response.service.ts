import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';

// Define interfaces for our data structures
interface ResponseObject {
  text?: string;
  response?: string;
  nsfw?: string | boolean;
  [key: string]: any; // Allow any other properties
}

interface ResponseData {
  responses?: ResponseObject[];
  [key: string]: any; // Allow any other properties
}

@Injectable({
  providedIn: 'root'
})
export class ResponseService {
  private responsesSubject = new BehaviorSubject<string[]>([]);
  public responses$ = this.responsesSubject.asObservable();

  private cachedResponses: string[] = [];
  private localStorageKey = 'cardGameResponses';

  constructor(private http: HttpClient) {
    // Clear any existing cached data to ensure we get fresh data
    localStorage.removeItem(this.localStorageKey);

    // Try to load responses from local storage first
    this.loadFromLocalStorage();
  }

  /**
   * Load responses from local storage if available
   */
  private loadFromLocalStorage(): void {
    const storedResponses = localStorage.getItem(this.localStorageKey);
    if (storedResponses) {
      try {
        this.cachedResponses = JSON.parse(storedResponses);
        this.responsesSubject.next(this.cachedResponses);
        console.log('Loaded responses from local storage:', this.cachedResponses.length);
      } catch (error) {
        console.error('Error parsing stored responses:', error);
        this.loadFromAssets();
      }
    } else {
      this.loadFromAssets();
    }
  }

  /**
   * Load responses from assets
   */
  private loadFromAssets(): void {
    console.log('Loading responses from assets...');
    this.http.get<ResponseData>('assets/cardGame/responses.json')
      .pipe(
        map(data => {
          // Handle different response formats
          let responseObjects: (ResponseObject | string)[] = [];

          console.log('Raw response data:', data);

          if (Array.isArray(data)) {
            console.log('Data is an array');
            responseObjects = data;
          } else if (data.responses && Array.isArray(data.responses)) {
            console.log('Data has responses array property');
            responseObjects = data.responses;
          } else {
            console.log('Data is an object, extracting values');
            responseObjects = Object.values(data);
          }

          console.log('Response objects extracted:', responseObjects.length);

          // Extract the response text from each object
          const extractedResponses = responseObjects.map((response: ResponseObject | string | any) => {
            if (typeof response === 'string') {
              return response;
            } else if (response && typeof response === 'object') {
              // If it's an object with a text property, return that
              if (response.text) {
                return response.text;
              }
              // If it's an object with a response property, return that
              if (response.response) {
                return response.response;
              }
              // Otherwise, stringify the object for debugging
              return JSON.stringify(response);
            }
            return 'Invalid response';
          });

          console.log('Extracted response texts:', extractedResponses.length);

          // Filter out any empty or invalid responses
          return extractedResponses.filter(text => text && text !== 'Invalid response');
        }),
        tap(responses => {
          console.log('Loaded responses from assets:', responses.length);
          console.log('Sample responses:', responses.slice(0, 3));

          // Add some fallback responses if we didn't get any
          if (responses.length === 0) {
            console.warn('No responses loaded from assets, adding fallback responses');
            responses = [
              "A stray pube.",
              "The entire Mormon Tabernacle Choir.",
              "A lifetime of sadness.",
              "The forbidden fruit.",
              "The three-word sentence no one dare say.",
              "The color pink.",
              "The magic condom.",
              "The magic flute.",
              "The magic roundabout.",
              "Farting and walking away."
            ];
          }

          // Store in local storage for future use
          this.cacheResponses(responses);
        }),
        catchError(error => {
          console.error('Error loading responses:', error);

          // Provide fallback responses in case of error
          const fallbackResponses = [
            "A stray pube.",
            "The entire Mormon Tabernacle Choir.",
            "A lifetime of sadness.",
            "The forbidden fruit.",
            "The three-word sentence no one dare say.",
            "The color pink.",
            "The magic condom.",
            "The magic flute.",
            "The magic roundabout.",
            "Farting and walking away."
          ];

          console.log('Using fallback responses');
          return of(fallbackResponses);
        })
      )
      .subscribe(responses => {
        this.responsesSubject.next(responses);
      });
  }

  /**
   * Cache responses in local storage
   */
  private cacheResponses(responses: string[]): void {
    this.cachedResponses = responses;
    try {
      localStorage.setItem(this.localStorageKey, JSON.stringify(responses));
      console.log('Cached responses in local storage');
    } catch (error) {
      console.error('Error caching responses:', error);
    }
  }

  /**
   * Get a random set of unique responses
   */
  getRandomResponses(count: number): string[] {
    if (this.cachedResponses.length === 0) {
      console.warn('No responses available, using fallback responses');
      // Provide fallback responses
      const fallbackResponses = [
        "A stray pube.",
        "The entire Mormon Tabernacle Choir.",
        "A lifetime of sadness.",
        "The forbidden fruit.",
        "The three-word sentence no one dare say.",
        "The color pink.",
        "The magic condom.",
        "The magic flute.",
        "The magic roundabout.",
        "Farting and walking away."
      ];

      // Cache these responses for future use
      this.cacheResponses(fallbackResponses);

      // Return a random subset of the fallback responses
      return this.getRandomSubset(fallbackResponses, count);
    }

    // If we have fewer cached responses than requested, we'll need to allow duplicates
    if (this.cachedResponses.length < count) {
      console.warn(`Only ${this.cachedResponses.length} responses available, but ${count} requested. Will include duplicates.`);
      return this.getRandomWithDuplicates(this.cachedResponses, count);
    }

    // Make a copy of the cached responses to avoid modifying the original
    return this.getRandomSubset(this.cachedResponses, count);
  }

  /**
   * Get a random subset of unique items from an array
   */
  private getRandomSubset(items: string[], count: number): string[] {
    // Make a copy of the items to avoid modifying the original
    const availableItems = [...items];
    const selectedItems: string[] = [];

    // Select random items
    while (selectedItems.length < count && availableItems.length > 0) {
      const randomIndex = Math.floor(Math.random() * availableItems.length);
      const item = availableItems.splice(randomIndex, 1)[0];
      selectedItems.push(item);
    }

    console.log(`Selected ${selectedItems.length} random items from ${items.length} available items`);
    return selectedItems;
  }

  /**
   * Get random items from an array, allowing duplicates if necessary
   */
  private getRandomWithDuplicates(items: string[], count: number): string[] {
    const selectedItems: string[] = [];

    // First, add all available items
    selectedItems.push(...items);

    // Then, if we need more, add random duplicates
    while (selectedItems.length < count) {
      const randomIndex = Math.floor(Math.random() * items.length);
      selectedItems.push(items[randomIndex]);
    }

    // Shuffle the array to mix original and duplicate items
    this.shuffleArray(selectedItems);

    console.log(`Selected ${selectedItems.length} items (with duplicates) from ${items.length} available items`);
    return selectedItems;
  }

  /**
   * Shuffle an array in place using Fisher-Yates algorithm
   */
  private shuffleArray(array: any[]): void {
    for (let i = array.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [array[i], array[j]] = [array[j], array[i]];
    }
  }

  /**
   * Replace a used response with a new one
   */
  replaceResponse(usedResponse: string, currentResponses: string[]): string {
    if (this.cachedResponses.length === 0) {
      console.warn('No responses available for replacement, using fallback');
      // Use a fallback response
      const fallbackResponses = [
        "A stray pube.",
        "The entire Mormon Tabernacle Choir.",
        "A lifetime of sadness.",
        "The forbidden fruit.",
        "The three-word sentence no one dare say.",
        "The color pink.",
        "The magic condom.",
        "The magic flute.",
        "The magic roundabout.",
        "Farting and walking away."
      ];

      // Find a fallback response that's not already in the player's hand
      const availableFallbacks = fallbackResponses.filter(
        response => !currentResponses.includes(response)
      );

      if (availableFallbacks.length > 0) {
        const randomIndex = Math.floor(Math.random() * availableFallbacks.length);
        const newResponse = availableFallbacks[randomIndex];
        console.log(`Using fallback response: "${newResponse}"`);
        return newResponse;
      }

      // If all fallbacks are already in hand, just return a random one
      const randomIndex = Math.floor(Math.random() * fallbackResponses.length);
      return fallbackResponses[randomIndex];
    }

    // Find responses that aren't already in the player's hand
    const availableResponses = this.cachedResponses.filter(
      response => !currentResponses.includes(response)
    );

    if (availableResponses.length === 0) {
      console.warn('No unique responses available for replacement, using random response');
      // Just pick a random response from all cached responses
      const randomIndex = Math.floor(Math.random() * this.cachedResponses.length);
      const newResponse = this.cachedResponses[randomIndex];
      console.log(`Replaced response "${usedResponse}" with random response "${newResponse}"`);
      return newResponse;
    }

    // Select a random response from the available ones
    const randomIndex = Math.floor(Math.random() * availableResponses.length);
    const newResponse = availableResponses[randomIndex];

    console.log(`Replaced response "${usedResponse}" with "${newResponse}"`);
    return newResponse;
  }
}
