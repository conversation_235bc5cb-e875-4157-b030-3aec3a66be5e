extends Control

# References
@onready var host_button = $VBoxContainer/HostButton
@onready var join_button = $VBoxContainer/Join<PERSON>ontaine<PERSON>/<PERSON><PERSON><PERSON><PERSON><PERSON>
@onready var back_button = $VBoxContainer/BackButton
@onready var code_input = $VBoxContainer/JoinContainer/CodeInput
@onready var name_input = $VBoxContainer/JoinContainer/NameInput

# Firebase and multiplayer references
var firebase_manager
var multiplayer_manager

func _ready():
	# Get references
	firebase_manager = get_node_or_null("/root/FirebaseManager")
	multiplayer_manager = get_node_or_null("/root/MultiplayerManager")

	if not firebase_manager or not multiplayer_manager:
		push_error("FirebaseManager or MultiplayerManager not found")
		host_button.disabled = true
		join_button.disabled = true
		return

	# Connect signals
	host_button.pressed.connect(_on_host_button_pressed)
	join_button.pressed.connect(_on_join_button_pressed)
	back_button.pressed.connect(_on_back_button_pressed)

	# Set up input validation
	code_input.text_changed.connect(_validate_inputs)
	name_input.text_changed.connect(_validate_inputs)

	# Initial validation
	_validate_inputs()

func _validate_inputs(_text = ""):
	var code_valid = code_input.text.length() == 4
	var name_valid = name_input.text.length() >= 2

	join_button.disabled = not (code_valid and name_valid)

func _on_host_button_pressed():
	# Change to the card game scene
	get_tree().change_scene_to_file("res://cardGame/cardGame.tscn")

func _on_join_button_pressed():
	# TODO: Implement joining a game via the web interface
	# For now, just show a message
	var dialog = AcceptDialog.new()
	dialog.title = "Web Interface Required"
	dialog.dialog_text = "To join a game, use the web interface on your phone.\n\nRoom Code: " + code_input.text.to_upper() + "\nName: " + name_input.text
	add_child(dialog)
	dialog.popup_centered()

func _on_back_button_pressed():
	# Go back to the main menu
	get_tree().change_scene_to_file("res://menu.tscn")
