extends Control

func _ready():
	#Autofocus on first button for controller support
	$VBoxContainer/truthOrTRUTH.grab_focus()

	#Button Signals
	$VBoxContainer/truthOrTRUTH.connect("pressed", _on_truth_pressed)
	$VBoxContainer/untitled.connect("pressed", _on_untitled_pressed)
	$VBoxContainer/floridaMan.connect("pressed", _on_florida_Pressed)
	$VBoxContainer/multiplayerButton.connect("pressed", _on_multiplayer_pressed)

func _on_truth_pressed():
	get_tree().change_scene_to_file("res://truth_or_truth!.tscn")

func _on_untitled_pressed():
	get_tree().change_scene_to_file("res://cardGame/cardGame.tscn")

func _on_florida_Pressed():
	get_tree().change_scene_to_file("res://floridaman.tscn")

func _on_multiplayer_pressed():
	get_tree().change_scene_to_file("res://cardGame/multiplayer_menu.tscn")

#func _on_quit_pressed():
	#get_tree().quit()  # Quits the game
