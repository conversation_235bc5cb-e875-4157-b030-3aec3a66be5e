[gd_scene load_steps=3 format=3 uid="uid://cffd5lfcyvpbb"]

[ext_resource type="Script" path="res://cardGame/room_display_new.gd" id="1_yvqm8"]
[ext_resource type="Script" path="res://addons/qr_code/qr_code_rect.gd" id="2_yvqm8"]

[node name="RoomDisplay" type="Control"]
script = ExtResource("1_yvqm8")
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="BackgroundPanel" type="Panel" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -400.0
offset_top = -400.0
offset_right = 400.0
offset_bottom = 400.0
grow_horizontal = 2
grow_vertical = 2

[node name="BackgroundColor" type="ColorRect" parent="BackgroundPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0.2, 0.2, 0.2, 1)

[node name="ContentPanel" type="Panel" parent="BackgroundPanel"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -250.0
offset_top = -250.0
offset_right = 250.0
offset_bottom = 250.0
grow_horizontal = 2
grow_vertical = 2

[node name="ContentColor" type="ColorRect" parent="BackgroundPanel/ContentPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0.25, 0.25, 0.25, 1)

[node name="MarginContainer" type="MarginContainer" parent="BackgroundPanel/ContentPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_constants/margin_left = 40
theme_override_constants/margin_top = 40
theme_override_constants/margin_right = 40
theme_override_constants/margin_bottom = 40

[node name="VBoxContainer" type="VBoxContainer" parent="BackgroundPanel/ContentPanel/MarginContainer"]
layout_mode = 2
theme_override_constants/separation = 20
alignment = 1

[node name="TitleLabel" type="Label" parent="BackgroundPanel/ContentPanel/MarginContainer/VBoxContainer"]
layout_mode = 2
theme_override_colors/font_color = Color(1, 1, 1, 1)
theme_override_font_sizes/font_size = 24
text = "Room Code"
horizontal_alignment = 1

[node name="CodeLabel" type="Label" parent="BackgroundPanel/ContentPanel/MarginContainer/VBoxContainer"]
layout_mode = 2
theme_override_colors/font_color = Color(1, 1, 1, 1)
theme_override_font_sizes/font_size = 48
text = "ABCD"
horizontal_alignment = 1

[node name="QRCodeRect" type="TextureRect" parent="BackgroundPanel/ContentPanel/MarginContainer/VBoxContainer"]
custom_minimum_size = Vector2(200, 200)
layout_mode = 2
size_flags_horizontal = 4
stretch_mode = 5
script = ExtResource("2_yvqm8")
error_correction = 2
module_px_size = 8
quiet_zone_size = 4
auto_version = true

[node name="InstructionsLabel" type="Label" parent="BackgroundPanel/ContentPanel/MarginContainer/VBoxContainer"]
layout_mode = 2
theme_override_colors/font_color = Color(1, 1, 1, 1)
theme_override_font_sizes/font_size = 18
text = "Scan this QR code with your phone camera
or go to http://example.com and enter this code"
horizontal_alignment = 1
