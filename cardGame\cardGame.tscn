[gd_scene load_steps=3 format=3 uid="uid://dxjh7tgl0g78r"]

[ext_resource type="Script" uid="uid://ccfa64pd06d38" path="res://cardGame/cardGame.gd" id="1_ge1j1"]
[ext_resource type="PackedScene" uid="uid://c8q5y6x4j7n2p" path="res://cardGame/room_display.tscn" id="2_yvqm8"]

[node name="cardGame" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_ge1j1")

[node name="ColorRect" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0, 0, 0, 1)

[node name="Title" type="Label" parent="."]
layout_mode = 0
offset_left = 13.0
offset_top = 50.0
offset_right = 166.0
offset_bottom = 73.0
theme_override_font_sizes/font_size = 28
text = "Untitled Card Game"

[node name="JudgeAsksLabel" type="Label" parent="."]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -400.0
offset_top = 70.0
offset_right = 400.0
offset_bottom = 100.0
grow_horizontal = 2
theme_override_font_sizes/font_size = 20
horizontal_alignment = 1
vertical_alignment = 1

[node name="CurrentPromptDisplay" type="Label" parent="."]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -400.0
offset_top = 100.0
offset_right = 400.0
offset_bottom = 150.0
grow_horizontal = 2
theme_override_font_sizes/font_size = 24
horizontal_alignment = 1
vertical_alignment = 1
autowrap_mode = 3

[node name="Prompt" type="Label" parent="CurrentPromptDisplay"]
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -34.0
offset_top = 370.0
offset_right = 33.0
offset_bottom = 393.0
grow_horizontal = 2
grow_vertical = 0
theme_override_colors/font_color = Color(1, 1, 1, 1)
theme_override_colors/font_outline_color = Color(0.169245, 0.169245, 0.169245, 1)
text = "Prompt: "

[node name="Judge" type="Label" parent="."]
visible = false
layout_mode = 0
offset_left = 18.0
offset_top = 92.0
offset_right = 66.0
offset_bottom = 115.0
text = "Judge:"

[node name="HBoxContainer" type="HBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 10
anchor_right = 1.0
offset_top = 180.0
offset_bottom = 680.0
grow_horizontal = 2
theme_override_constants/separation = 20
alignment = 1

[node name="HBoxContainer2" type="HBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 12
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_top = -200.0
grow_horizontal = 2
grow_vertical = 0
theme_override_constants/separation = 10

[node name="Timer" type="Timer" parent="."]

[node name="Label" type="Label" parent="Timer"]
offset_left = 15.0
offset_top = 138.0
offset_right = 101.0
offset_bottom = 161.0
text = "Time left: 0"

[node name="TextureRect" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -400.0
offset_top = -100.0
offset_right = 400.0
offset_bottom = 100.0
grow_horizontal = 2
grow_vertical = 2
theme_override_constants/separation = 20

[node name="RoomCodeDisplay" parent="." instance=ExtResource("2_yvqm8")]
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
offset_left = -350.0
offset_top = -450.0
grow_horizontal = 0
grow_vertical = 0

[node name="WinningResponseDisplay" type="Panel" parent="."]
visible = false
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -300.0
offset_top = -150.0
offset_right = 300.0
offset_bottom = 150.0
grow_horizontal = 2
grow_vertical = 2

[node name="VBoxContainer" type="VBoxContainer" parent="WinningResponseDisplay"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_constants/separation = 20
alignment = 1

[node name="PlayerNameLabel" type="Label" parent="WinningResponseDisplay/VBoxContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 28
text = "Player Name"
horizontal_alignment = 1

[node name="ResponseText" type="Label" parent="WinningResponseDisplay/VBoxContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 32
text = "Winning Response Text"
horizontal_alignment = 1
autowrap_mode = 3
