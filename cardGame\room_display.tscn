[gd_scene load_steps=3 format=3 uid="uid://c8q5y6x4j7n2p"]

[ext_resource type="Script" path="res://cardGame/room_display.gd" id="1_yvqm8"]
[ext_resource type="Script" path="res://addons/qr_code/qr_code_rect.gd" id="2_yvqm8"]

[node name="RoomDisplay" type="Control"]
script = ExtResource("1_yvqm8")
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="OuterPanel" type="Panel" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="OuterColorRect" type="ColorRect" parent="OuterPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0.2, 0.2, 0.2, 1)

[node name="MarginContainer" type="MarginContainer" parent="OuterPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_constants/margin_left = 20
theme_override_constants/margin_top = 20
theme_override_constants/margin_right = 20
theme_override_constants/margin_bottom = 20

[node name="VBoxContainer" type="VBoxContainer" parent="OuterPanel/MarginContainer"]
layout_mode = 2
theme_override_constants/separation = 15
alignment = 1

[node name="TitleLabel" type="Label" parent="OuterPanel/MarginContainer/VBoxContainer"]
layout_mode = 2
theme_override_colors/font_color = Color(1, 1, 1, 1)
theme_override_font_sizes/font_size = 20
text = "Room Code"
horizontal_alignment = 1

[node name="CodeLabel" type="Label" parent="OuterPanel/MarginContainer/VBoxContainer"]
layout_mode = 2
theme_override_colors/font_color = Color(1, 1, 1, 1)
theme_override_font_sizes/font_size = 36
text = "ABCD"
horizontal_alignment = 1

[node name="QRCodeRect" type="TextureRect" parent="OuterPanel/MarginContainer/VBoxContainer"]
custom_minimum_size = Vector2(180, 180)
layout_mode = 2
size_flags_horizontal = 4
stretch_mode = 5
script = ExtResource("2_yvqm8")
error_correction = 2
module_px_size = 8
quiet_zone_size = 4
auto_version = true

[node name="InstructionsLabel" type="Label" parent="OuterPanel/MarginContainer/VBoxContainer"]
layout_mode = 2
theme_override_colors/font_color = Color(1, 1, 1, 1)
theme_override_font_sizes/font_size = 14
text = "Scan this QR code with your phone camera
or go to http://example.com and enter this code"
horizontal_alignment = 1
