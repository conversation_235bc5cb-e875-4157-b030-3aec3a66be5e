[gd_scene format=3 uid="uid://cli1k4xh5apo7"]

[node name="Panel" type="Panel"]
custom_minimum_size = Vector2(350, 500)
offset_right = 350.0
offset_bottom = 500.0
size_flags_horizontal = 3
size_flags_vertical = 3

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="NameLabel" type="Label" parent="VBoxContainer"]
layout_mode = 2
text = "Player Name"
horizontal_alignment = 1

[node name="ScoreLabel" type="Label" parent="VBoxContainer"]
layout_mode = 2
text = "Score: 0"
horizontal_alignment = 1

[node name="JudgeIndicator" type="Label" parent="VBoxContainer"]
layout_mode = 2
text = "JUDGE"
horizontal_alignment = 1
visible = false

[node name="HandContainer" type="VBoxContainer" parent="VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3
theme_override_constants/separation = 10
